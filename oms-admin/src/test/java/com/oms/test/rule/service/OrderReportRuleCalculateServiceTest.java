package com.oms.test.rule.service;

import com.oms.rulev2.service.OrderReportRuleCalculateService;
import com.oms.rulev2.service.request.OrderReportMatchRequest;
import com.oms.rulev2.service.request.param.OrderReportMatchLineParam;
import com.oms.rulev2.service.response.RuleFinalResult;
import com.oms.rulev2.service.response.info.ReportOrderMatchInfo;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.ArrayList;
import java.util.List;

@DisplayName("规则测试案例")
@SpringBootTest
@ActiveProfiles("local")
@Slf4j
public class OrderReportRuleCalculateServiceTest {
    @Autowired
    private OrderReportRuleCalculateService orderReportRuleCalculateService;

    @Test
    public void test() {
        OrderReportMatchRequest request = new OrderReportMatchRequest();
        request.setTenantId(1L);
        request.setPrjId(1L);
        request.setOutOrderNo("123");
        List<OrderReportMatchLineParam> orderLines = new ArrayList<>();
        OrderReportMatchLineParam param = new OrderReportMatchLineParam();
        param.setStartQuantity(1);
        param.setSkuId(321L);
        orderLines.add(param);
        request.setOrderLines(orderLines);
        final RuleFinalResult<List<ReportOrderMatchInfo>> result = orderReportRuleCalculateService.calculation(request);
        log.info("result： {}", result);
    }
}
