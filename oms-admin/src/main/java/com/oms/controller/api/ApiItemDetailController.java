package com.oms.controller.api;

import java.util.List;
import java.util.Arrays;

import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.oms.common.annotation.RepeatSubmit;
import com.oms.common.annotation.Log;
import com.oms.common.core.controller.BaseController;
import com.oms.common.core.domain.PageQuery;
import com.oms.common.core.domain.R;
import com.oms.common.core.validate.AddGroup;
import com.oms.common.core.validate.EditGroup;
import com.oms.common.enums.BusinessType;
import com.oms.common.utils.poi.ExcelUtil;
import com.oms.domain.vo.ApiItemDetailVo;
import com.oms.domain.bo.ApiItemDetailBo;
import com.oms.service.IApiItemDetailService;
import com.oms.common.core.page.TableDataInfo;

/**
 * api商品详情
 *
 * <AUTHOR>
 * @date 2025-05-07
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/itemDetail")
public class ApiItemDetailController extends BaseController {

    private final IApiItemDetailService iApiItemDetailService;

    /**
     * 查询api商品详情列表
     */
    @SaCheckPermission("oms:itemDetail:list")
    @GetMapping("/list")
    public TableDataInfo<ApiItemDetailVo> list(ApiItemDetailBo bo, PageQuery pageQuery) {
        return iApiItemDetailService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出api商品详情列表
     */
    @SaCheckPermission("oms:itemDetail:export")
    @Log(title = "导出api商品详情", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(ApiItemDetailBo bo, HttpServletResponse response) {
        List<ApiItemDetailVo> list = iApiItemDetailService.queryList(bo);
        ExcelUtil.exportExcel(list, "api商品详情", ApiItemDetailVo.class, response);
    }

    /**
     * 获取api商品详情详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("oms:itemDetail:query")
    @GetMapping("/{id}")
    public R<ApiItemDetailVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iApiItemDetailService.queryById(id));
    }

    /**
     * 新增api商品详情
     */
    @SaCheckPermission("oms:itemDetail:add")
    @Log(title = "新增api商品详情", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ApiItemDetailBo bo) {
        return toAjax(iApiItemDetailService.insertByBo(bo));
    }

    /**
     * 修改api商品详情
     */
    @SaCheckPermission("oms:itemDetail:edit")
    @Log(title = "修改api商品详情", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ApiItemDetailBo bo) {
        return toAjax(iApiItemDetailService.updateByBo(bo));
    }

    /**
     * 删除api商品详情
     *
     * @param ids 主键串
     */
    @SaCheckPermission("oms:itemDetail:remove")
    @Log(title = "删除api商品详情", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotNull(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iApiItemDetailService.deleteWithValidByIds(Arrays.asList(ids), true));
    }
}
