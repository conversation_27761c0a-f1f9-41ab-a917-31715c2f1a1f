package com.oms.controller.api;

import java.util.List;
import java.util.Arrays;

import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.oms.common.annotation.RepeatSubmit;
import com.oms.common.annotation.Log;
import com.oms.common.core.controller.BaseController;
import com.oms.common.core.domain.PageQuery;
import com.oms.common.core.domain.R;
import com.oms.common.core.validate.AddGroup;
import com.oms.common.core.validate.EditGroup;
import com.oms.common.enums.BusinessType;
import com.oms.common.utils.poi.ExcelUtil;
import com.oms.domain.vo.ApiSkuExtendsAttrVo;
import com.oms.domain.bo.ApiSkuExtendsAttrBo;
import com.oms.service.IApiSkuExtendsAttrService;
import com.oms.common.core.page.TableDataInfo;

/**
 * api商品属性拓展
 *
 * <AUTHOR>
 * @date 2025-05-07
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/skuExtendsAttr")
public class ApiSkuExtendsAttrController extends BaseController {

    private final IApiSkuExtendsAttrService iApiSkuExtendsAttrService;

    /**
     * 查询api商品属性拓展列表
     */
    @SaCheckPermission("oms:skuExtendsAttr:list")
    @GetMapping("/list")
    public TableDataInfo<ApiSkuExtendsAttrVo> list(ApiSkuExtendsAttrBo bo, PageQuery pageQuery) {
        return iApiSkuExtendsAttrService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出api商品属性拓展列表
     */
    @SaCheckPermission("oms:skuExtendsAttr:export")
    @Log(title = "导出api商品属性拓展", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(ApiSkuExtendsAttrBo bo, HttpServletResponse response) {
        List<ApiSkuExtendsAttrVo> list = iApiSkuExtendsAttrService.queryList(bo);
        ExcelUtil.exportExcel(list, "api商品属性拓展", ApiSkuExtendsAttrVo.class, response);
    }

    /**
     * 获取api商品属性拓展详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("oms:skuExtendsAttr:query")
    @GetMapping("/{id}")
    public R<ApiSkuExtendsAttrVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iApiSkuExtendsAttrService.queryById(id));
    }

    /**
     * 新增api商品属性拓展
     */
    @SaCheckPermission("oms:skuExtendsAttr:add")
    @Log(title = "新增api商品属性拓展", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ApiSkuExtendsAttrBo bo) {
        return toAjax(iApiSkuExtendsAttrService.insertByBo(bo));
    }

    /**
     * 修改api商品属性拓展
     */
    @SaCheckPermission("oms:skuExtendsAttr:edit")
    @Log(title = "修改api商品属性拓展", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ApiSkuExtendsAttrBo bo) {
        return toAjax(iApiSkuExtendsAttrService.updateByBo(bo));
    }

    /**
     * 删除api商品属性拓展
     *
     * @param ids 主键串
     */
    @SaCheckPermission("oms:skuExtendsAttr:remove")
    @Log(title = "删除api商品属性拓展", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotNull(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iApiSkuExtendsAttrService.deleteWithValidByIds(Arrays.asList(ids), true));
    }
}
