package com.oms.controller.api;

import java.util.List;
import java.util.Arrays;

import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.oms.common.annotation.RepeatSubmit;
import com.oms.common.annotation.Log;
import com.oms.common.core.controller.BaseController;
import com.oms.common.core.domain.PageQuery;
import com.oms.common.core.domain.R;
import com.oms.common.core.validate.AddGroup;
import com.oms.common.core.validate.EditGroup;
import com.oms.common.enums.BusinessType;
import com.oms.common.utils.poi.ExcelUtil;
import com.oms.domain.vo.ApiOutAttributeValueVo;
import com.oms.domain.bo.ApiOutAttributeValueBo;
import com.oms.service.IApiOutAttributeValueService;
import com.oms.common.core.page.TableDataInfo;

/**
 * api外部属性值
 *
 * <AUTHOR>
 * @date 2025-05-07
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/outAttributeValue")
public class ApiOutAttributeValueController extends BaseController {

    private final IApiOutAttributeValueService iApiOutAttributeValueService;

    /**
     * 查询api外部属性值列表
     */
    @SaCheckPermission("oms:outAttributeValue:list")
    @GetMapping("/list")
    public TableDataInfo<ApiOutAttributeValueVo> list(ApiOutAttributeValueBo bo, PageQuery pageQuery) {
        return iApiOutAttributeValueService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出api外部属性值列表
     */
    @SaCheckPermission("oms:outAttributeValue:export")
    @Log(title = "导出api外部属性值", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(ApiOutAttributeValueBo bo, HttpServletResponse response) {
        List<ApiOutAttributeValueVo> list = iApiOutAttributeValueService.queryList(bo);
        ExcelUtil.exportExcel(list, "api外部属性值", ApiOutAttributeValueVo.class, response);
    }

    /**
     * 获取api外部属性值详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("oms:outAttributeValue:query")
    @GetMapping("/{id}")
    public R<ApiOutAttributeValueVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iApiOutAttributeValueService.queryById(id));
    }

    /**
     * 新增api外部属性值
     */
    @SaCheckPermission("oms:outAttributeValue:add")
    @Log(title = "新增api外部属性值", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ApiOutAttributeValueBo bo) {
        return toAjax(iApiOutAttributeValueService.insertByBo(bo));
    }

    /**
     * 修改api外部属性值
     */
    @SaCheckPermission("oms:outAttributeValue:edit")
    @Log(title = "修改api外部属性值", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ApiOutAttributeValueBo bo) {
        return toAjax(iApiOutAttributeValueService.updateByBo(bo));
    }

    /**
     * 删除api外部属性值
     *
     * @param ids 主键串
     */
    @SaCheckPermission("oms:outAttributeValue:remove")
    @Log(title = "删除api外部属性值", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotNull(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iApiOutAttributeValueService.deleteWithValidByIds(Arrays.asList(ids), true));
    }
}
