package com.oms.controller.admin.stlmt;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.google.api.client.repackaged.com.google.common.base.Throwables;
import com.oms.common.annotation.Log;
import com.oms.common.annotation.RepeatSubmit;
import com.oms.common.core.controller.BaseController;
import com.oms.common.core.domain.R;
import com.oms.common.core.page.TableDataInfo;
import com.oms.common.core.validate.AddGroup;
import com.oms.common.core.validate.EditGroup;
import com.oms.common.enums.BusinessType;
import com.oms.common.enums.StlmtInvoiceStatusEnum;
import com.oms.common.exception.ServiceException;
import com.oms.common.utils.poi.ExcelUtil;
import com.oms.domain.bo.StlmtCustomerBillBo;
import com.oms.domain.vo.StlmtCustomerBillVo;
import com.oms.service.IStlmtCustomerBillService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 客户对账单
 *
 * <AUTHOR>
 * @date 2025-02-04
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/oms/stlmt/customerBill")
public class StlmtCustomerBillController extends BaseController {

    private final IStlmtCustomerBillService iStlmtCustomerBillService;

    /**
     * 查询客户对账单列表
     */
    @SaCheckPermission("oms:customerBill:list")
    @GetMapping("/list")
    public TableDataInfo<StlmtCustomerBillVo> list(StlmtCustomerBillBo bo) {
        return iStlmtCustomerBillService.queryPageList(bo);
    }


    /**
     * 查询客户对账单列表(选择客户对账单)
     */
    @SaCheckPermission("oms:customerBill:csr:list")
    @GetMapping("/csr/list")
    public TableDataInfo<StlmtCustomerBillVo> csrlist(StlmtCustomerBillBo bo) {
        //需要按照项目 跟 客户进行数据过滤
        if(null == bo.getProjectId() || null == bo.getDeliverCustomerCode()){
            throw new ServiceException("项目id或客户Id不能为空");
        }
        bo.setInvoiceCustomerId(Long.valueOf(bo.getDeliverCustomerCode()));
        bo.setDeliverCustomerCode(null);
        bo.setInvoiceStatus(StlmtInvoiceStatusEnum.UN_INVOICE.getKey());
        //审核通过的
        bo.setStatus(2);
        return iStlmtCustomerBillService.queryPageList(bo);
    }

    /**
     * 导出客户对账单列表
     */
    @SaCheckPermission("oms:customerBill:export")
    @Log(title = "导出客户对账单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(StlmtCustomerBillBo bo, HttpServletResponse response) {
        List<StlmtCustomerBillVo> list = iStlmtCustomerBillService.queryList(bo);
        ExcelUtil.exportExcel(list, "客户对账单", StlmtCustomerBillVo.class, response);
    }

    /**
     * 新增客户对账单
     */
    @SaCheckPermission("oms:customerBill:add")
    @Log(title = "新增客户对账单", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/add")
    public R<Void> add(@Validated(AddGroup.class) @RequestBody StlmtCustomerBillBo bo) {
        return toAjax(iStlmtCustomerBillService.insertByBo(bo));
    }

    /**
     * 修改客户对账单
     */
    @SaCheckPermission("oms:customerBill:edit")
    @Log(title = "修改客户对账单", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("/edit")
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody StlmtCustomerBillBo bo) {
        return toAjax(iStlmtCustomerBillService.updateByBo(bo));
    }


    /**
     * 提交客户对账单
     */
    @SaCheckPermission("oms:customerBill:submit")
    @Log(title = "提交客户对账单")
    @RepeatSubmit()
    @PutMapping("/submit/{ids}")
    public R<Void> submit(@NotNull(message = "主键不能为空") @PathVariable Long[] ids) {
        try {
            return toAjax(iStlmtCustomerBillService.submit(Arrays.asList(ids)));
        }catch (ServiceException e){
            return R.fail("提交客户对账单失败:" + e.getMessage());
        }catch (Exception e){
            log.error("提交客户对账单 param:{}, ,case:{}", ids, Throwables.getStackTraceAsString(e));
            return R.fail("提交客户对账单失败");
        }
    }

    /**
     * 审核
     *
     * @param
     */
    @SaCheckPermission("oms:customerBill:audit")
    @Log(title = "审核客户对账单", businessType = BusinessType.UPDATE)
    @PostMapping("/audit")
    public R<Void> audit(@RequestParam(value = "id") Long id, @RequestParam(value = "auditStatus") Integer auditStatus,
                         @RequestParam(value = "auditMsg", required = false) String auditMsg) {
        try {
            return toAjax(iStlmtCustomerBillService.audit(id, auditStatus, auditMsg));
        }catch (ServiceException e){
            return R.fail("审核客户对账单失败:" + e.getMessage());
        }catch (Exception e){
            log.error("审核客户对账单 param:{}, {}, {},case:{}", id, auditStatus, auditMsg, Throwables.getStackTraceAsString(e));
            return R.fail("审核客户对账单失败");
        }
    }

    /**
     * 删除客户对账单
     *
     * @param ids 主键串
     */
    @SaCheckPermission("oms:customerBill:remove")
    @Log(title = "删除客户对账单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotNull(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iStlmtCustomerBillService.deleteWithValidByIds(Arrays.asList(ids), true));
    }

    /**
     * 获取客户对账单详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("oms:customerBill:query")
    @GetMapping("/{id}")
    public R<StlmtCustomerBillVo> getInfo(@NotNull(message = "主键不能为空")
                                          @PathVariable Long id) {
        return R.ok(iStlmtCustomerBillService.queryById(id));
    }
}
