package com.oms.controller.admin.goods;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.alibaba.fastjson.JSON;
import com.google.api.client.repackaged.com.google.common.base.Throwables;
import com.oms.common.core.controller.BaseController;
import com.oms.common.core.domain.R;
import com.oms.common.core.page.TableDataInfo;
import com.oms.common.exception.ServiceException;
import com.oms.domain.bo.GoodsAttributeBo;
import com.oms.domain.vo.GoodsAttributeVo;
import com.oms.service.IGoodsAttributeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * 商品属性
 *
 * <AUTHOR>
 * @date 2024-07-22
 */
@Validated
@RequiredArgsConstructor
@RestController
@Slf4j
@RequestMapping("/oms/goodsAttribute")
public class GoodsAttributeController extends BaseController {

    private final IGoodsAttributeService iGoodsAttributeService;

    /**
     * 查询商品属性列表
     */
    @SaCheckPermission("oms:attribute:queryPageList")
    @PostMapping("/queryPageList")
    public TableDataInfo<GoodsAttributeVo> queryPageList(GoodsAttributeBo bo) {
        bo.setTenantId(1L);
        return iGoodsAttributeService.queryPageList(bo);
    }

    /**
     * 查询商品属性列表
     */
    @SaCheckPermission("oms:attribute:list")
    @GetMapping("/list")
    public R<List<GoodsAttributeVo>> list(GoodsAttributeBo bo) {
        return R.ok(iGoodsAttributeService.queryPageSelectList(bo));
    }
    /**
     * 导出商品属性列表
     */
//	@SaCheckPermission("oms:attribute:export")
	//@ActionLog(module = ModuleType.GOODS, title = "导出商品属性列表", businessType = BusinessType.EXPORT)
	//@PostMapping("/export")
	//public void export(GoodsAttributeBo bo, HttpServletResponse response) {
	//	List<GoodsAttributeVo> list = iGoodsAttributeService.queryList(bo);
	//	ExcelUtil.exportExcel(list, "商品属性", GoodsAttributeVo.class, response);
    //}

    /**
     * 获取商品属性详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("oms:attribute:query")
	@GetMapping("/{id}")
    public R<GoodsAttributeVo> getInfo(@PathVariable(value = "id") Long id) {
        return R.ok(iGoodsAttributeService.queryById(id));
    }

    /**
     * 新增商品属性
     */
	@SaCheckPermission("oms:attribute:add")
    @PostMapping()
    public R<Void> add(@RequestBody GoodsAttributeBo bo) {
        try {
            bo.setTenantId(1L);
            return toAjax(iGoodsAttributeService.insertByBo(bo) ? 1 : 0);
        }catch (ServiceException e){
            return R.fail(e.getMessage());
        }catch (Exception e){
            log.error("新增商品属性失败 param:{},case:{}", JSON.toJSONString(bo), Throwables.getStackTraceAsString(e));
            return R.fail("新增商品属性失败！");
        }
    }

    /**
     * 修改商品属性
     */
	@SaCheckPermission("oms:attribute:edit")
    @PutMapping()
    public R<Void> edit(@RequestBody GoodsAttributeBo bo) {
        try {
            return toAjax(iGoodsAttributeService.updateByBo(bo) ? 1 : 0);
        }catch (ServiceException e){
            return R.fail(e.getMessage());
        }catch (Exception e){
            log.error("修改商品属性失败 param:{},case:{}", JSON.toJSONString(bo), Throwables.getStackTraceAsString(e));
            return R.fail("修改商品属性失败！");
        }
    }

    /**
     * 删除商品属性
     *
     * @param ids 主键串
     */
	@SaCheckPermission("oms:attribute:remove")
    @DeleteMapping("/{ids}")
    public R<Void> remove(@PathVariable(value = "ids") Long[] ids) {
        return toAjax(iGoodsAttributeService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
