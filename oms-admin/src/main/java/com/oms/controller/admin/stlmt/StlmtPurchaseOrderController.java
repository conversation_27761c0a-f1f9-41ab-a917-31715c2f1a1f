package com.oms.controller.admin.stlmt;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaIgnore;
import cn.hutool.core.bean.BeanUtil;
import com.oms.common.annotation.Log;
import com.oms.common.annotation.RepeatSubmit;
import com.oms.common.core.controller.BaseController;
import com.oms.common.core.domain.PageQuery;
import com.oms.common.core.domain.R;
import com.oms.common.core.page.TableDataInfo;
import com.oms.common.core.validate.AddGroup;
import com.oms.common.enums.BusinessType;
import com.oms.common.utils.poi.ExcelUtil;
import com.oms.domain.bo.StlmtPurchaseOrderBo;
import com.oms.domain.bo.extend.stlmt.StlmtPurchaseOrderToProcessQueryBo;
import com.oms.domain.vo.StlmtPurchaseOrderVo;
import com.oms.service.IStlmtPurchaseOrderService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 结算采购单据
 *
 * <AUTHOR>
 * @date 2025-01-23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/oms/stlmt/purchaseOrder")
public class StlmtPurchaseOrderController extends BaseController {

    private final IStlmtPurchaseOrderService iStlmtPurchaseOrderService;

    /**
     * 查询结算采购单据列表
     */
    @SaCheckPermission("oms:purchaseOrder:list")
    @GetMapping("/list")
    public TableDataInfo<StlmtPurchaseOrderVo> list(StlmtPurchaseOrderBo bo, PageQuery pageQuery) {
        return iStlmtPurchaseOrderService.queryPageList(bo, pageQuery);
    }



    /**
     * 已开销项未开进项列表
     */
    @SaCheckPermission("oms:toInvociePurchaseList:list")
    @GetMapping("/toInvocie/purchase/list")
    @SaIgnore
    public TableDataInfo<StlmtPurchaseOrderVo> queryToInvociePurchaseList(StlmtPurchaseOrderToProcessQueryBo bo, PageQuery pageQuery) {
        StlmtPurchaseOrderBo stlmtPurchaseOrderBo = BeanUtil.copyProperties(bo,StlmtPurchaseOrderBo.class);
        //handleQueryParam(bo,stlmtPurchaseOrderBo);
        //已开销项
        stlmtPurchaseOrderBo.setInvoiceStatus(3L);
        //未开进项
        stlmtPurchaseOrderBo.setVInvoiceStatus(1L);
        return iStlmtPurchaseOrderService.queryToInvociePurchaseList(stlmtPurchaseOrderBo, pageQuery);
    }


    /**
     * 导出结算采购单据列表
     */
    @SaCheckPermission("oms:purchaseOrder:export")
    @Log(title = "结算采购单据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(StlmtPurchaseOrderBo bo, HttpServletResponse response) {
        List<StlmtPurchaseOrderVo> list = iStlmtPurchaseOrderService.queryList(bo);
        ExcelUtil.exportExcel(list, "结算采购单据", StlmtPurchaseOrderVo.class, response);
    }

    /**
     * 获取结算采购单据详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("oms:purchaseOrder:query")
    @GetMapping("/{id}")
    public R<StlmtPurchaseOrderVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iStlmtPurchaseOrderService.queryById(id));
    }


    /**
     * 新增结算采购单据
     */
    @SaCheckPermission("oms:purchaseOrder:add")
    @Log(title = "结算采购单据", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody StlmtPurchaseOrderBo bo) {
        return toAjax(iStlmtPurchaseOrderService.insertByBo(bo));
    }

    /**
     * 修改结算采购单据
     */
    @SaCheckPermission("oms:purchaseOrder:edit")
    @Log(title = "结算采购单据", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@RequestBody StlmtPurchaseOrderBo bo) {
        return toAjax(iStlmtPurchaseOrderService.updateByBo(bo));
    }

    /**
     * 删除结算采购单据
     *
     * @param ids 主键串
     */
    @SaCheckPermission("oms:purchaseOrder:remove")
    @Log(title = "结算采购单据", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iStlmtPurchaseOrderService.deleteWithValidByIds(Arrays.asList(ids), true));
    }


//    private void handleQueryParam(StlmtPurchaseOrderToProcessQueryBo bo,StlmtPurchaseOrderBo queryBo) {
//        if(StringUtils.isNotEmpty(bo.getOutIds())){
//            List<String> ids = Arrays.stream(bo.getOutIds().split(","))
//                    .map(String::valueOf)
//                    .collect(Collectors.toList());
//            queryBo.setOutIds(ids);
//        }
//
//        if(StringUtils.isNotEmpty(bo.getPurchaseOrderIds())){
//            List<Long> ids = Arrays.stream(bo.getPurchaseOrderIds().split(","))
//                    .map(Long::valueOf)
//                    .collect(Collectors.toList());
//            queryBo.setPurchaseOrderIds(ids);
//        }
//
//        if(StringUtils.isNotEmpty(bo.getSellerOrderIds())){
//            List<Long> ids = Arrays.stream(bo.getSellerOrderIds().split(","))
//                    .map(Long::valueOf)
//                    .collect(Collectors.toList());
//            queryBo.setSellerOrderIds(ids);
//        }
//
//        if(StringUtils.isNotEmpty(bo.getPackageOrderIds())){
//            List<Long> ids = Arrays.stream(bo.getPackageOrderIds().split(","))
//                    .map(Long::valueOf)
//                    .collect(Collectors.toList());
//            queryBo.setPackageOrderIds(ids);
//        }
//    }
}
