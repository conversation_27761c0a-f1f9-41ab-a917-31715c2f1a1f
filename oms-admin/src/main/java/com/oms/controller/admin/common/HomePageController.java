package com.oms.controller.admin.common;

import cn.dev33.satoken.annotation.SaIgnore;
import com.oms.common.core.domain.R;
import com.oms.domain.vo.HomeQueryVo;
import com.oms.domain.vo.extend.order.OrderHomeQueryVo;
import com.oms.service.IOrderService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 短信演示案例
 * 请先阅读文档 否则无法使用
 *
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/oms/home")
public class HomePageController {


    private final IOrderService orderService;
    /**
     * 查询首页信息
     */
    @GetMapping("/info")
    public R<HomeQueryVo> queryHomeInfo() {
        HomeQueryVo homeQueryVo = new HomeQueryVo();
        OrderHomeQueryVo orderHomeQueryVo = orderService.queryHomeData(null);
        homeQueryVo.setToDeliveryCount(orderHomeQueryVo.getToDeliveryCount());
        homeQueryVo.setToDeliveredCount(orderHomeQueryVo.getToDeliveredCount());
        homeQueryVo.setDeliveryTimeoutCount(orderHomeQueryVo.getDeliveryTimeoutCount());
        homeQueryVo.setDeliveredTimeoutCount(orderHomeQueryVo.getDeliveredTimeoutCount());
        //调用
        return R.ok(homeQueryVo);
    }

}
