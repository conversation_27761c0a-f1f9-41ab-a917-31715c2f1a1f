package com.oms.controller.admin.vendor;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.json.JSONUtil;
import com.google.api.client.repackaged.com.google.common.base.Throwables;
import com.oms.common.annotation.Log;
import com.oms.common.annotation.RepeatSubmit;
import com.oms.common.core.controller.BaseController;
import com.oms.common.core.domain.R;
import com.oms.common.core.page.TableDataInfo;
import com.oms.common.core.validate.AddGroup;
import com.oms.common.core.validate.EditGroup;
import com.oms.common.enums.BusinessType;
import com.oms.common.enums.VendorContractSignTypeEnum;
import com.oms.common.enums.VendorContractSourceTypeEnum;
import com.oms.common.exception.ServiceException;
import com.oms.common.utils.poi.ExcelUtil;
import com.oms.domain.bo.VendorContractBo;
import com.oms.domain.bo.extend.vendor.VendorAuditBo;
import com.oms.domain.bo.extend.vendor.VendorContractDoubleStampBo;
import com.oms.domain.vo.VendorContractVo;
import com.oms.service.IVendorContractService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 供应商合同
 *
 * <AUTHOR>
 * @date 2024-09-01
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/oms/vendorContract")
public class VendorContractController extends BaseController {

    private final IVendorContractService iVendorContractService;

    /**
     * 查询供应商合同列表
     */
    @SaCheckPermission("oms:contract:list")
    @GetMapping("/list")
    public TableDataInfo<VendorContractVo> list(VendorContractBo bo) {
        return iVendorContractService.queryPageList(bo);
    }

    /**
     * 导出供应商合同列表
     */
    @SaCheckPermission("oms:contract:export")
    @Log(title = "供应商合同", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(VendorContractBo bo, HttpServletResponse response) {
        List<VendorContractVo> list = iVendorContractService.queryList(bo);
        ExcelUtil.exportExcel(list, "供应商合同", VendorContractVo.class, response);
    }

    /**
     * 获取供应商合同详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("oms:contract:query")
    @GetMapping("/{id}")
    public R<VendorContractVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iVendorContractService.queryById(id));
    }

    /**
     * 新建合同签署
     */
    @SaCheckPermission("oms:contract:add")
    @Log(title = "oms新建合同签署", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/add")
    public R<Void> add(@Validated(AddGroup.class) @RequestBody VendorContractBo bo) {
        try {
            bo.setSourceType(VendorContractSourceTypeEnum.OMS.getCode());
            return toAjax(iVendorContractService.insertByBo(bo));
        }catch (ServiceException e){
            return R.fail("oms新建合同签署失败:" + e.getMessage());
        }catch (Exception e){
            log.error("oms新建合同签署 param:{},case:{}", JSONUtil.toJsonStr(bo), Throwables.getStackTraceAsString(e));
            return R.fail("oms新建合同签署失败");
        }
    }


    /**
     * 新建合同签署
     */
    @SaCheckPermission("oms:contract:vendorRegisterCreate")
    @Log(title = "oms创建入驻签署合同", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/vendorRegisterCreate")
    public R<Void> vendorRegisterCreate(@Validated(AddGroup.class) @RequestBody VendorContractBo bo) {
        try {
            bo.setSourceType(VendorContractSourceTypeEnum.VENDOR_REGISTER.getCode());
            bo.setSignType(VendorContractSignTypeEnum.WAITING_SIGNATURE.getCode());
            return toAjax(iVendorContractService.insertByBo(bo));
        }catch (ServiceException e){
            return R.fail("创建入驻签署合同失败:" + e.getMessage());
        }catch (Exception e){
            log.error("创建入驻签署合同 param:{},case:{}", JSONUtil.toJsonStr(bo), Throwables.getStackTraceAsString(e));
            return R.fail("创建入驻签署合同失败");
        }
    }


    @SaCheckPermission("oms:contract:update")
    @Log(title = "oms修改合同签署", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/update")
    public R<Void> update(@Validated(EditGroup.class) @RequestBody VendorContractBo bo) {
        try {
            return toAjax(iVendorContractService.updateByBo(bo));
        }catch (ServiceException e){
            return R.fail("oms修改合同签署失败:" + e.getMessage());
        }catch (Exception e){
            log.error("oms修改合同签署 param:{},case:{}", JSONUtil.toJsonStr(bo), Throwables.getStackTraceAsString(e));
            return R.fail("oms修改合同签署失败");
        }
    }


    @SaCheckPermission("oms:contract:auditStampContract")
    @Log(title = "oms审核盖章合同", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/auditStampContract")
    public R<Void> auditStampContract(@Validated(EditGroup.class) @RequestBody VendorAuditBo bo) {
        try {
            return toAjax(iVendorContractService.auditStampContract(bo));
        }catch (ServiceException e){
            return R.fail("oms审核盖章合同失败:" + e.getMessage());
        }catch (Exception e){
            log.error("oms审核盖章合同 param:{},case:{}", JSONUtil.toJsonStr(bo), Throwables.getStackTraceAsString(e));
            return R.fail("oms审核盖章合同失败");
        }
    }


    @SaCheckPermission("oms:contract:auditStampContract")
    @Log(title = "oms上传双章合同", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/uploadDoubleStampContract")
    public R<Void> uploadDoubleStampContract(@Validated(EditGroup.class) @RequestBody VendorContractDoubleStampBo bo) {
        try {
            return toAjax(iVendorContractService.uploadDoubleStampContract(bo));
        }catch (ServiceException e){
            return R.fail("oms上传双章合同失败:" + e.getMessage());
        }catch (Exception e){
            log.error("oms上传双章合同 param:{},case:{}", JSONUtil.toJsonStr(bo), Throwables.getStackTraceAsString(e));
            return R.fail("oms上传双章合同失败");
        }
    }


    @SaCheckPermission("oms:contract:vendorWarnInfo")
    @Log(title = "oms供应商合同提醒", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @GetMapping("/vendorWarnInfo")
    public R<Void> vendorWarnInfo(@RequestParam(value = "id") Long id) {
        try {
            return toAjax(iVendorContractService.vendorWarnInfo(id));
        }catch (ServiceException e){
            return R.fail("oms供应商合同提醒失败:" + e.getMessage());
        }catch (Exception e){
            log.error("oms供应商合同提醒 param:{},case:{}", id, Throwables.getStackTraceAsString(e));
            return R.fail("oms供应商合同提醒失败");
        }
    }

    @SaCheckPermission("oms:contract:alreadyTakeBackContract")
    @Log(title = "oms收回合同", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @GetMapping(value = "/alreadyTakeBackContract")
    public R<Void> alreadyTakeBackContract(@RequestParam(value = "id") Long id) {
        try {
            return toAjax(iVendorContractService.alreadyTakeBackContract(id));
        }catch (ServiceException e){
            return R.fail("oms收回合同失败:" + e.getMessage());
        }catch (Exception e){
            log.error("oms收回合同 param:{},case:{}", id, Throwables.getStackTraceAsString(e));
            return R.fail("oms收回合同失败");
        }
    }

    @SaCheckPermission("oms:contract:receiveStampContract")
    @Log(title = "oms收到合同", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @GetMapping(value = "/receiveStampContract")
    public R<Void> receiveStampContract(@RequestParam(value = "id") Long id) {
        try {
            return toAjax(iVendorContractService.receiveStampContract(id));
        }catch (ServiceException e){
            return R.fail("oms收到合同失败:" + e.getMessage());
        }catch (Exception e){
            log.error("oms收到合同 param:{},case:{}", id, Throwables.getStackTraceAsString(e));
            return R.fail("oms收到合同失败");
        }
    }




    /**
     * 删除供应商合同
     *
     * @param ids 主键串
     */
    @SaCheckPermission("oms:contract:remove")
    @Log(title = "供应商合同", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iVendorContractService.deleteWithValidByIds(Arrays.asList(ids), true));
    }
}
