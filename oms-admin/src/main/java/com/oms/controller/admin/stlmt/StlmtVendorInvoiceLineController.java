package com.oms.controller.admin.stlmt;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.oms.common.annotation.Log;
import com.oms.common.annotation.RepeatSubmit;
import com.oms.common.core.controller.BaseController;
import com.oms.common.core.domain.PageQuery;
import com.oms.common.core.domain.R;
import com.oms.common.core.page.TableDataInfo;
import com.oms.common.core.validate.AddGroup;
import com.oms.common.core.validate.EditGroup;
import com.oms.common.enums.BusinessType;
import com.oms.common.utils.poi.ExcelUtil;
import com.oms.domain.bo.StlmtVendorInvoiceLineBo;
import com.oms.domain.vo.StlmtVendorInvoiceLineVo;
import com.oms.service.IStlmtVendorInvoiceLineService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 结算供应商发票行
 *
 * <AUTHOR>
 * @date 2025-04-04
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/oms/vendorInvoiceLine")
public class StlmtVendorInvoiceLineController extends BaseController {

    private final IStlmtVendorInvoiceLineService iStlmtVendorInvoiceLineService;

    /**
     * 查询结算供应商发票行列表
     */
    @SaCheckPermission("oms:vendorInvoiceLine:list")
    @GetMapping("/list")
    public TableDataInfo<StlmtVendorInvoiceLineVo> list(StlmtVendorInvoiceLineBo bo, PageQuery pageQuery) {
        return iStlmtVendorInvoiceLineService.queryPageList(bo, pageQuery);
    }

    /**
     * 查询结算供应商发票行列表 - 账单头维度
     */
    @SaCheckPermission("oms:vendorInvoiceLine:list")
    @GetMapping("/invoiceLineBill")
    public TableDataInfo<StlmtVendorInvoiceLineVo> invoiceLineBill(StlmtVendorInvoiceLineBo bo, PageQuery pageQuery) {
        return iStlmtVendorInvoiceLineService.invoiceLineBill(bo, pageQuery);
    }

    /**
     * 查询结算供应商发票行列表 - 采购单头维度
     */
    @SaCheckPermission("oms:vendorInvoiceLine:list")
    @GetMapping("/invoiceLineOrder")
    public TableDataInfo<StlmtVendorInvoiceLineVo> invoiceLineOrder(StlmtVendorInvoiceLineBo bo, PageQuery pageQuery) {
        return iStlmtVendorInvoiceLineService.invoiceLineOrder(bo, pageQuery);
    }

    /**
     * 导出结算供应商发票行列表
     */
    @SaCheckPermission("oms:vendorInvoiceLine:export")
    @Log(title = "导出结算供应商发票行", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(StlmtVendorInvoiceLineBo bo, HttpServletResponse response) {
        List<StlmtVendorInvoiceLineVo> list = iStlmtVendorInvoiceLineService.queryList(bo);
        ExcelUtil.exportExcel(list, "结算供应商发票行", StlmtVendorInvoiceLineVo.class, response);
    }

    /**
     * 获取结算供应商发票行详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("oms:vendorInvoiceLine:query")
    @GetMapping("/{id}")
    public R<StlmtVendorInvoiceLineVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iStlmtVendorInvoiceLineService.queryById(id));
    }

    /**
     * 新增结算供应商发票行 基于账单
     */
    @SaCheckPermission("oms:vendorInvoiceLine:add")
    @Log(title = "新增结算供应商发票行", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/addByBill")
    public R<Void> addByBill(@Validated(AddGroup.class) @RequestBody StlmtVendorInvoiceLineBo bo) {
        return toAjax(iStlmtVendorInvoiceLineService.addByBill(bo));
    }

    /**
     * 新增结算供应商发票行 基于采购单
     */
    @SaCheckPermission("oms:vendorInvoiceLine:add")
    @Log(title = "新增结算供应商发票行", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/addByOrder")
    public R<Void> addByOrder(@Validated(AddGroup.class) @RequestBody StlmtVendorInvoiceLineBo bo) {
        return toAjax(iStlmtVendorInvoiceLineService.addByOrder(bo));
    }

    /**
     * 新增结算供应商发票行
     */
    @SaCheckPermission("oms:vendorInvoiceLine:add")
    @Log(title = "新增结算供应商发票行", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody StlmtVendorInvoiceLineBo bo) {
        return toAjax(iStlmtVendorInvoiceLineService.insertByBo(bo));
    }

    /**
     * 修改结算供应商发票行
     */
    @SaCheckPermission("oms:vendorInvoiceLine:edit")
    @Log(title = "修改结算供应商发票行", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody StlmtVendorInvoiceLineBo bo) {
        return toAjax(iStlmtVendorInvoiceLineService.updateByBo(bo));
    }

    /**
     * 删除结算供应商发票行
     *
     * @param ids 主键串
     */
    @SaCheckPermission("oms:vendorInvoiceLine:remove")
    @Log(title = "删除结算供应商发票行", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotNull(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iStlmtVendorInvoiceLineService.deleteWithValidByIds(Arrays.asList(ids), true));
    }

    /**
     * 删除结算供应商发票行 - 通过对账单id
     *
     * @param bo 采购进项票头id 对账单id
     */
    @SaCheckPermission("oms:vendorInvoiceLine:remove")
    @Log(title = "删除结算供应商发票行", businessType = BusinessType.DELETE)
    @DeleteMapping("/removeToBill")
    public R<Void> removeToBill(@RequestBody StlmtVendorInvoiceLineBo bo) {
        return toAjax(iStlmtVendorInvoiceLineService.removeToBill(bo));
    }

    /**
     * 删除结算供应商发票行 - 通过采购单id
     *
     * @param bo 采购进项票头id 采购单id集合
     */
    @SaCheckPermission("oms:vendorInvoiceLine:remove")
    @Log(title = "删除结算供应商发票行", businessType = BusinessType.DELETE)
    @DeleteMapping("/removeToOrder")
    public R<Void> removeToOrder(@RequestBody StlmtVendorInvoiceLineBo bo) {
        return toAjax(iStlmtVendorInvoiceLineService.removeToOrder(bo));
    }
}
