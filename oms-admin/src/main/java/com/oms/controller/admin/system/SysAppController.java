package com.oms.controller.admin.system;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.oms.common.annotation.Log;
import com.oms.common.annotation.RepeatSubmit;
import com.oms.common.core.controller.BaseController;
import com.oms.common.core.domain.PageQuery;
import com.oms.common.core.domain.R;
import com.oms.common.core.page.TableDataInfo;
import com.oms.common.core.validate.AddGroup;
import com.oms.common.core.validate.EditGroup;
import com.oms.common.enums.BusinessType;
import com.oms.common.utils.poi.ExcelUtil;
import com.oms.domain.bo.SysAppBo;
import com.oms.domain.vo.SysAppVo;
import com.oms.service.ISysAppService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 系统应用程序
 *
 * <AUTHOR>
 * @date 2024-09-05
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/oms/app")
public class SysAppController extends BaseController {

    private final ISysAppService iSysAppService;

    /**
     * 查询系统应用程序列表
     */
    @SaCheckPermission("oms:app:list")
    @GetMapping("/list")
    public TableDataInfo<SysAppVo> list(SysAppBo bo, PageQuery pageQuery) {
        return iSysAppService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出系统应用程序列表
     */
    @SaCheckPermission("oms:app:export")
    @Log(title = "系统应用程序", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SysAppBo bo, HttpServletResponse response) {
        List<SysAppVo> list = iSysAppService.queryList(bo);
        ExcelUtil.exportExcel(list, "系统应用程序", SysAppVo.class, response);
    }

    /**
     * 获取系统应用程序详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("oms:app:query")
    @GetMapping("/{id}")
    public R<SysAppVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iSysAppService.queryById(id));
    }

    /**
     * 新增系统应用程序
     */
    @SaCheckPermission("oms:app:add")
    @Log(title = "系统应用程序", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SysAppBo bo) {
        return toAjax(iSysAppService.insertByBo(bo));
    }

    /**
     * 修改系统应用程序
     */
    @SaCheckPermission("oms:app:edit")
    @Log(title = "系统应用程序", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SysAppBo bo) {
        return toAjax(iSysAppService.updateByBo(bo));
    }

    /**
     * 删除系统应用程序
     *
     * @param ids 主键串
     */
    @SaCheckPermission("oms:app:remove")
    @Log(title = "系统应用程序", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iSysAppService.deleteWithValidByIds(Arrays.asList(ids), true));
    }
}
