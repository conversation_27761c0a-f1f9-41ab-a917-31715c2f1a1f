package com.oms.controller.admin.rule;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.oms.common.annotation.Log;
import com.oms.common.annotation.RepeatSubmit;
import com.oms.common.core.controller.BaseController;
import com.oms.common.core.domain.PageQuery;
import com.oms.common.core.domain.R;
import com.oms.common.core.page.TableDataInfo;
import com.oms.common.core.validate.AddGroup;
import com.oms.common.core.validate.EditGroup;
import com.oms.common.enums.BusinessType;
import com.oms.common.utils.poi.ExcelUtil;
import com.oms.domain.bo.RuleTypeBo;
import com.oms.domain.vo.RuleTypeVo;
import com.oms.service.IRuleTypeService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 规则类型
 *
 * <AUTHOR>
 * @date 2024-11-09
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/oms/rule/type")
public class RuleTypeController extends BaseController {

    private final IRuleTypeService iRuleTypeService;

    /**
     * 查询规则类型列表
     */
    @SaCheckPermission("oms:rule:type:list")
    @GetMapping("/list")
    public TableDataInfo<RuleTypeVo> list(RuleTypeBo bo, PageQuery pageQuery) {
        return iRuleTypeService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出规则类型列表
     */
    @SaCheckPermission("oms:rule:type:export")
    @Log(title = "规则类型", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(RuleTypeBo bo, HttpServletResponse response) {
        List<RuleTypeVo> list = iRuleTypeService.queryList(bo);
        ExcelUtil.exportExcel(list, "规则类型", RuleTypeVo.class, response);
    }

    /**
     * 获取规则类型详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("oms:rule:type:query")
    @GetMapping("/{id}")
    public R<RuleTypeVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iRuleTypeService.queryById(id));
    }

    /**
     * 新增规则类型
     */
    @SaCheckPermission("oms:rule:type:add")
    @Log(title = "规则类型", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody RuleTypeBo bo) {
        return toAjax(iRuleTypeService.insertByBo(bo));
    }

    /**
     * 修改规则类型
     */
    @SaCheckPermission("oms:rule:type:edit")
    @Log(title = "规则类型", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody RuleTypeBo bo) {
        return toAjax(iRuleTypeService.updateByBo(bo));
    }

    /**
     * 删除规则类型
     *
     * @param ids 主键串
     */
    @SaCheckPermission("oms:rule:type:remove")
    @Log(title = "规则类型", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iRuleTypeService.deleteWithValidByIds(Arrays.asList(ids), true));
    }
}
