package com.oms.controller.admin.system;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.alibaba.fastjson.JSON;
import com.google.api.client.repackaged.com.google.common.base.Throwables;
import com.oms.common.annotation.Log;
import com.oms.common.annotation.RepeatSubmit;
import com.oms.common.core.controller.BaseController;
import com.oms.common.core.domain.R;
import com.oms.common.core.page.TableDataInfo;
import com.oms.common.core.validate.AddGroup;
import com.oms.common.core.validate.EditGroup;
import com.oms.common.enums.BusinessType;
import com.oms.common.enums.StatusEnum;
import com.oms.common.enums.SysUserDataTypeEnum;
import com.oms.common.exception.ServiceException;
import com.oms.common.utils.poi.ExcelUtil;
import com.oms.domain.bo.SysUserAffiliationBo;
import com.oms.domain.bo.extend.user.SysUserAffiliationBatchBo;
import com.oms.domain.vo.SysUserAffiliationVo;
import com.oms.domain.vo.SysUserIdentityAffiliationVo;
import com.oms.service.ISysUserAffiliationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 用户附属
 *
 * <AUTHOR>
 * @date 2024-09-08
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/oms/userAffiliation")
public class SysUserAffiliationController extends BaseController {

    private final ISysUserAffiliationService iSysUserAffiliationService;


    /**
     * 身份认证列表
     */
    @SaCheckPermission("oms:userAffiliation:userIdentityList")
    @GetMapping("/userIdentityList")
    public TableDataInfo<SysUserIdentityAffiliationVo> userIdentityPageList(SysUserAffiliationBo bo) {
        return iSysUserAffiliationService.userIdentityPageList(bo);
    }

    /**
     * 新增用户身份认证
     */
    @SaCheckPermission("oms:userAffiliation:addUserIdentity")
    @Log(title = "新增用户身份认证", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/addUserIdentity")
    public R<Void> addUserIdentity(@Validated(AddGroup.class) @RequestBody SysUserAffiliationBo bo) {
        try {
            return toAjax(iSysUserAffiliationService.addUserIdentity(bo));
        }catch (ServiceException e){
            return R.fail(e.getMessage());
        }catch (Exception e){
            log.error("新增用户身份认证失败 param:{},case:{}", JSON.toJSONString(bo), Throwables.getStackTraceAsString(e));
            return R.fail("新增用户身份认证失败");
        }
    }


    /**
     * 批量新增用户附属列表
     */
    @SaCheckPermission("oms:userAffiliation:batchOperatorUserAffiliation")
    @Log(title = "批量操作用户附属列表", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/batchOperatorUserAffiliation")
    public R<Void> batchOperatorUserAffiliation(@Validated(AddGroup.class) @RequestBody SysUserAffiliationBatchBo bo) {
        try {
            return toAjax(iSysUserAffiliationService.batchOperatorUserAffiliation(bo));
        }catch (ServiceException e){
            return R.fail(e.getMessage());
        }catch (Exception e){
            log.error("批量操作用户附属列表失败 param:{},case:{}", JSON.toJSONString(bo), Throwables.getStackTraceAsString(e));
            return R.fail("批量操作用户附属列表失败");
        }
    }


    /**
     * 用户认证启用
     */
    @SaCheckPermission("oms:userAffiliation:userIdentityEnable")
    @Log(title = "用户认证启用", businessType = BusinessType.UPDATE)
    @PostMapping("/userIdentityEnable/{id}")
    public R<Void> userIdentityEnable(@PathVariable(value = "id") Long id) {
        try {
            return toAjax(iSysUserAffiliationService.userIdentityChangeStatus(id, StatusEnum.NOR.getValue()));
        }catch (ServiceException e){
            return R.fail(e.getMessage());
        }catch (Exception e){
            log.error("用户认证启用失败 param:{},case:{}", id, Throwables.getStackTraceAsString(e));
            return R.fail("用户认证启用失败");
        }
    }

    /**
     * 用户身份禁用
     */
    @SaCheckPermission("oms:userAffiliation:userIdentityDisable")
    @Log(title = "用户身份禁用", businessType = BusinessType.UPDATE)
    @PostMapping("/userIdentityDisable/{id}")
    public R<Void> userIdentityDisable(@PathVariable(value = "id") Long id) {
        try {
            return toAjax(iSysUserAffiliationService.userIdentityChangeStatus(id, StatusEnum.DIS.getValue()));
        }catch (ServiceException e){
            return R.fail(e.getMessage());
        }catch (Exception e){
            log.error("用户身份禁用失败 param:{},case:{}", id, Throwables.getStackTraceAsString(e));
            return R.fail("用户身份禁用失败");
        }
    }

    /**
     * 用户身份可用列表
     */
    @SaCheckPermission("oms:userAffiliation:userIdentityAvailableList")
    @GetMapping("/userIdentityAvailableList")
    public R<List<SysUserIdentityAffiliationVo>> userIdentityAvailableList(SysUserAffiliationBo bo) {
        try {
            return R.ok(iSysUserAffiliationService.userIdentityAvailableList(bo));
        }catch (ServiceException e){
            return R.fail(e.getMessage());
        }catch (Exception e){
            log.error("用户身份可用列表失败 param:{},case:{}", JSON.toJSONString(bo), Throwables.getStackTraceAsString(e));
            return R.fail("用户身份可用列表失败");
        }
    }

    /**
     * 删除用户附属
     *
     * @param ids 主键串
     */
    @SaCheckPermission("oms:userAffiliation:remove")
    @Log(title = "删除用户附属", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotNull(message = "主键不能为空")@PathVariable String[] ids) {
        try {
            iSysUserAffiliationService.deleteWithValidByIds(Arrays.asList(ids), true);
            return R.ok();
        }catch (ServiceException e){
            return R.fail(e.getMessage());
        }catch (Exception e){
            log.error("删除用户附属失败 param:{},case:{}", ids, Throwables.getStackTraceAsString(e));
            return R.fail("删除用户附属失败");
        }
    }

    /**
     * 查询用户附属列表
     */
    @SaCheckPermission("oms:userAffiliation:list")
    @GetMapping("/list")
    public TableDataInfo<SysUserAffiliationVo> list(SysUserAffiliationBo bo) {
        return iSysUserAffiliationService.queryPageList(bo);
    }

    /**
     * 查询用户附属-可用项目区域
     */
    @SaCheckPermission("oms:userAffiliation:availableProjectArea")
    @GetMapping("/availableProjectArea")
    public List<SysUserAffiliationVo> availableProjectArea(@RequestParam(value = "userId") Long userId, @RequestParam(value = "projectId") String projectId) {
        return iSysUserAffiliationService.availableProjectArea(userId, projectId);
    }

    /**
     * 查询用户附属-- 已绑定项目区域
     */
    @SaCheckPermission("oms:userAffiliation:bindProjectArea")
    @GetMapping("/bindProjectArea")
    public List<SysUserAffiliationVo> list(@RequestParam(value = "userId") Long userId, @RequestParam(value = "projectId") String projectId) {
        return iSysUserAffiliationService.bindProjectArea(userId, projectId);
    }


    /**
     * 导出用户附属列表
     */
    @SaCheckPermission("oms:userAffiliation:export")
    @Log(title = "用户附属", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SysUserAffiliationBo bo, HttpServletResponse response) {
        List<SysUserAffiliationVo> list = iSysUserAffiliationService.queryList(bo);
        ExcelUtil.exportExcel(list, "用户附属", SysUserAffiliationVo.class, response);
    }

    /**
     * 获取用户附属详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("oms:userAffiliation:query")
    @GetMapping("/{id}")
    public R<SysUserAffiliationVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable String id) {
        return R.ok(iSysUserAffiliationService.queryById(id));
    }

    /**
     * 修改用户附属
     */
    @SaCheckPermission("oms:userAffiliation:edit")
    @Log(title = "用户附属", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SysUserAffiliationBo bo) {
        return toAjax(iSysUserAffiliationService.updateByBo(bo));
    }


}
