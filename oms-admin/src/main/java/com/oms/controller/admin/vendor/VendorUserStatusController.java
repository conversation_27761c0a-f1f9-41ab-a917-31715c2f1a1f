package com.oms.controller.admin.vendor;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.google.api.client.repackaged.com.google.common.base.Throwables;
import com.oms.common.annotation.Log;
import com.oms.common.annotation.RepeatSubmit;
import com.oms.common.core.controller.BaseController;
import com.oms.common.core.domain.R;
import com.oms.common.core.page.TableDataInfo;
import com.oms.common.core.validate.AddGroup;
import com.oms.common.core.validate.EditGroup;
import com.oms.common.enums.BusinessType;
import com.oms.common.enums.StatusEnum;
import com.oms.common.exception.ServiceException;
import com.oms.common.utils.poi.ExcelUtil;
import com.oms.domain.bo.VendorUserStatusBo;
import com.oms.domain.vo.VendorUserStatusVo;
import com.oms.service.IVendorUserStatusService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 供应商账号状态
 *
 * <AUTHOR>
 * @date 2024-10-21
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/oms/vendorUserStatus")
public class VendorUserStatusController extends BaseController {

    private final IVendorUserStatusService iVendorUserStatusService;

    /**
     * 查询供应商账号状态列表
     */
    @SaCheckPermission("oms:vendorUserStatus:list")
    @GetMapping("/list")
    public TableDataInfo<VendorUserStatusVo> list(VendorUserStatusBo bo) {
        return iVendorUserStatusService.queryPageList(bo);
    }



    /**
     * 获取供应商账号状态详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("oms:vendorUserStatus:query")
    @GetMapping("/{id}")
    public R<VendorUserStatusVo> getInfo(@NotNull(message = "主键不能为空")
                                         @PathVariable Long id) {
        return R.ok(iVendorUserStatusService.queryById(id));
    }


    /**
     * 供应商账号关停
     */
    @SaCheckPermission("vendor:vendorUserStatus:stop")
    @Log(title = "供应商账号关停", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/stop")
    public R<Void> stop(@Validated(AddGroup.class) @RequestBody VendorUserStatusBo bo) {
        try {
            bo.setApplyStatus(StatusEnum.DIS.getValue());
            return toAjax(iVendorUserStatusService.insertByBo(bo));
        }catch (ServiceException e){
            return R.fail(e.getMessage());
        }catch (Exception e){
            log.error("供应商账号关停失败 param:{},case:{}", JSON.toJSONString(bo), Throwables.getStackTraceAsString(e));
            return R.fail("供应商账号关停失败");
        }
    }

    /**
     * 供应商账号重启
     */
    @SaCheckPermission("vendor:vendorUserStatus:restart")
    @Log(title = "供应商账号重启", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/restart")
    public R<Void> restart(@Validated(AddGroup.class) @RequestBody VendorUserStatusBo bo) {
        try {
            bo.setApplyStatus(StatusEnum.NOR.getValue());
            return toAjax(iVendorUserStatusService.insertByBo(bo));
        }catch (ServiceException e){
            return R.fail(e.getMessage());
        }catch (Exception e){
            log.error("供应商账号重启失败 param:{},case:{}", JSON.toJSONString(bo), Throwables.getStackTraceAsString(e));
            return R.fail("供应商账号重启失败");
        }
    }


    /**
     * 供应商账号重启关停审核
     */
    @SaCheckPermission("oms:vendorUserStatus:edit")
    @Log(title = "供应商账号重启关停审核", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/auditStatus")
    public R<Void> auditStatus(@RequestBody VendorUserStatusBo bo) {
        try {
            return toAjax(iVendorUserStatusService.auditStatus(bo));
        }catch (ServiceException e){
            return R.fail(e.getMessage());
        }catch (Exception e){
            log.error("供应商账号重启关停审核失败 param:{},case:{}", JSONUtil.toJsonStr(bo), Throwables.getStackTraceAsString(e));
            return R.fail("供应商账号重启关停审核失败");
        }
    }


    /**
     * 修改供应商账号状态
     */
    @SaCheckPermission("oms:vendorUserStatus:edit")
    @Log(title = "供应商账号", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody VendorUserStatusBo bo) {
        return toAjax(iVendorUserStatusService.updateByBo(bo));
    }



    /**
     * 导出供应商账号状态列表
     */
    @SaCheckPermission("oms:vendorUserStatus:export")
    @Log(title = "供应商账号状态", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(VendorUserStatusBo bo, HttpServletResponse response) {
        List<VendorUserStatusVo> list = iVendorUserStatusService.queryList(bo);
        ExcelUtil.exportExcel(list, "供应商账号状态", VendorUserStatusVo.class, response);
    }

}
