package com.oms.controller.admin.goods;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.oms.common.annotation.Log;
import com.oms.common.annotation.RepeatSubmit;
import com.oms.common.core.controller.BaseController;
import com.oms.common.core.domain.PageQuery;
import com.oms.common.core.domain.R;
import com.oms.common.core.page.TableDataInfo;
import com.oms.common.core.validate.AddGroup;
import com.oms.common.core.validate.EditGroup;
import com.oms.common.enums.BusinessType;
import com.oms.common.utils.poi.ExcelUtil;
import com.oms.domain.bo.GoodsPrjSkuBo;
import com.oms.domain.vo.GoodsPrjSkuVo;
import com.oms.service.IGoodsPrjSkuService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 项目sku
 *
 * <AUTHOR>
 * @date 2024-10-07
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/oms/prjSku")
public class GoodsPrjSkuController extends BaseController {

    private final IGoodsPrjSkuService iGoodsPrjSkuService;

    /**
     * 查询项目sku列表
     */
    @SaCheckPermission("oms:prjSku:list")
    @GetMapping("/list")
    public TableDataInfo<GoodsPrjSkuVo> list(GoodsPrjSkuBo bo, PageQuery pageQuery) {
        return iGoodsPrjSkuService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出项目sku列表
     */
    @SaCheckPermission("oms:prjSku:export")
    @Log(title = "项目sku", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(GoodsPrjSkuBo bo, HttpServletResponse response) {
        List<GoodsPrjSkuVo> list = iGoodsPrjSkuService.queryList(bo);
        ExcelUtil.exportExcel(list, "项目sku", GoodsPrjSkuVo.class, response);
    }

    /**
     * 获取项目sku详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("oms:prjSku:query")
    @GetMapping("/{id}")
    public R<GoodsPrjSkuVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iGoodsPrjSkuService.queryById(id));
    }

    /**
     * 新增项目sku
     */
    @SaCheckPermission("oms:prjSku:add")
    @Log(title = "项目sku", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody GoodsPrjSkuBo bo) {
        bo.setTenantId(1L);
        return toAjax(iGoodsPrjSkuService.insertByBo(bo));
    }

    /**
     * 修改项目sku
     */
    @SaCheckPermission("oms:prjSku:edit")
    @Log(title = "项目sku", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody GoodsPrjSkuBo bo) {
        return toAjax(iGoodsPrjSkuService.updateByBo(bo));
    }

    /**
     * 删除项目sku
     *
     * @param ids 主键串
     */
    @SaCheckPermission("oms:prjSku:remove")
    @Log(title = "项目sku", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iGoodsPrjSkuService.deleteWithValidByIds(Arrays.asList(ids), true));
    }
}
