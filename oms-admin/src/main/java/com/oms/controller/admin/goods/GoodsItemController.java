package com.oms.controller.admin.goods;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaIgnore;
import com.alibaba.fastjson.JSON;
import com.google.api.client.repackaged.com.google.common.base.Throwables;
import com.google.api.client.util.Lists;
import com.oms.common.annotation.Log;
import com.oms.common.core.controller.BaseController;
import com.oms.common.core.domain.R;
import com.oms.common.core.page.TableDataInfo;
import com.oms.common.enums.BusinessType;
import com.oms.common.exception.ServiceException;
import com.oms.common.utils.poi.ExcelUtil;
import com.oms.domain.bo.GoodsItemBo;
import com.oms.domain.excel.GoodsItemTemplate;
import com.oms.domain.vo.GoodsItemVo;
import com.oms.esmodel.GoodsItemEs;
import com.oms.esservice.IGoodsItemEsService;
import com.oms.service.IGoodsItemService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

//import java.util.List;
//import javax.servlet.http.HttpServletResponse;

/**
 * 商品
 *
 * <AUTHOR>
 * @date 2024-07-22
 */
@Validated
@RequiredArgsConstructor
@RestController
@Slf4j
@RequestMapping("/oms/goodsItem")
public class GoodsItemController extends BaseController {

    private final IGoodsItemService iGoodsItemService;
    private final IGoodsItemEsService goodsItemEsService;

    /**
     * 查询商品列表
     */
    @SaCheckPermission("oms:item:list")
	@GetMapping("/list")
    public TableDataInfo<GoodsItemVo> list(GoodsItemBo bo) {
        bo.setTenantId(1L);
        return iGoodsItemService.queryPageList(bo);
    }

    /**
     * 查询商品列表 -es
     */
    @SaCheckPermission("oms:item:list")
    @GetMapping("/pageList")
    public TableDataInfo<GoodsItemEs> pageList(GoodsItemBo bo) {
        bo.setPushStatusList(Arrays.asList(1L, 4L,5L,6L));
        return goodsItemEsService.pageList(bo);
    }

    /**
     * 导出商品列表
     */
    @SaCheckPermission("oms:item:export")
    @Log(title = "商品", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(GoodsItemBo bo, HttpServletResponse response) {
        List<GoodsItemVo> list = iGoodsItemService.queryList(bo);
        ExcelUtil.exportExcel(list, "商品", GoodsItemVo.class, response);
    }

    /**
     * 商品导入模板导出
     */
    @SaCheckPermission("oms:item:export")
    @Log(title = "商品导入模板导出", businessType = BusinessType.EXPORT)
    @GetMapping("/exportTemplate")
    public void exportTemplate(HttpServletResponse response) {
        List<GoodsItemTemplate> list = Lists.newArrayList();
        GoodsItemTemplate template = new GoodsItemTemplate();
        template.setPrjName("填写中台项目名称");
        template.setBackCateName("填写商品分类 一级类目名称>二级名称>三级名称>四级名称");
        template.setOutCateName("填写外部客户分类名称  一级类目名称>二级名称>三级名称>四级名称");
        template.setSkuCode("填写sku编码");
        template.setBarcode("填写条形码");
        template.setUniversalName("填写通用名称");
        template.setBrandName("填写品牌名称");
        template.setUnit("填写计量单位名称");
        template.setSpecification("填写规格 (规格型号必填一个)");
        template.setCommunityModel("填写型号 (规格型号必填一个)");
        template.setItemColor("填写颜色");
        template.setVatrate("填写税率(0 0.09 0.13)");
        template.setTaxCode("填写税收分类编码");
        template.setBjUrl1("填写比价链接1");
        template.setBjUrl2("填写比价链接2");
        template.setBjUrl3("填写比价链接3");
        template.setCsrDesc("填写客户专用描述信息");
        template.setBasePrice("填写成本价");
        template.setMarketPrice("填写市场价");
        template.setSalePrice("填写销售价");
        template.setPacking("填写包装清单");
        template.setTitleName("填写商品副标题");
        template.setDetail("填写商品详情（富文本）");
        list.add(template);
        ExcelUtil.exportExcel(list, "商品导入模板", GoodsItemTemplate.class, response);
    }


    /**
     * 获取商品详细信息
     *
     * @param bo 主键
     */
    @SaCheckPermission("oms:item:query")
	@GetMapping("/getInfo")
    @SaIgnore
    public R<GoodsItemVo> getInfo(GoodsItemBo bo) {
        return R.ok(iGoodsItemService.queryById(bo));
    }

    /**
     * 查询商品信息 -用于修改
     *
     * @param bo 主键
     */
    @SaCheckPermission("oms:item:query")
    @GetMapping("/queryItemByUpdate")
    public R<GoodsItemVo> queryItemByUpdate(GoodsItemBo bo) {
        return R.ok(iGoodsItemService.queryItemByUpdate(bo));
    }

    /**
     * 新增商品
     */
	@SaCheckPermission("oms:item:add")
    @PostMapping()
    public R<GoodsItemVo> add(@RequestBody GoodsItemBo bo) {
        try {
            return R.ok(iGoodsItemService.insertByBo(bo));
        }catch (ServiceException e){
            return R.fail(e.getMessage());
        }catch (Exception e){
            log.error("新增商品失败 param:{},case:{}", JSON.toJSONString(bo), Throwables.getStackTraceAsString(e));
            return R.fail("新增商品失败！");
        }
    }

    /**
     * 修改商品
     */
	@SaCheckPermission("oms:item:edit")
    @PutMapping()
    @SaIgnore
    public R<Void> edit(@RequestBody GoodsItemBo bo) {
        try {
            return toAjax(iGoodsItemService.updateByBo(bo) ? 1 : 0);
        }catch (ServiceException e){
            return R.fail(e.getMessage());
        }catch (Exception e){
            log.error("修改商品失败 param:{},case:{}", JSON.toJSONString(bo), Throwables.getStackTraceAsString(e));
            return R.fail("修改商品失败！");
        }
    }

    /**
     * 管理员修改商品
     */
    @SaCheckPermission("oms:item:edit")
    @PutMapping("/editAdmin")
    public R<Void> editAdmin(@RequestBody GoodsItemBo bo) {
        try {
            bo.setIfAdminEdit(true);
            return toAjax(iGoodsItemService.updateByBo(bo) ? 1 : 0);
        }catch (ServiceException e){
            return R.fail(e.getMessage());
        }catch (Exception e){
            log.error("修改商品失败 param:{},case:{}", JSON.toJSONString(bo), Throwables.getStackTraceAsString(e));
            return R.fail("修改商品失败！");
        }
    }

    /**
     * 冻结商品
     */
    @SaCheckPermission("oms:item:edit")
    @PostMapping("/freeze")
    public R<Void> freeze(@RequestBody GoodsItemBo bo) {
        try {
            return toAjax(iGoodsItemService.freeze(bo) ? 1 : 0);
        }catch (ServiceException e){
            return R.fail(e.getMessage());
        }catch (Exception e){
            log.error("锁定失败失败 param:{},case:{}", JSON.toJSONString(bo), Throwables.getStackTraceAsString(e));
            return R.fail("锁定失败！");
        }
    }

    /**
     * 解冻商品
     */
    @SaCheckPermission("oms:item:edit")
    @PostMapping("/unfreeze")
    public R<Void> unfreeze(@RequestBody GoodsItemBo bo) {
        try {
            return toAjax(iGoodsItemService.unfreeze(bo) ? 1 : 0);
        }catch (ServiceException e){
            return R.fail(e.getMessage());
        }catch (Exception e){
            log.error("解锁失败 param:{},case:{}", JSON.toJSONString(bo), Throwables.getStackTraceAsString(e));
            return R.fail("解锁失败！");
        }
    }



    /**
     * 删除商品
     *
     * @param ids 主键串
     */
	@SaCheckPermission("oms:item:remove")
    @DeleteMapping("/{ids}")
    public R<Void> remove(@PathVariable(value = "ids") Long[] ids) {
        return toAjax(iGoodsItemService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }

    /**
     * 批量发布项目
     */
    @SaCheckPermission("oms:item:edit")
    @PostMapping("/batchPush")
    public R<Void> batchPush(@RequestBody GoodsItemBo bo) {
        try {
            return toAjax(iGoodsItemService.batchPush(bo) ? 1 : 0);
        }catch (ServiceException e){
            return R.fail(e.getMessage());
        }catch (Exception e){
            log.error("锁定失败失败 param:{},case:{}", JSON.toJSONString(bo), Throwables.getStackTraceAsString(e));
            return R.fail("锁定失败！");
        }
    }
}
