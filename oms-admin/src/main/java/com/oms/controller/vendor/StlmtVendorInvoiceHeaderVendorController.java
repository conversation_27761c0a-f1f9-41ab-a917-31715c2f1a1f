package com.oms.controller.vendor;

import java.util.List;
import java.util.Arrays;

import com.oms.common.helper.LoginHelper;
import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.oms.common.annotation.RepeatSubmit;
import com.oms.common.annotation.Log;
import com.oms.common.core.controller.BaseController;
import com.oms.common.core.domain.PageQuery;
import com.oms.common.core.domain.R;
import com.oms.common.core.validate.AddGroup;
import com.oms.common.core.validate.EditGroup;
import com.oms.common.enums.BusinessType;
import com.oms.common.utils.poi.ExcelUtil;
import com.oms.domain.vo.StlmtVendorInvoiceHeaderVo;
import com.oms.domain.bo.StlmtVendorInvoiceHeaderBo;
import com.oms.service.IStlmtVendorInvoiceHeaderService;
import com.oms.common.core.page.TableDataInfo;

/**
 * 结算供应商发票头
 *
 * <AUTHOR>
 * @date 2025-02-27
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/vendor/vendorInvoiceHeader")
public class StlmtVendorInvoiceHeaderVendorController extends BaseController {

    private final IStlmtVendorInvoiceHeaderService iStlmtVendorInvoiceHeaderService;

    /**
     * 查询结算供应商发票头列表
     */
    @SaCheckPermission("oms:vendorInvoiceHeader:list")
    @GetMapping("/list")
    public TableDataInfo<StlmtVendorInvoiceHeaderVo> list(StlmtVendorInvoiceHeaderBo bo, PageQuery pageQuery) {
        bo.setVendorId(LoginHelper.getVendorId());
        return iStlmtVendorInvoiceHeaderService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出结算供应商发票头列表
     */
    @SaCheckPermission("oms:vendorInvoiceHeader:export")
    @Log(title = "导出结算供应商发票头", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(StlmtVendorInvoiceHeaderBo bo, HttpServletResponse response) {
        List<StlmtVendorInvoiceHeaderVo> list = iStlmtVendorInvoiceHeaderService.queryList(bo);
        ExcelUtil.exportExcel(list, "结算供应商发票头", StlmtVendorInvoiceHeaderVo.class, response);
    }

    /**
     * 获取结算供应商发票头详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("oms:vendorInvoiceHeader:query")
    @GetMapping("/{id}")
    public R<StlmtVendorInvoiceHeaderVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iStlmtVendorInvoiceHeaderService.queryById(id));
    }

    /**
     * 新增结算供应商发票头
     */
    @SaCheckPermission("oms:vendorInvoiceHeader:add")
    @Log(title = "新增结算供应商发票头", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody StlmtVendorInvoiceHeaderBo bo) {
        bo.setInvoiceSource("1");
        bo.setVendorId(LoginHelper.getVendorId());
        return toAjax(iStlmtVendorInvoiceHeaderService.insertByBo(bo));
    }

    /**
     * 修改结算供应商发票头
     */
    @SaCheckPermission("oms:vendorInvoiceHeader:edit")
    @Log(title = "修改结算供应商发票头", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody StlmtVendorInvoiceHeaderBo bo) {
        return toAjax(iStlmtVendorInvoiceHeaderService.updateByBo(bo));
    }

    /**
     * 删除结算供应商发票头
     *
     * @param ids 主键串
     */
    @SaCheckPermission("oms:vendorInvoiceHeader:remove")
    @Log(title = "删除结算供应商发票头", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotNull(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iStlmtVendorInvoiceHeaderService.deleteWithValidByIds(Arrays.asList(ids), true));
    }

    /**
     * 提交采购进项发票 -提交单据信息
     */
    @SaCheckPermission("oms:vendorInvoiceHeader:submit")
    @Log(title = "提交采购进项发票", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/submit")
    public R<Void> submit(@Validated(AddGroup.class) @RequestBody StlmtVendorInvoiceHeaderBo bo) {
        bo.setSubmitBy(LoginHelper.getUsername());
        return toAjax(iStlmtVendorInvoiceHeaderService.submit(bo));
    }

    /**
     * 提交采购进项发票 -提交单据信息
     */
    @SaCheckPermission("oms:vendorInvoiceHeader:submit")
    @Log(title = "提交采购进项发票", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/submitBatch")
    public R<Void> submitBatch(@Validated(AddGroup.class) @RequestBody StlmtVendorInvoiceHeaderBo bo) {
        bo.setSubmitBy(LoginHelper.getUsername());
        return toAjax(iStlmtVendorInvoiceHeaderService.submitBatch(bo));
    }

    /**
     * 提交采购进项发票 - 提交发票信息
     */
    @SaCheckPermission("oms:vendorInvoiceHeader:submit")
    @Log(title = "提交采购进项发票", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/submitInvoiceBatch")
    public R<Void> submitInvoiceBatch(@Validated(AddGroup.class) @RequestBody StlmtVendorInvoiceHeaderBo bo) {
        return toAjax(iStlmtVendorInvoiceHeaderService.submitInvoiceBatch(bo));
    }

    /**
     * 如果是oms发起  供应商审核
     * 审核采购进项发票 - 审核单据信息
     */
    @SaCheckPermission("oms:vendorInvoiceHeader:audit")
    @Log(title = "审核采购进项发票 - 审核单据信息", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/audit")
    public R<Void> audit(@Validated(EditGroup.class) @RequestBody StlmtVendorInvoiceHeaderBo bo) {
        bo.setAuditBy(LoginHelper.getUsername());
        return toAjax(iStlmtVendorInvoiceHeaderService.audit(bo));
    }
}
