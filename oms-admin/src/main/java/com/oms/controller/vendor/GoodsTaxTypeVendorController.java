package com.oms.controller.vendor;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.oms.common.core.controller.BaseController;
import com.oms.common.core.domain.R;
import com.oms.common.core.page.TableDataInfo;
import com.oms.domain.bo.GoodsTaxTypeBo;
import com.oms.domain.vo.GoodsTaxTypeVo;
import com.oms.service.IGoodsTaxTypeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;

//import java.util.List;
//import javax.servlet.http.HttpServletResponse;

/**
 * 品牌
 *
 * <AUTHOR>
 * @date 2024-08-31
 */
@Validated
@RequiredArgsConstructor
@Slf4j
@RestController
@RequestMapping("/vendor/goodsTaxType")
public class GoodsTaxTypeVendorController extends BaseController {

    private final IGoodsTaxTypeService iGoodsTaxTypeService;

    /**
     * 查询税收分类列表
     */
    @SaCheckPermission("oms:TaxType:queryPageList")
    @PostMapping("/queryPageList")
    public TableDataInfo<GoodsTaxTypeVo> queryPageList(@RequestBody GoodsTaxTypeBo bo) {
        return iGoodsTaxTypeService.queryPageList(bo);
    }

    /**
     * 导出税收分类列表
     */
//    @SaCheckPermission("oms:TaxType:export")
//	@ActionLog(module = ModuleType.Goods, title = "导出税收分类列表", businessType = BusinessType.EXPORT, operatorType = OperatorType.MANAGE)
    //@PostMapping("/export")
    //public void export(GoodsTaxTypeBo bo, HttpServletResponse response) {
    //	List<GoodsTaxTypeVo> list = iGoodsTaxTypeService.queryList(bo);
    //	ExcelUtil.exportExcel(list, "税收分类", GoodsTaxTypeVo.class, response);
    //}

    /**
     * 获取税收分类详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("oms:TaxType:query")
    @GetMapping("/{id}")
    public R<GoodsTaxTypeVo> getInfo(@PathVariable(value = "id") Long id) {
        return R.ok(iGoodsTaxTypeService.queryById(id));
    }

}
