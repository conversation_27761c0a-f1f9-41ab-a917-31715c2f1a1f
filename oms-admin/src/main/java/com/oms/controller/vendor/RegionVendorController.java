package com.oms.controller.vendor;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaIgnore;
import com.oms.common.annotation.Log;
import com.oms.common.annotation.RepeatSubmit;
import com.oms.common.core.controller.BaseController;
import com.oms.common.core.domain.R;
import com.oms.common.core.page.TableDataInfo;
import com.oms.common.core.validate.AddGroup;
import com.oms.common.core.validate.EditGroup;
import com.oms.common.enums.BusinessType;
import com.oms.common.utils.poi.ExcelUtil;
import com.oms.domain.bo.RegionBo;
import com.oms.domain.vo.RegionVo;
import com.oms.service.IRegionService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 系统地区
 *
 * <AUTHOR>
 * @date 2024-10-09
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/vendor/region")
public class RegionVendorController extends BaseController {

    private final IRegionService iRegionService;

    /**
     * 查询系统地区列表
     */
    @SaCheckPermission("vendor:region:list")
    @GetMapping("/list")
    public TableDataInfo<RegionVo> list(RegionBo bo) {
        return iRegionService.queryPageList(bo);
    }

    /**
     * 根据父地址id查询地址信息
     *
     * @param bo
     */
    @SaCheckPermission("vendor:region:query")
    @GetMapping("/queryRegionByPid")
    public R<List<RegionVo>> queryRegionByPid(RegionBo bo) {
        return R.ok(iRegionService.queryRegionByPid(bo));
    }

    /**
     * 根据父地址code查询地址信息
     *
     * @param bo
     */
    @SaIgnore
    @GetMapping("/queryRegionByPCode")
    public R<List<RegionVo>> queryRegionByPCode(RegionBo bo) {
        return R.ok(iRegionService.queryRegionByPCode(bo));
    }

}
