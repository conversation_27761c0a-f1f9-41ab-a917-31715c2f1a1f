package com.oms.controller.vendor;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.google.api.client.repackaged.com.google.common.base.Throwables;
import com.oms.common.annotation.Log;
import com.oms.common.annotation.RepeatSubmit;
import com.oms.common.core.controller.BaseController;
import com.oms.common.core.domain.PageQuery;
import com.oms.common.core.domain.R;
import com.oms.common.core.page.TableDataInfo;
import com.oms.common.enums.BusinessType;
import com.oms.common.exception.ServiceException;
import com.oms.common.helper.LoginHelper;
import com.oms.common.utils.poi.ExcelUtil;
import com.oms.domain.bo.GoodsItemAuditBo;
import com.oms.domain.vo.GoodsItemAuditVo;
import com.oms.service.IGoodsItemAuditService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 商品审核
 *
 * <AUTHOR>
 * @date 2024-10-06
 */
@Validated
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/vendor/itemAudit")
public class GoodsItemAuditVendorController extends BaseController {

    private final IGoodsItemAuditService iGoodsItemAuditService;

    /**
     * 查询商品审核列表
     */
    @SaCheckPermission("oms:itemAudit:list")
    @GetMapping("/list")
    public TableDataInfo<GoodsItemAuditVo> list(GoodsItemAuditBo bo, PageQuery pageQuery) {
        bo.setTenantId(1L);
        bo.setVendorId(LoginHelper.getVendorId());
        return iGoodsItemAuditService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出商品审核列表
     */
    @SaCheckPermission("oms:itemAudit:export")
    @Log(title = "商品审核", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(GoodsItemAuditBo bo, HttpServletResponse response) {
        List<GoodsItemAuditVo> list = iGoodsItemAuditService.queryList(bo);
        ExcelUtil.exportExcel(list, "商品审核", GoodsItemAuditVo.class, response);
    }

    /**
     * 获取商品审核详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("oms:itemAudit:query")
    @GetMapping("/{id}")
    public R<GoodsItemAuditVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iGoodsItemAuditService.queryById(id));
    }

    /**
     * 提交审核
     */
    @SaCheckPermission("oms:itemAudit:submit")
    @PostMapping("/submit")
    @RepeatSubmit()
    public R<Void> submit(@RequestBody List<GoodsItemAuditBo> list) {
        try {
            if(CollectionUtil.isEmpty(list)){
                return R.fail("请选择商品！");
            }
            list.forEach(bo -> bo.setTenantId(1L));;
            return toAjax(iGoodsItemAuditService.submit(list, LoginHelper.getVendorId()));
        }catch (ServiceException e){
            return R.fail(e.getMessage());
        }catch (Exception e){
            log.error("提交审核失败 param:{},case:{}", JSON.toJSONString(list), Throwables.getStackTraceAsString(e));
            return R.fail("提交审核失败！");
        }
    }

    /**
     * 申请推送项目
     */
    @SaCheckPermission("oms:itemAudit:submit")
    @PostMapping("/applyPushPrj")
    @RepeatSubmit()
    public R<Void> applyPushPrj(@RequestBody GoodsItemAuditBo bo) {
        try {
            bo.setTenantId(1L);
            bo.setVendorId(LoginHelper.getVendorId());
            return toAjax(iGoodsItemAuditService.applyPushPrj(bo));
        }catch (ServiceException e){
            return R.fail(e.getMessage());
        }catch (Exception e){
            log.error("提交审核失败 param:{},case:{}", JSON.toJSONString(bo), Throwables.getStackTraceAsString(e));
            return R.fail("提交审核失败！");
        }
    }
}
