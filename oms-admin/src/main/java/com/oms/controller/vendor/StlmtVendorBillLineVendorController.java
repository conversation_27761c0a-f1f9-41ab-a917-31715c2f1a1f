package com.oms.controller.vendor;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Throwables;
import com.oms.common.annotation.Log;
import com.oms.common.annotation.RepeatSubmit;
import com.oms.common.core.controller.BaseController;
import com.oms.common.core.domain.R;
import com.oms.common.core.page.TableDataInfo;
import com.oms.common.core.validate.AddGroup;
import com.oms.common.core.validate.EditGroup;
import com.oms.common.enums.BusinessType;
import com.oms.common.exception.ServiceException;
import com.oms.common.helper.LoginHelper;
import com.oms.common.utils.poi.ExcelUtil;
import com.oms.domain.bo.StlmtVendorBillLineBo;
import com.oms.domain.bo.extend.stlmt.StlmtVendorBillLineQueryBo;
import com.oms.domain.bo.extend.stlmt.StlmtVendorBillOrderDelBo;
import com.oms.domain.bo.extend.stlmt.StlmtVendorBillOrderOperateCreateBo;
import com.oms.domain.bo.extend.stlmt.StlmtVendorBillPurchaseBo;
import com.oms.domain.vo.StlmtVendorBillLineVo;
import com.oms.domain.vo.extend.settlement.StlmtVendorBillPurchaseAmountVo;
import com.oms.domain.vo.extend.settlement.StlmtVendorBillPurchaseOrderLineVo;
import com.oms.domain.vo.extend.settlement.StlmtVendorBillPurchaseOrderVo;
import com.oms.service.IStlmtVendorBillLineService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 供应商对账明细
 *
 * <AUTHOR>
 * @date 2025-02-04
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/vendor/stlmt/vendorBillLine")
public class StlmtVendorBillLineVendorController extends BaseController {

    private final IStlmtVendorBillLineService iStlmtVendorBillLineService;


    /**
     * 查询供应商对账明细列表
     */
    @SaCheckPermission("oms:vendorBillLine:list")
    @GetMapping("/list")
    public TableDataInfo<StlmtVendorBillLineVo> list(StlmtVendorBillLineBo bo) {
        return iStlmtVendorBillLineService.queryPageList(bo);
    }


    /**
     * 供应商对账单-添加列表-采购单据
     */
    @SaCheckPermission("oms:vendorBillLine:pagePurchaseEdit")
    @GetMapping("/list/pagePurchaseEdit")
    public TableDataInfo<StlmtVendorBillPurchaseOrderVo> pagePurchaseEdit(StlmtVendorBillPurchaseBo bo) {
        return iStlmtVendorBillLineService.getPagePurchaseEdit(bo);
    }

    /**
     * 供应商对账单-添加列表-采购单据-合计 -- 废弃， 从前台去统计
     */
    @SaCheckPermission("oms:vendorBillLine:pagePurchaseEditTotalAmount")
    @GetMapping("/list/pagePurchaseEdit/getTotalAmount")
    public StlmtVendorBillPurchaseAmountVo getPagePurchaseEditTotalAmount(StlmtVendorBillPurchaseBo bo) {
        return iStlmtVendorBillLineService.getPagePurchaseEditTotalAmount(bo);
    }


    /**
     * 供应商对账单-添加列表-采购商品
     */
    @SaCheckPermission("oms:vendorBillLine:pagePurchaseGoodsEdit")
    @GetMapping("/list/pagePurchaseGoodsEdit")
    public TableDataInfo<StlmtVendorBillPurchaseOrderLineVo> pagePurchaseGoodsEdit(StlmtVendorBillPurchaseBo bo) {
        return iStlmtVendorBillLineService.getPagePurchaseGoodsEdit(bo);
    }

    /**
     * 供应商对账单-添加列表-采购单据-合计
     */
    @SaCheckPermission("oms:vendorBillLine:pagePurchaseGoodsEditTotalAmount")
    @GetMapping("/list/pagePurchaseGoodsEdit/getTotalAmount")
    public StlmtVendorBillPurchaseAmountVo getPagePurchaseGoodsEditTotalAmount(StlmtVendorBillPurchaseBo bo) {
        return iStlmtVendorBillLineService.getPagePurchaseGoodsEditTotalAmount(bo);
    }

    /**
     * 供应商对账单-详情-采购单据列表
     */
    @SaCheckPermission("oms:vendorBillLine:pageLineOrder")
    @GetMapping("/list/pageLineOrder")
    public TableDataInfo<StlmtVendorBillPurchaseOrderLineVo> pageLineOrder(StlmtVendorBillLineQueryBo request) {
        return iStlmtVendorBillLineService.pageLineOrder(request);
    }

    /**
     * 供应商对账单-详情-采购商品行列表
     */
    @SaCheckPermission("oms:vendorBillLine:pageLineGoods")
    @GetMapping("/list/pageLineGoods")
    public TableDataInfo<StlmtVendorBillPurchaseOrderLineVo> pageLineGoods(StlmtVendorBillLineQueryBo request) {
        return iStlmtVendorBillLineService.pageLineGoods(request);
    }



    /**
     * 供应商对账单-采购单据新增
     */
    @SaCheckPermission("oms:vendorBillLine:createPurchaseLine")
    @Log(title = "供应商对账单-采购单据新增", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/createPurchase")
    public R<Void> createPurchase(@Validated(AddGroup.class) @RequestBody StlmtVendorBillOrderOperateCreateBo bo) {
        try {
            return toAjax(iStlmtVendorBillLineService.createPurchase(bo));
        }catch (ServiceException e){
            return R.fail(e.getMessage());
        }catch (Exception e){
            log.error("供应商对账单-采购单据新增失败 param:{},case:{}", JSON.toJSONString(bo), Throwables.getStackTraceAsString(e));
            return R.fail("供应商对账单-采购单据新增失败");
        }
    }


    /**
     * 供应商对账单-采购商品新增
     */
    @SaCheckPermission("oms:vendorBillLine:createPurchaseLine")
    @Log(title = "供应商对账单-采购商品新增", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/createPurchaseLine")
    public R<Void> createPurchaseLine(@Validated(AddGroup.class) @RequestBody StlmtVendorBillOrderOperateCreateBo bo) {
        try {
            return toAjax(iStlmtVendorBillLineService.createPurchaseLine(bo));
        }catch (ServiceException e){
            return R.fail(e.getMessage());
        }catch (Exception e){
            log.error("供应商对账单-采购商品新增失败 param:{},case:{}", JSON.toJSONString(bo), Throwables.getStackTraceAsString(e));
            return R.fail("供应商对账单-采购商品新增失败");
        }
    }



    /**
     * 删除供应商对账单采购订单
     */
    @SaCheckPermission("oms:vendorBillLine:deletePurchase")
    @Log(title = "删除供应商对账单采购订单", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/deletePurchase")
    public R<Void> deletePurchase(@Validated(AddGroup.class) @RequestBody StlmtVendorBillOrderDelBo bo) {
        try {
            return toAjax(iStlmtVendorBillLineService.deletePurchase(bo));
        }catch (ServiceException e){
            log.error("删除供应商对账单采购订单失败 param:{},case:{}", JSON.toJSONString(bo), Throwables.getStackTraceAsString(e));
            return R.fail(e.getMessage());
        }catch (Exception e){
            log.error("删除供应商对账单采购订单失败 param:{},case:{}", JSON.toJSONString(bo), Throwables.getStackTraceAsString(e));
            return R.fail("删除供应商对账单采购订单失败");
        }
    }

    /**
     * 删除供应商对账单采购订单商品
     */
    @SaCheckPermission("oms:vendorBillLine:deletePurchaseLine")
    @Log(title = "删除供应商对账单采购订单商品", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/deletePurchaseLine")
    public R<Void> deletePurchaseLine(@Validated(AddGroup.class) @RequestBody StlmtVendorBillOrderDelBo bo) {
        try {
            return toAjax(iStlmtVendorBillLineService.deletePurchaseLine(bo));
        }catch (ServiceException e){
            log.error("删除供应商对账单采购订单商品失败 param:{},case:{}", JSON.toJSONString(bo), Throwables.getStackTraceAsString(e));
            return R.fail(e.getMessage());
        }catch (Exception e){
            log.error("删除供应商对账单采购订单商品失败 param:{},case:{}", JSON.toJSONString(bo), Throwables.getStackTraceAsString(e));
            return R.fail("删除供应商对账单采购订单商品失败");
        }
    }




    /**
     * 导出供应商对账明细列表
     */
    @SaCheckPermission("oms:vendorBillLine:export")
    @Log(title = "导出供应商对账明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(StlmtVendorBillLineBo bo, HttpServletResponse response) {
        List<StlmtVendorBillLineVo> list = iStlmtVendorBillLineService.queryList(bo);
        ExcelUtil.exportExcel(list, "供应商对账明细", StlmtVendorBillLineVo.class, response);
    }

    /**
     * 获取供应商对账明细详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("oms:vendorBillLine:query")
    @GetMapping("/{id}")
    public R<StlmtVendorBillLineVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iStlmtVendorBillLineService.queryById(id));
    }

    /**
     * 新增供应商对账明细
     */
    @SaCheckPermission("oms:vendorBillLine:add")
    @Log(title = "新增供应商对账明细", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody StlmtVendorBillLineBo bo) {
        return toAjax(iStlmtVendorBillLineService.insertByBo(bo));
    }

    /**
     * 修改供应商对账明细
     */
    @SaCheckPermission("oms:vendorBillLine:edit")
    @Log(title = "修改供应商对账明细", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody StlmtVendorBillLineBo bo) {
        return toAjax(iStlmtVendorBillLineService.updateByBo(bo));
    }
}
