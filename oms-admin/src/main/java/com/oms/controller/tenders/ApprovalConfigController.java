package com.oms.controller.tenders;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.google.api.client.repackaged.com.google.common.base.Throwables;
import com.oms.common.annotation.Log;
import com.oms.common.annotation.RepeatSubmit;
import com.oms.common.core.controller.BaseController;
import com.oms.common.core.domain.R;
import com.oms.common.core.page.TableDataInfo;
import com.oms.common.core.validate.AddGroup;
import com.oms.common.core.validate.EditGroup;
import com.oms.common.enums.BusinessType;
import com.oms.common.exception.ServiceException;
import com.oms.common.utils.poi.ExcelUtil;
import com.oms.domain.bo.ApprovalConfigBo;
import com.oms.domain.vo.ApprovalConfigVo;
import com.oms.service.IApprovalConfigService;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;


/**
 * 审批配置
 *
 * <AUTHOR>
 * @date 2024-10-15
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/tenders/approvalConfig")
public class ApprovalConfigController extends BaseController {

    private final IApprovalConfigService iApprovalConfigService;

    /**
     * 查询审批配置列表
     */
    @SaCheckPermission("@ss.hasPermi('tenders:approvalConfig:list')")
    @GetMapping("/list")
    public TableDataInfo<ApprovalConfigVo> list(ApprovalConfigBo bo) {
        return iApprovalConfigService.queryPageList(bo);
    }


    /**
     * 新增审批配置
     */
    @SaCheckPermission("@ss.hasPermi('tenders:approvalConfig:add')")
    @Log(title = "审批配置新增", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/add")
    public R add(@Validated(AddGroup.class) @RequestBody ApprovalConfigBo bo) {
        try {
            return toAjax(iApprovalConfigService.insertByBo(bo));
        }catch (ServiceException e){
            return R.fail("审批配置新增失败:" + e.getMessage());
        }catch (Exception e){
            log.error("审批配置新增 param:{},case:{}", JSONUtil.toJsonStr(bo), Throwables.getStackTraceAsString(e));
            return R.fail("审批配置新增失败");
        }
    }


    /**
     * 修改审批配置
     */
    @SaCheckPermission("@ss.hasPermi('tenders:approvalConfig:edit')")
    @Log(title = "审批配置修改", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/edit")
    public R edit(@Validated(EditGroup.class) @RequestBody ApprovalConfigBo bo) {
        try {
            return toAjax(iApprovalConfigService.updateByBo(bo));
        }catch (ServiceException e){
            return R.fail("审批配置修改失败:" + e.getMessage());
        }catch (Exception e){
            log.error("审批配置修改 param:{},case:{}", JSONUtil.toJsonStr(bo), Throwables.getStackTraceAsString(e));
            return R.fail("审批配置修改失败");
        }
    }




    /**
     * 获取审批配置详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("@ss.hasPermi('tenders:approvalConfig:query')")
    @GetMapping("/{id}")
    public R<ApprovalConfigVo> getInfo(@NotNull(message = "主键不能为空")
                                       @PathVariable Long id) {
        try {
            return R.ok(iApprovalConfigService.queryById(id));
        }catch (ServiceException e){
            return R.fail("获取审批配置详细信息失败:" + e.getMessage());
        }catch (Exception e){
            log.error("获取审批配置详细信息 param:{},case:{}", id, Throwables.getStackTraceAsString(e));
            return R.fail("获取审批配置详细信息失败");
        }
    }


    /**
     * 删除审批配置
     *
     * @param ids 主键串
     */
    @SaCheckPermission("@ss.hasPermi('tenders:approvalConfig:remove')")
    @Log(title = "审批配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R remove(@NotEmpty(message = "主键不能为空")
                             @PathVariable Long[] ids) {
        return toAjax(iApprovalConfigService.deleteWithValidByIds(Arrays.asList(ids), true));
    }



     /**
     * 审批配置批量调整状态
     */
    @SaCheckPermission("@ss.hasPermi('tenders:approvalConfig:batchChangeStatus')")
    @Log(title = "审批配置批量调整状态", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/batchChangeStatus")
    public R batchChangeStatus(
            @RequestParam(value="ids") List<Long> ids, @RequestParam(value="status") String status) {
        try {
            return toAjax(iApprovalConfigService.batchChangeStatus(ids, status));
        }catch (ServiceException e){
            return R.fail("审批配置批量调整状态失败:" + e.getMessage());
        }catch (Exception e){
            log.error("审批配置批量调整状态 param:{}, {},case:{}", ids, status , Throwables.getStackTraceAsString(e));
            return R.fail("审批配置批量调整状态失败");
        }
    }


    /**
     * 审批配置调整状态
     */
    @SaCheckPermission("@ss.hasPermi('tenders:approvalConfig:ChangeStatus')")
    @Log(title = "审批配置调整状态", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/ChangeStatus")
    public R ChangeStatus(@RequestParam(value="id") Long id, @RequestParam(value="status") String status) {
        try {
            return toAjax(iApprovalConfigService.changeStatus(id, status));
        }catch (ServiceException e){
            return R.fail("审批配置调整状态失败:" + e.getMessage());
        }catch (Exception e){
            log.error("审批配置调整状态 param:{}, {},case:{}", id, status , Throwables.getStackTraceAsString(e));
            return R.fail("审批配置调整状态失败");
        }
    }


    /**
     * 导出审批配置列表
     */
    @SneakyThrows
    @SaCheckPermission("@ss.hasPermi('tenders:approvalConfig:export')")
    @Log(title = "审批配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(ApprovalConfigBo bo, HttpServletResponse response) {
        List<ApprovalConfigVo> list = iApprovalConfigService.queryList(bo);
        ExcelUtil.exportExcel(list, "审批配置", ApprovalConfigVo.class, response);
    }

}
