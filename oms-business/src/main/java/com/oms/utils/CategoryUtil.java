package com.oms.utils;

import cn.hutool.core.collection.CollectionUtil;
import com.google.api.client.util.Lists;
import com.oms.domain.vo.GoodsBackCategoryVo;

import java.util.List;
import java.util.Map;

public class CategoryUtil {
    public static String getItemCategoryNames(Map<Long, GoodsBackCategoryVo> categoryVoMap, Long cateId){
        if(CollectionUtil.isEmpty(categoryVoMap) || cateId == null){
            return "";
        }
        Long thisCateId = cateId;
        List<String> cateNameList = Lists.newArrayList();
        while (true){
            GoodsBackCategoryVo categoryVo = categoryVoMap.get(cateId);
            if(categoryVo != null){
                cateNameList.add(categoryVo.getCateName());
                cateId = categoryVo.getPid();
            }else{
                break;
            }
        }
        StringBuilder cateNames = new StringBuilder();
        if(cateNameList.size() > 0){
            for (int i = cateNameList.size() - 1 ; i >= 0; i--) {
                if(cateNames.length() > 0){
                    cateNames.append(">");
                }
                cateNames.append(cateNameList.get(i));
            }
        }
        cateNames.append("(").append(thisCateId).append(")");
        return cateNames.toString();
    }


    public static List<Long> getItemCategoryIds(Map<Long, GoodsBackCategoryVo> categoryVoMap, Long cateId){
        List<Long> cateIds = Lists.newArrayList();
        if(CollectionUtil.isEmpty(categoryVoMap) || cateId == null){
            return cateIds;
        }
        while (true){
            GoodsBackCategoryVo categoryVo = categoryVoMap.get(cateId);
            if(categoryVo != null){
                cateIds.add(categoryVo.getId());
                cateId = categoryVo.getPid();
            }else{
                break;
            }
        }
        List<Long>  result = Lists.newArrayList();
        for (int i = cateIds.size() - 1 ; i >= 0; i--) {
            result.add(cateIds.get(i));
        }
        return result;
    }

}
