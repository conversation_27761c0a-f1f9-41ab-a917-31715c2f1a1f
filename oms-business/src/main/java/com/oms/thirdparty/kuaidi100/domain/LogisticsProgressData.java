/**
 * io.terminus
 * Copyright(c) 2012-2018 All Rights Reserved.
 */
package com.oms.thirdparty.kuaidi100.domain;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version Id:,v0.1 2018/6/4 下午2:26 sean Exp $
 * @description
 */
@Data
public class LogisticsProgressData implements Serializable {

    /** 时间，原始格式 */
    private String time;

    /** 内容 */
    private String context;

    /** 格式化后时间 */
    private String ftime;

    /** 行政区域的编码 */
    private String areaCode;

    /** 行政区域的名称 */
    private String areaName;

    /** 签收状态 */
    private String status;

    /** 签收状态码 */
    private String statusCode;

    /*经纬度*/
    private String areaCenter;
}
