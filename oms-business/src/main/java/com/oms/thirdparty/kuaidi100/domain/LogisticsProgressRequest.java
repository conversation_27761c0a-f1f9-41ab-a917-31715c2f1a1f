/**
 * io.terminus
 * Copyright(c) 2012-2018 All Rights Reserved.
 */
package com.oms.thirdparty.kuaidi100.domain;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
@Data
public class LogisticsProgressRequest implements Serializable {

    /** 快递公司编码 */
    private String com;

    /** 快递单号 */
    private String num;

    private String phone;

    /** 出发地城市 */
    private String from;

    /** 目的地城市 */
    private String to;

    /** 开通行政区域解析功能 */
    private String resultv2 = "1";
}
