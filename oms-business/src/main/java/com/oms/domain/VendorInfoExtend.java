package com.oms.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.oms.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 供应商扩展信息对象 vendor_info_extend
 *
 * <AUTHOR>
 * @date 2024-09-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("vendor_info_extend")
public class VendorInfoExtend extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 供应商id
     */
    private Long vendorId;
    /**
     * 店铺logo
     */
    private String logo;
    /**
     * 营业执照路径
     */
    private String businessLicenseUrl;
    /**
     * 营业执照有效开始时间
     */
    private Date businessLicenseStartDate;
    /**
     * 营业执照有效失效时间
     */
    private Date businessLicenseEndDate;
    /**
     * 营业执照有效期永久状态： 0-否 1-是
     */
    private Long businessLicenseFlag;
    /**
     * 身份证有效期开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String identityCardStartDate;

    /**
     * 身份证有效期结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String identityCardEndDate;

    /**
     * 身份证正面路径
     */
    private String frontIdentityCardUrl;

    /**
     * 身份证反面路径
     */
    private String reverseIdentityCardUrl;

    /**
     * 身份证有效期永久状态： 0-否 1-是
     */
    private Boolean identityCardPermanentFlag;

    /**
     * 开户许可证路径
     */
    private String accountOpeningLicenseUrl;
    /**
     * 法人
     */
    private String legalPerson;

    /**
     * 身份证号码
     */
    private String identityCardNumber;

    /**
     * 开票单位名称
     */
    private String invoiceCompanyName;
    /**
     * 纳税人类型 0-一般纳税人 1-小规模纳税人
     */
    private Integer taxpayerType;
    /**
     * 纳税人识别号
     */
    private String tin;
    /**
     * 开票注册地址
     */
    private String invoiceCompanyAddress;
    /**
     * 开票注册电话
     */
    private String invoiceTelephone;
    /**
     * 开票开户银行
     */
    private String invoiceBank;
    /**
     * 开票银行帐户
     */
    private String invoiceAccno;
    /**
     * 开票信息证明路径
     */
    private String invoiceInfoCertUrl;
    /**
     * 开户银行名称
     */
    private String bankName;
    /**
     * 开户支行名称
     */
    private String bankSubBranchName;
    /**
     * 银行账号
     */
    private String bankAccount;
    /**
     * 银行账户名
     */
    private String bankAccountName;
    /**
     * 银行代号/银联号
     */
    private String bankCode;
    /**
     * 评估表附件路径
     */
    private String assessFileUrl;
    /**
     * 默认退货省id
     */
    private Long defaultReturnProvinceId;
    /**
     * 默认退货市id
     */
    private Long defaultReturnCityId;
    /**
     * 默认退货区域id
     */
    private Long defaultReturnRegionId;
    /**
     * 默认退货街道id
     */
    private Long defaultReturnStreetId;
    /**
     * 默认退货省名称
     */
    private String defaultReturnProvinceName;
    /**
     * 默认退货市名称
     */
    private String defaultReturnCityName;
    /**
     * 默认退货区域名称
     */
    private String defaultReturnRegionName;
    /**
     * 默认退货街道名称
     */
    private String defaultReturnStreetName;
    /**
     * 默认退货详细地址
     */
    private String defaultReturnAddress;
    /**
     * 默认退货联系人
     */
    private String defaultReturnContacts;
    /**
     * 默认退货联系电话
     */
    private String defaultReturnContactsMobile;

    /**
     * 仓库面积(平方)
     */
    private String warehouseArea;

    /**
     * 运输车辆(辆)
     */
    private String  transportVehicles ;

    /**
     * 客服(人)
     */
    private String   customerService ;

    /**
     * 快递/物流
     */
    private String  expressLogistics ;

    /**
     * 仓库证明材料路径
     */
    private String warehouseCertUrl;

    /**
     * 物流证明材料
     */
    private String logisticsCertUrl;

    /**
     * 一般纳税人资格证或三个月开票的发票复印件路径
     */
    private String taxpayerOrInvoiceCertUrl;

    /**
     * 默认发货省id
     */
    private String vendorShipProvinceId;
    /**
     * 默认发货市id
     */
    private String vendorShipCityId;

    /**
     * 默认发货区县id
     */
    private String vendorShipRegionId;

    /**
     * 默认发货联系人
     */
    private String vendorShipContacts;

    /**
     * 默认发货联系电话
     */
    private String vendorShipContactsMobile;
    /**
     * 发货省份名称
     */
    private String vendorShipProvinceName;
    /**
     * 发货市名称
     */
    private String vendorShipCityName;
    /**
     * 发货区县名称
     */
    private String vendorShipRegionName;

    /**
     * 默认发货镇id
     */
    private Long vendorShipStreetId	;

    /**
     * 默认发货镇
     */
    private String vendorShipStreetName	;

    /**
     * 发货详细地址
     */
    private String vendorShipAddress;

    /**
     * 发货专用章路径
     */
    private String shippingSealUrl ;

    /**
     * 合同附件路径
     */
    private String contractUrl;


}
