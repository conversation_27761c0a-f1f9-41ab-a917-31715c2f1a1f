package com.oms.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.oms.common.core.domain.BaseEntity;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 结算-销售调价对象 stlmt_seller_price_adj
 *
 * <AUTHOR>
 * @date 2025-01-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("stlmt_seller_price_adj")
public class StlmtSellerPriceAdj extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 调价编号
     */
    private String code;
    /**
     * 项目ID
     */
    private Long projectId;
    /**
     * 项目名称
     */
    private String projectName;
    /**
     * 提交人
     */
    private Long commitedBy;
    /**
     * 提交人名称
     */
    private String commitedName;
    /**
     * 提交时间
     */
    private Date commitedAt;
    /**
     * 审核人
     */
    private Long auditBy;
    /**
     * 审核人名称
     */
    private String auditName;
    /**
     * 审核时间
     */
    private Date auditAt;

    /**
     * 审核人消息
     */
    private String auditMsg;
    /**
     * 状态 0-待提交 1-待审核 2-审核通过 3-审核不通过
     */
    private Integer status;
    /**
     * 调价时间
     */
    private Date adjustTime;
    /**
     * 送货客户ID
     */
    private Long deliverCustomerId;
    /**
     * 送货客户名称
     */
    private String deliverCustomerName;
    /**
     * 开票客户ID
     */
    private Long invoiceCustomerId;
    /**
     * 开票客户名称
     */
    private String invoiceCustomerName;
    /**
     * 备注
     */
    private String remark;


}
