package com.oms.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.oms.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 接口投标单对象 tenders_interface
 *
 * <AUTHOR>
 * @date 2024-10-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tenders_interface")
public class TendersInterface extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 游标
     */
    private String cursorMark;
    /**
     * 信息id
     */
    private String infoId;
    /**
     * 信息标题
     */
    private String infoTitle;
    /**
     * 信息内容
     */
    private String infoContent;

    /**
     * 省份id
     */
    private Long provinceId;

    /**
     * 省份名称
     */
    private String provinceName;

    /**
     * 市名称
     */
    private String cityName;

    /**
     * 区县名称
     */
    private String districtName;

    /**
     * 状态  0-未转内部投标单 1-已转内部投标单
     */
    private Integer status;
    /**
     * 信息类型 0：公告 1：预告 2：变更 3：结果 5：其他
     */
    private Integer infoType;
    /**
     * 二级信息类型细分：  1：意见征集  2：招标预告  3：招标公告  4：重新招标  5：信息变更  6：答疑公告  7：废标公告  8：流标公告  9：开标公示  10：候选人公示  11：中标通知  12：合同公告  13：验收合同  14：违规公告  15：其他公告
     */
    private Integer infoTypeSegment;
    /**
     * 发布时间
     */
    private Date infoPublishTime;
    /**
     * 招标编号
     */
    private String tendersNumber;
    /**
     * 标书获取开始时间
     */
    private Date bidingAcquireTime;
    /**
     * 标书截止时间
     */
    private Date bidingEndTime;
    /**
     * 投标开始时间
     */
    private Date tenderBeginTime;
    /**
     * 投标截止时间
     */
    private Date tenderEndTime;
    /**
     * 开标时间
     */
    private Date openBidingTime;
    /**
     * 招标方式  0：公开招标  1：邀请招标  2：竞争性谈判或竞争性磋商  3：单一来源采购  4：询价  5：国务院政府采购监督管理部门认定的其他采购方式  6：电子反拍
     */
    private Integer biddingType;
    /**
     * 是否电子招标  1：是  0：否
     */
    private Integer isElectronic;
    /**
     * 招标单位
     */
    private String tenderCompany;
    /**
     * 招标单位联系人
     */
    private String tenderRelationName;
    /**
     * 招标单位联系方式
     */
    private String tenderRelationWay;
    /**
     * 代理机构
     */
    private String agentUnit;
    /**
     * 代理机构联系人
     */
    private String agentRelationName;
    /**
     * 代理机构联系方式
     */
    private String agentRelationWay;
    /**
     * 中标单位
     */
    private String winBiddingUnit;
    /**
     * 中标单位联系人
     */
    private String winBiddingRelationName;
    /**
     * 中标单位联系方式
     */
    private String winBiddingRelationWay;
    /**
     * 预算金额
     */
    private BigDecimal bidBudget;
    /**
     * 中标金额
     */
    private BigDecimal bidWinnerAmount;
    /**
     * 资金来源
     */
    private String fundsSource;
    /**
     * 评标专家
     */
    private String bidEvaluationExpert;
    /**
     * 附件
     */
    private String fileList;
    /**
     * 所属区域
     */
    private String belongingArea;
    /**
     * 物资分类
     */
    private String materialCategory;
    /**
     * 支撑部门
     */
    private String supportDepartment;
    /**
     * 项目等级
     */
    private Integer projectLevel;

    /**
     * 行业
     */
    private String industry ;

    /**
     * 中标公司数量
     */
    private Integer companyNumber;

    /**
     * 招标公告地址
     */
    private String websiteUrl;

    /**
     * 扩展数据
     */
    private String extraData;

    /**
     * 邮件发送状态 0-待发送, 1-发送成功 2-发送失败
     */
    private Integer emailSendStatus;

    /**
     * 邮件发送消息
     */
    private String emailSendMsg;

    /**
     * 全量邮件发送状态 0-待发送, 1-发送成功 2-发送失败
     */
    private Integer allEmailSendStatus;

    /**
     * 全量邮件发送消息
     */
    private String allEmailSendMsg;


}
