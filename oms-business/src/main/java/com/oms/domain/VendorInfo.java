package com.oms.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.oms.common.core.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.NoArgsConstructor;

/**
 * 供应商信息对象 vendor_info
 *
 * <AUTHOR>
 * @date 2024-09-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@TableName("vendor_info")
public class VendorInfo extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 供应商名称
     */
    private String name;
    /**
     * 供应商简称
     */
    private String shortName;
    /**
     * 状态 (正常-NOR，关闭-DIS)
     */
    private String status;

    /**
     * 父供应商id
     */
    private Long parentId;

    /**
     * 类型 1-供应商 2-服务商
     */
    private Integer type ;
    /**
     * 联系电话
     */
    private String phone;
    /**
     * 电子邮件
     */
    private String email;
    /**
     * 联系人
     */
    private String contactName;
    /**
     * 统一社会信用代码
     */
    private String unifiedSocialCreditCode;

    /**
     * 是否自动确认采购单  0-否 1-是
     */
    private Integer isAutoConfirmPurchaseOrder;

    /**
     * 是否自动客服验收发货  0-否 1-是
     */
    private Integer isCheckDelivery;

    /**
     * 是否需要保证金 0-否 1-是
     */
    private Integer isDeposit;

    /**
     * 保证金金额
     */
    private BigDecimal depositAmount;

    /**
     * 是否需要违约金 0-否 1-是
     */
    private Integer isPenalty;

    /**
     * 违约金金额
     */
    private BigDecimal penaltyAmount;

    /**
     * 对账状态 0-未收款对账 1-已收款对账
     */
    private Integer isBillStatus ;


    /**
     * 审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date auditDate;

    /**
     * 第三方供应商编码
     */
    private String thirdVendorNo;

    /**
     * 第三方地区号
     */
    private String thirdRegionCode;

    /**
     * 是否有保底销售金额 0-否 1-是
     */
    private Integer isLowSalePrice;

    /**
     * 保底销售金额
     */
    private BigDecimal lowSalePrice;

    /**
     * 是否有奖励条款 0-否 1-是
     */
    private Integer isRewardTerms;

    /**
     * 奖励条款
     */
    private String rewardTerms;

    /**
     * 是否签订合同 0-否 1-是
     */
    private Integer isSignContract ;

}
