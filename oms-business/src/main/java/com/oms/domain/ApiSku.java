package com.oms.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.oms.common.core.domain.BaseEntity;

import java.math.BigDecimal;

/**
 * api sku对象 api_sku
 *
 * <AUTHOR>
 * @date 2025-05-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("api_sku")
public class ApiSku extends BaseEntity {

    private static final long serialVersionUID=1L;
    /**
     * 项目id
     */
    private Long prjId;
    /**
     * 项目名称
     */
    private String prjName;
    /**
     * api商品id
     */
    private Long apiItemId;
    /**
     * skuid
     */
    private Long skuId;
    /**
     * 外部skuid
     */
    private String outSkuId;
    /**
     * sku编码
     */
    private String skuCode;
    /**
     * sku条码
     */
    private String barcode;
    /**
     * 规格
     */
    private String specification;
    /**
     * 型号
     */
    private String communityModel;
    /**
     * 官网价
     */
    private BigDecimal officialPrice;
    /**
     * 销售价
     */
    private BigDecimal salePrice;
    /**
     * 市场价
     */
    private BigDecimal marketPrice;
    /**
     * 组合品明细{skuid:数量}
     */
    private String skuJson;
    /**
     * 起售数量
     */
    private Long minQuantity;
    /**
     * 起售倍数
     */
    private Long salesMultiple;
}
