package com.oms.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.oms.common.core.domain.BaseEntity;

import java.math.BigDecimal;

/**
 * 供应商对账明细对象 stlmt_vendor_bill_line
 *
 * <AUTHOR>
 * @date 2025-02-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("stlmt_vendor_bill_line")
public class StlmtVendorBillLine extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 项目id
     */
    private Long projectId;
    /**
     * 项目名称
     */
    private String projectName;
    /**
     * 供应商对账抬头ID
     */
    private Long vendorBillId;
    /**
     * 采购单据头表ID
     */
    private Long stlmtPurchaseOrderId;
    /**
     * 采购单据行表ID
     */
    private Long stlmtPurchaseOrderLineId;
    /**
     * 采购订单
     */
    private Long purchaseOrderId;
    /**
     * 包裹单id
     */
    private Long packageOrderId;
    /**
     * 外部订单
     */
    private String outId;
    /**
     * 销售订单
     */
    private Long sellerOrderId;
    /**
     * 订单类型;0:服务商 1:自营
     */
    private Integer orderType;
    /**
     * 销售类型;1:采购 2:采退
     */
    private Integer sellType;
    /**
     * 单据类型;1:采购订单?2：采退订单 3：成本调价
     */
    private Integer documentType;
    /**
     * 商品id
     */
    private Long itemId;
    /**
     * 商品名称
     */
    private String itemName;
    /**
     * 商品SKUID
     */
    private Long skuId;
    /**
     * 商品SKU编码
     */
    private String skuCode;
    /**
     * 商品名称
     */
    private String skuName;
    /**
     * 计价单位
     */
    private String pricingUnit;
    /**
     * 税率
     */
    private BigDecimal taxRate;
    /**
     * 对账金额
     */
    private BigDecimal reconciledAmount;
}
