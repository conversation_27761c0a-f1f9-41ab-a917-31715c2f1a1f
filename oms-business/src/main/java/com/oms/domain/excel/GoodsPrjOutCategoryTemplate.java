package com.oms.domain.excel;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.oms.common.core.domain.BaseEntityVo;
import com.oms.common.core.validate.AddGroup;
import com.oms.common.core.validate.EditGroup;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * 项目外部类目导入模板
 *
 * <AUTHOR>
 * @date 2025-02-14
 */
@Data
@ExcelIgnoreUnannotated
public class GoodsPrjOutCategoryTemplate implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 项目名称
     */
    @ExcelProperty(value = "项目名称")
    @NotBlank(message = "项目名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String prjName;
    /**
     * 外部分类id
     */
    @ExcelProperty(value = "外部分类id")
    @NotBlank(message = "外部分类id不能为空", groups = { AddGroup.class, EditGroup.class })
    private String outId;

    /**
     * 外部分类父id
     */
    @ExcelProperty(value = "外部分类父id")
    private String outPid;

    /**
     * 外部类目名称
     */
    @ExcelProperty(value = "外部类目名称")
    @NotBlank(message = "外部类目名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String cateName;

    /**
     * 绑定后台类目名称 格式 名称(id),名称(id)
     */
    @ExcelProperty(value = "绑定后台类目名称")
    private String backCateNames;
}
