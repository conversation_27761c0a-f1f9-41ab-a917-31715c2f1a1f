package com.oms.domain.excel;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.oms.common.core.validate.AddGroup;
import com.oms.common.core.validate.EditGroup;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 项目商品导入模板
 *
 * <AUTHOR>
 * @date 2025-02-14
 */
@Data
@ExcelIgnoreUnannotated
public class GoodsItemTemplate implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 项目名称
     */
    @ExcelProperty(value = "项目名称")
    @NotBlank(message = "项目名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String prjName;
    /**
     * 商品分类
     */
    @ExcelProperty(value = "商品分类")
    @NotBlank(message = "商品分类不能为空", groups = { AddGroup.class, EditGroup.class })
    private String backCateName;

    /**
     * 外部分类名称
     */
    @ExcelProperty(value = "外部分类名称")
    @NotBlank(message = "外部分类名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String outCateName;

    /**
     * sku编码
     */
    @ExcelProperty(value = "sku编码")
    @NotBlank(message = "sku编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String skuCode;

    /**
     * 条形码
     */
    @ExcelProperty(value = "条形码")
    @NotBlank(message = "条形码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String barcode;

    /**
     * 通用名称
     */
    @ExcelProperty(value = "通用名称")
    @NotBlank(message = "通用名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String universalName;

    /**
     * 品牌名称
     */
    @ExcelProperty(value = "品牌名称")
    @NotBlank(message = "品牌名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String brandName;

    /**
     * 计量单位名称
     */
    @ExcelProperty(value = "计量单位名称")
    @NotBlank(message = "计量单位名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String unit;

    /**
     * 规格
     */
    @ExcelProperty(value = "规格")
    private String specification;
    /**
     * 型号
     */
    @ExcelProperty(value = "型号")
    private String communityModel;

    /**
     * 颜色
     */
    @ExcelProperty(value = "颜色")
    private String itemColor;

    /**
     * 税率
     */
    @ExcelProperty(value = "税率")
    @NotBlank(message = "税率不能为空", groups = { AddGroup.class, EditGroup.class })
    private String vatrate;

    /**
     * 税收分类编码
     */
    @ExcelProperty(value = "税收分类编码")
    @NotBlank(message = "税收分类编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String taxCode;

    /**
     * 比价链接1
     */
    @ExcelProperty(value = "比价链接1")
    private String bjUrl1;

    /**
     * 比价链接2
     */
    @ExcelProperty(value = "比价链接2")
    private String bjUrl2;

    /**
     * 比价链接3
     */
    @ExcelProperty(value = "比价链接3")
    private String bjUrl3;
    /**
     * 客户专用描述信息
     */
    @ExcelProperty(value = "客户专用描述信息")
    private String csrDesc;
    /**
     * 成本价
     */
    @ExcelProperty(value = "成本价")
    @NotBlank(message = "成本价不能为空", groups = { AddGroup.class, EditGroup.class })
    private String basePrice;
    /**
     * 市场价
     */
    @ExcelProperty(value = "市场价")
    @NotBlank(message = "市场价不能为空", groups = { AddGroup.class, EditGroup.class })
    private String marketPrice;
    /**
     * 销售价
     */
    @ExcelProperty(value = "销售价")
    @NotBlank(message = "销售价不能为空", groups = { AddGroup.class, EditGroup.class })
    private String salePrice;
    /**
     * 包装清单
     */
    @ExcelProperty(value = "包装清单")
    @NotBlank(message = "包装清单不能为空", groups = { AddGroup.class, EditGroup.class })
    private String packing;
    /**
     * 商品副标题
     */
    @ExcelProperty(value = "商品副标题")
    private String titleName;
    /**
     * 商品详情
     */
    @ExcelProperty(value = "商品详情")
    private String detail;
}
