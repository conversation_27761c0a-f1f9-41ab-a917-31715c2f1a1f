package com.oms.domain.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 供应商付款单导入模板类
 */
@Data
public class StlmtVendorPaymentTemplate {
    @ExcelProperty(value = "*供应商")
    @NotBlank(message = "供应商不能为空")
    private String vendor;

    @ExcelProperty(value = "*客户发票号")
    @NotBlank(message = "客户发票号不能为空")
    private String invoiceNo;

    @ExcelProperty(value = "*付款金额")
    @NotNull(message = "付款金额不能为空")
    private BigDecimal paymentAmount;

    @ExcelProperty(value = "*付款日期")
    @NotNull(message = "付款日期不能为空")
    private Date paymentDate;

    @ExcelProperty(value = "*收款人")
    @NotBlank(message = "收款人不能为空")
    private String payeeName;

    @ExcelProperty(value = "收款银行账号")
    private String payeeAccount;

    @ExcelProperty(value = "收款银行名称")
    private String payeeBank;

    @ExcelProperty(value = "付款备注")
    private String paymentRemark;

    @ExcelProperty(value = "付款类型")
    private String paymentType;

//    @ExcelProperty(value = "*导入类型（1.外部单号，2.发货单号，3.采购单号）")
//    @NotBlank(message = "导入类型不能为空")
//    private String importType;
//
//    @ExcelProperty(value = "*单号")
//    @NotBlank(message = "单号不能为空")
//    private String orderNo;
//
//    @ExcelProperty(value = "SKUID")
//    private String skuId;
}
