package com.oms.domain.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class StlmtCustomerPaymentTemplate {

//    @ExcelProperty(value = "项目ID(必填)")
//    private Long prjId;

    @ExcelProperty(value = "*项目名称")
    private String prjName;

    @ExcelProperty(value = "*客户名称")
    private String customerName;

    @ExcelProperty(value = "*发票号")
    private String invoiceNo;

    @ExcelProperty(value = "*收款日期")
    private Date paymentDate;

    @ExcelProperty(value = "*付款单位")
    private String payerName;

    @ExcelProperty(value = "付款账号")
    private String payerAccount;

    @ExcelProperty(value = "付款银行")
    private String payerBank;

    @ExcelProperty(value = "*收款金额")
    private BigDecimal paymentAmount;

    @ExcelProperty(value = "手续费")
    private BigDecimal fee;

    @ExcelProperty(value = "尾差")
    private BigDecimal balanceDiff;

    @ExcelProperty(value = "收款备注")
    private String paymentRemark;

    @ExcelProperty(value = "银行流水号")
    private String txnSn;

    @ExcelProperty(value = "附件地址")
    private String attachmentUrl;
}
