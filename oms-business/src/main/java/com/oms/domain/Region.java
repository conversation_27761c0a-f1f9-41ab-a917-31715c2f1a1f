package com.oms.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.oms.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 系统地区对象 region
 *
 * <AUTHOR>
 * @date 2024-10-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("region")
public class Region extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 父id
     */
    private Long pid;
    /**
     * 国家编码(cn-中国)countrycode
     */
    private String countryCode;
    /**
     * 地区编号
     */
    private String regionCode;
    /**
     * 地区名称
     */
    private String regionName;
    /**
     * 地区缩写
     */
    private String regionAbbreviation;
    /**
     * 中文名
     */
    private String regionNameI18n;
    /**
     * 显示名
     */
    private String regionNameLocal;
    /**
     * 层级1-国家，2-省，3-市，4-区 5-街道
     */
    private Long regionLevel;
    /**
     * 显示顺序，正序显示
     */
    private Long orderNum;
    /**
     * 状态 (正常-NOR，关闭-DIS)
     */
    private String status;

}
