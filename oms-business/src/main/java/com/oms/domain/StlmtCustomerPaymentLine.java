package com.oms.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.oms.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 客户收款明细对象 stlmt_customer_payment_line
 *
 * <AUTHOR>
 * @date 2025-02-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("stlmt_customer_payment_line")
public class StlmtCustomerPaymentLine extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 收款单编号
     */
    private String paymentNo;
    /**
     * 销售单号
     */
    private String sellerOrderNo;
    /**
     * 销售单行号
     */
    private String sellerOrderLineNo;
    /**
     * 发货单号
     */
    private String deliveryOrderNo;
    /**
     * 外部单号
     */
    private String outId;
    /**
     * 客户名称
     */
    private String customerName;
    /**
     * 商品ID
     */
    private Long itemId;
    /**
     * 商品SKUID
     */
    private Long skuId;
    /**
     * 商品SKU名称
     */
    private String skuName;
    /**
     * 商品SKU编码
     */
    private String skuCode;
    /**
     * 商品图片URL
     */
    private String itemImage;
    /**
     * 商品名称
     */
    private String itemName;
    /**
     * 销售数量
     */
    private BigDecimal salesQuantity;
    /**
     * 发货数量
     */
    private BigDecimal deliveryQuantity;
    /**
     * 销售金额
     */
    private BigDecimal salesAmount;
    /**
     * 发货金额
     */
    private BigDecimal deliveryAmount;
    /**
     * 已收金额
     */
    private BigDecimal receivedAmount;

//    /**
//     * 结算销售单据ID 对应StlmtSellerOrder的id
//     */
//    private Long sellerOrderHId;
//
//    /**
//     * 结算销售单据行ID 对应StlmtSellerOrderDtl的id
//     */
//    private Long sellerOrderLId;

    /**
     * ID列表以逗号分隔,销售单据返回
     */
    @TableField(exist = false)
    private String ids;

}
