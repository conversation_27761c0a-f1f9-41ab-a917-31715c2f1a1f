package com.oms.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.oms.common.core.domain.BaseEntity;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.ibatis.annotations.Insert;

/**
 * 导入任务对象 sys_async_import
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_async_import")
public class SysAsyncImport extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 主键id
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 项目id
     */
    private Long prjId;
    /**
     * 项目名称
     */
    private String prjName;
    /**
     * 供应商id
     */
    private Long vendorId;
    /**
     * 供应商名称
     */
    private String vendorName;
    /**
     * 导入类型
     */
    private String importType;
    /**
     * 导入参数
     */
    private String importParam;
    /**
     * 导入文件
     */
    private String importFile;
    /**
     * 导入时长
     */
    private Long duration;
    /**
     * 失败原因
     */
    private String failureReason;
    /**
     * 失败文件
     */
    private String failureFile;
    /**
     * 文件大小
     */
    private Long fileSize;
    /**
     * 状态 (NOT-未开始，EXEC-执行中，SUCC-成功，FAIL-失败)
     */
    private String status;

    /**
     * 应用系统 OMS/VENDOR/PRICE_PARITY/TENDERS/API
     */
    private String systemApp;


}
