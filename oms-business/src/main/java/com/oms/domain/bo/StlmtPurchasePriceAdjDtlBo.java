package com.oms.domain.bo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.oms.common.core.domain.BaseEntity;
import com.oms.common.core.validate.AddGroup;
import com.oms.common.core.validate.EditGroup;
import javax.validation.constraints.*;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 结算-采购调价明细业务对象 stlmt_purchase_price_adj_dtl
 *
 * <AUTHOR>
 * @date 2025-01-13
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class StlmtPurchasePriceAdjDtlBo extends BaseEntity {

    /**
     * 主建
     */
    @NotNull(message = "主建不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 采购调价ID
     */
    private Long purchasePriceAdjId;

    /**
     * OMS采购订单ID
     */
    private Long purchaseOrderId;

    /**
     * OMS采购订单明细ID
     */
    private Long purchaseOrderLineId;

    /**
     * 外部订单
     */
    private String outId;

    /**
     * 销售订单
     */
    private Long sellerOrderId;

    /**
     * 结算采购单据ID
     */
    private Long stlmtPurchaseOrderId;

    /**
     * 结算采购单据明细ID
     */
    private Long stlmtPurchaseOrderLineId;

    /**
     * 采购时间
     */
    private Date purchaseTime;

    /**
     * 收货时间
     */
    private Date arrivalDate;

    /**
     * 供应商ID
     */
    private Long vendorId;

    /**
     * 供应商名称
     */
    private String vendorName;

    /**
     * 订单类型;0:服务商 1:自营
     */
    private Long orderType;

    /**
     * 采购类型;1:采购 2:采退
     */
    private Long purchaseType;

    /**
     * 单据类型;1:采购订单 2：采退订单 3：成本调价
     */
    private Long documentType;

    /**
     * 商品id
     */
    private Long itemId;

    /**
     * 商品名称
     */
    private String itemName;

    /**
     * skuId
     */
    private Long skuId;

    /**
     * 商品名称
     */
    private String skuName;

    /**
     * 计价单位
     */
    private String pricingUnit;

    /**
     * 税率
     */
    private BigDecimal taxRate;

    /**
     * 采购数量
     */
    private BigDecimal purchaseQuantity;

    /**
     * 原单价
     */
    private BigDecimal priceFrom;

    /**
     * 新单价
     */
    private BigDecimal priceTo;

    /**
     * 原采购金额
     */
    private BigDecimal purchaseAmountFrom;

    /**
     * 新采购金额
     */
    private BigDecimal purchaseAmountTo;
    /**
     * 包裹单号
     */
    private Long packageOrderId;



}
