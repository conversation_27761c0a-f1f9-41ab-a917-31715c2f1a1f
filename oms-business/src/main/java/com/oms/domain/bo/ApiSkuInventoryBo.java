package com.oms.domain.bo;

import com.oms.common.core.domain.BaseEntityBo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * api 商品库存业务对象 api_sku_inventory
 *
 * <AUTHOR>
 * @date 2025-05-11
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class ApiSkuInventoryBo extends BaseEntityBo {

    /**
     * 项目id
     */
    private Long prjId;
    /**
     * 项目名称
     */
    private String prjName;
    /**
     * api商品id
     */
    private Long apiItemId;
    /**
     * apiskuid
     */
    private Long apiSkuId;
    /**
     * 商品id
     */
    private Long itemId;
    /**
     * skuid
     */
    private Long skuId;
    /**
     * 区域id 全国为1 默认库存为-1
     */
    private Long areaId;
    /**
     * 库存数量
     */
    private Long inventory;
    /**
     * 有效期开始
     */
    private Date validityStart;
    /**
     * 有效期截止
     */
    private Date validityEnd;
    /**
     * 是否长期 Y是 N否
     */
    private String isLong;
    /**
     * 状态 (正常-NOR，关闭-DIS)
     */
    private String status;
}
