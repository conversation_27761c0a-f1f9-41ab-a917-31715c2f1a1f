package com.oms.domain.bo;

import com.oms.common.core.domain.BaseEntityBo;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 商品单位业务对象 goods_unit
 *
 * <AUTHOR>
 * @date 2024-08-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GoodsUnitBo extends BaseEntityBo {

	private static final long serialVersionUID = 1L;
    /**
     * 单位名称
     */
	private String unitName;

    /**
     * 状态 (正常-NOR，关闭-DIS)
     */
	private String status;

	private String query;
}
