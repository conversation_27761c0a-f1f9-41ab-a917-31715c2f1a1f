package com.oms.domain.bo.extend.price;

import com.oms.common.core.domain.PageQuery;
import com.oms.common.core.validate.AddGroup;
import com.oms.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 比价商品业务对象 price_goods
 *
 * <AUTHOR>
 * @date 2024-12-19
 */

@Data
public class PriceGoodsAddLineBo{

    /**
     * 平台编码 jd-京东 sn-苏宁
     */
    @NotBlank(message = "平台编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String platformCode;
    /**
     * 平台名称
     */
    @NotBlank(message = "平台名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String platformName;

    /**
     * 比价链接
     */
    @NotBlank(message = "比价链接不能为空", groups = { AddGroup.class, EditGroup.class })
    private String url;

    /**
     * 转换因子
     */
    private BigDecimal conversionFactor;


}
