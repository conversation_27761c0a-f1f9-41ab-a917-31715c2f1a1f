package com.oms.domain.bo.extend.stlmt;

import com.oms.common.core.domain.BaseEntity;
import com.oms.common.core.validate.AddGroup;
import com.oms.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 客户对账单业务对象 stlmt_customer_bill
 *
 * <AUTHOR>
 * @date 2025-02-04
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class StlmtCustomerBillOrderOperateCreateBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long customerBillId;

    @NotNull(message = "List不能为空", groups = { AddGroup.class})
    private List<StlmtCustomerBillOrderLineCreateBo> list ;



}
