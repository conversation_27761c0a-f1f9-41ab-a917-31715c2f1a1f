package com.oms.domain.bo;

import com.oms.common.core.domain.BaseEntityVo;
import com.oms.common.core.validate.AddGroup;
import com.oms.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 供应商类目权限业务对象 vendor_back_category
 *
 * <AUTHOR>
 * @date 2024-11-20
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class VendorBackCategoryBo extends BaseEntityVo {

    /**
     * 主键
     */
//    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 供应商ID
     */
//    @NotNull(message = "供应商ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long vendorId;

    /**
     * 后台类目id
     */
//    @NotNull(message = "后台类目id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long categoryId;

    /**
     * 状态 (正常1 禁用-1)
     */
//    @NotBlank(message = "状态 (正常1 禁用-1)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String status;

    private List<Long> categoryIds;

}
