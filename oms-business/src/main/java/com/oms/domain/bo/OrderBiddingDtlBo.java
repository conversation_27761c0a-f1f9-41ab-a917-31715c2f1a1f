package com.oms.domain.bo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.oms.common.core.domain.BaseEntity;
import com.oms.common.core.validate.AddGroup;
import com.oms.common.core.validate.EditGroup;
import javax.validation.constraints.*;

import java.math.BigDecimal;

/**
 * 大单议价订单明细业务对象 order_bidding_dtl
 *
 * <AUTHOR>
 * @date 2025-03-31
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class OrderBiddingDtlBo extends BaseEntity {

    /**
     * ID
     */
    @NotNull(message = "ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 大单议价id
     */
    @NotNull(message = "大单议价id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long orderId;

    /**
     * 议单编码
     */
    @NotBlank(message = "议单编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String orderNo;

    /**
     * 商品编号
     */
    @NotBlank(message = "商品编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String itemNo;

    /**
     * 商品名称
     */
    @NotBlank(message = "商品名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String itemName;

    /**
     * 品牌名称
     */
    @NotBlank(message = "品牌名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String branName;

    /**
     * 商品图片路径
     */
    @NotBlank(message = "商品图片路径不能为空", groups = { AddGroup.class, EditGroup.class })
    private String image;

    /**
     * 含税价
     */
    @NotNull(message = "含税价不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal ratePrice;

    /**
     * 税率
     */
    @NotNull(message = "税率不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal rate;

    /**
     * 不含税价
     */
    @NotNull(message = "不含税价不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal nakedPrice;

    /**
     * 订购数量
     */
    @NotNull(message = "订购数量不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long orderNum;

    /**
     * 采购确认供应商报价ID
     */
    @NotNull(message = "采购确认供应商报价ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long supplierOptimalDtlId;

    /**
     * 最终竞价
     */
    @NotNull(message = "最终竞价不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal finalBid;

    /**
     * EA资质是否有 1有 2无
     */
    @NotNull(message = "EA资质是否有 1有 2无不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long isEaHave;

    /**
     * 渠道确认服务商报价ID
     */
    @NotNull(message = "渠道确认服务商报价ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long channelOptimalDtlId;

    /**
     * 销售确认服务商报价ID
     */
    @NotNull(message = "销售确认服务商报价ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long saleOptimalDtlId;

    /**
     * 采购确认成本价
     */
    @NotNull(message = "采购确认成本价不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal supplierOptimalPrice;

    /**
     * 采购确认备注
     */
    @NotBlank(message = "采购确认备注不能为空", groups = { AddGroup.class, EditGroup.class })
    private String supplierOptimalRemark;

    /**
     * 渠道确认销售价
     */
    @NotNull(message = "渠道确认销售价不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal channelOptimalPrice;

    /**
     * 渠道确认费率
     */
    @NotNull(message = "渠道确认费率不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal channelOptimalRate;

    /**
     * 渠道确认备注
     */
    @NotBlank(message = "渠道确认备注不能为空", groups = { AddGroup.class, EditGroup.class })
    private String channelOptimalRemark;

    /**
     * 销售确认销售价
     */
    @NotNull(message = "销售确认销售价不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal saleOptimalPrice;

    /**
     * 销售确认费率
     */
    @NotNull(message = "销售确认费率不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal saleOptimalRate;

    /**
     * 销售确认备注
     */
    @NotBlank(message = "销售确认备注不能为空", groups = { AddGroup.class, EditGroup.class })
    private String saleOptimalRemark;

    /**
     * 最终归属ID
     */
    @NotNull(message = "最终归属ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long finalBelongId;

    /**
     * 采购确认资质
     */
    @NotBlank(message = "采购确认资质不能为空", groups = { AddGroup.class, EditGroup.class })
    private String supplierOptimalQualification;

    /**
     * 渠道确认资质
     */
    @NotBlank(message = "渠道确认资质不能为空", groups = { AddGroup.class, EditGroup.class })
    private String channelOptimalQualification;

    /**
     * 销售确认资质
     */
    @NotBlank(message = "销售确认资质不能为空", groups = { AddGroup.class, EditGroup.class })
    private String saleOptimalQualification;

    /**
     * 采购确认供应商名称
     */
    @NotBlank(message = "采购确认供应商名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String supplierOptimalName;

    /**
     * 渠道确认服务商名称
     */
    @NotBlank(message = "渠道确认服务商名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String channelOptimalName;

    /**
     * 销售确认服务商名称
     */
    @NotBlank(message = "销售确认服务商名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String saleOptimalName;

    /**
     * 指派创建ID
     */
    @NotNull(message = "指派创建ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long assignCreateId;

    /**
     * 指派创建名称
     */
    @NotBlank(message = "指派创建名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String assignCreateName;

    /**
     * 绑定SKUID
     */
    @NotNull(message = "绑定SKUID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long bindingSkuId;

    /**
     * 采购确认是否有资质
     */
    @NotNull(message = "采购确认是否有资质不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long supplierOptimalHaveQualification;

    /**
     * 渠道确认是否有资质
     */
    @NotNull(message = "渠道确认是否有资质不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long channelOptimalHaveQualification;

    /**
     * 销售确认是否有资质
     */
    @NotNull(message = "销售确认是否有资质不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long saleOptimalHaveQualification;

    /**
     * 最终归属名称
     */
    @NotBlank(message = "最终归属名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String finalBelongName;

    /**
     * 采购确认供应商id
     */
    @NotNull(message = "采购确认供应商id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long supplierOptimalId;

    /**
     * 渠道确认服务商id
     */
    @NotNull(message = "渠道确认服务商id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long channelOptimalId;

    /**
     * 销售确认服务商id
     */
    @NotNull(message = "销售确认服务商id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long saleOptimalId;

    /**
     * 最终运营确认归属备注
     */
    @NotBlank(message = "最终运营确认归属备注不能为空", groups = { AddGroup.class, EditGroup.class })
    private String finalBelongRemarks;

    /**
     * 采购确认报价税率
     */
    @NotNull(message = "采购确认报价税率不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal supplierOptimalTaxRate;

    /**
     * 采购确认未税价格
     */
    @NotNull(message = "采购确认未税价格不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal supplierOptimalTaxNoPrice;

    /**
     * 渠道确认报价税率
     */
    @NotNull(message = "渠道确认报价税率不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal channelOptimalTaxRate;

    /**
     * 渠道确认未税价格
     */
    @NotNull(message = "渠道确认未税价格不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal channelOptimalTaxNoPrice;

    /**
     * 销售确认报价税率
     */
    @NotNull(message = "销售确认报价税率不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal saleOptimalTaxRate;

    /**
     * 销售确认未税价格
     */
    @NotNull(message = "销售确认未税价格不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal saleOptimalTaxNoPrice;

    /**
     * 运营确认最终税率
     */
    @NotNull(message = "运营确认最终税率不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal finalBelongTaxRate;

    /**
     * 运营确认最终未税价格
     */
    @NotNull(message = "运营确认最终未税价格不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal finalBelongTaxNoPrice;

    /**
     * 结算方式: 现结、周结、半月结、月结、背靠背
     */
    @NotNull(message = "结算方式: 现结、周结、半月结、月结、背靠背不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long settleType;

}
