package com.oms.domain.bo;

import com.oms.common.core.domain.BaseEntity;
import com.oms.common.core.validate.AddGroup;
import com.oms.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 规则字典业务对象 rule_dictionary
 *
 * <AUTHOR>
 * @date 2024-11-09
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class RuleDictionaryBo extends BaseEntity {

    /**
     * 规则常量ID
     */
    @NotNull(message = "规则常量ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 常量类型
     */
    @NotBlank(message = "常量类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String dicType;

    /**
     * 中台编码: item商品 ,order订单
     */
    @NotBlank(message = "中台编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String centerCode;

    /**
     * 取值类型: 0:定值 1:动态
     */
    @NotNull(message = "取值类型: 0:定值 1:动态不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long valueType;

    /**
     * 常量名称
     */
    @NotBlank(message = "常量名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String dicName;

    /**
     * 常量值 (value_type=1时存，接口类名)
     */
    @NotBlank(message = "常量值 (value_type=1时存，接口类名)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String dicVal;

    /**
     * 排序
     */
    @NotNull(message = "排序不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long sort;

    /**
     * 状态 1启用 -1禁用
     */
    @NotNull(message = "状态 1启用 -1禁用", groups = { AddGroup.class, EditGroup.class })
    private Integer status;


}
