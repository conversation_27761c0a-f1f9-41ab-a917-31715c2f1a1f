package com.oms.domain.bo;

import com.oms.common.core.domain.BaseEntityBo;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * api外部品牌业务对象 api_out_brand
 *
 * <AUTHOR>
 * @date 2025-05-07
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class ApiOutBrandBo extends BaseEntityBo {


    /**
     * 项目id
     */
    private Long prjId;

    /**
     * 项目名称
     */
    private String prjName;

    /**
     * 外部品牌id
     */
    private String outId;

    /**
     * 外部品牌名称
     */
    private String outName;

    /**
     * 品牌logo
     */
    private String logo;

    /**
     * 备注
     */
    private String remark;

    /**
     * 状态 (正常-NOR，关闭-DIS)
     */
    private String status;
}
