package com.oms.domain.bo;

import lombok.Data;

/**
 * 发票BO
 */
@Data
public class InvoiceBo {
    private static final long serialVersionUID = -6398145860129779427L;

//    private Integer invoiceKind;

    /**
     * 数电发票类型：1：数电专票(纸质) 2：数电专票(电子)   3:数电普票(纸质)  4：数电普票(电子)
     */
    private Long invoiceType;
    /**
     * 发票抬头
     */
    private String invoiceTitle;

//    /**
//     * 个人开票身份证号
//     */
//    private String idCard;

//    /**
//     * 内容：1：明细 2：大类'
//     */
//    private String content;
    /**
     * 纳税人识别
     */
    private String itins;

    /**
     * 发票地址
     */
    private String address;

    /**
     * 联系电话
     */
    private String telephone;

    /**
     * 开户银行，增值税专票时必填
     */
    private String regBankName;

    /**
     * 开户银行，增值税专票时必填
     */
    private String bankAccount;

//    /**
//     * 邮箱地址
//     */
//    private String email;

    //    /**
//     *  发票种类:普通、电子、专用"
//     */
//    private Integer invoiceKind;

}
