package com.oms.domain.bo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 预销售订单业务对象 OutSellerOrderBo （用于外部订单使用）
 *
 * <AUTHOR>
 * @date 2024-08-28
 */

@Data
public class OutBigCustomerCreateOrderBo {

    /**
     * 客户id
     */
    private String clientId;

    /**
     * 商城订单编号 （唯一主键不重复）
     */
    private String orderNo;

    /**
     * 下单时间
     */
    private Date createTime;

    /**
     * 订单金额
     */
    private BigDecimal orderPrice;

    /**
     * 是否需要开具发票（0：需要；1：不需要）
     */
    private Integer isInvoice;

    /**
     * 发票类型（1=数电专票纸质，2=数电专票电子 3-数电普票纸质 4-数电普票电子）
     */
    private Integer invoiceType;

    /**
     * 发票抬头
     */
    private String invoiceTitle;

    /**
     * 电子邮箱
     */
    private String invoiceEmial;

    /**
     * 支付方式（01.月结 02:在线上支付 03.货到付款）
     */
    private Integer orderPaymentArgs;

    /**
     * 单位名称
     */
    private String companyName;

    /**
     * 下单人名称
     */
    private String buyerName;

    /**
     * 下单人座机
     */
    private String buyerTel;

    /**
     * 下单人手机号
     */
    private String buyerMobile;

    /**
     * 收货人姓名
     */
    private String reciverName;

    /**
     * 收货人手机号码
     */
    private String reciverMobile;

    /**
     * 邮箱
     */
    private String reciverEmial;

    /**
     * 一级地址id
     */
    private String provinceId;

    /**
     * 二级地址id
     */
    private String cityId;

    /**
     * 三级地址id
     */
    private String countyId;

    /**
     * 四级地址id
     */
    private String townId;

    /**
     * 一级地址名称
     */
    private String provinceName;

    /**
     * 二级地址名称
     */
    private String cityName;

    /**
     * 三级地址名称
     */
    private String countyName;

    /**
     * 四级地址名称
     */
    private String townName;

    /**
     * 收货人详细地址
     */
    private String reciverAddressDetail;

    /**
     * 订单备注
     */
    private String remark;


    /**
     * 订单商品明细
     */
    private List<OutBigCustomerCreateOrderLineBo> productItems;
}
