package com.oms.domain.bo;

import com.oms.common.core.domain.PageQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.oms.common.core.domain.BaseEntity;
import com.oms.common.core.validate.AddGroup;
import com.oms.common.core.validate.EditGroup;
import javax.validation.constraints.*;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 比价商品变价业务对象 price_goods_change_record
 *
 * <AUTHOR>
 * @date 2024-12-19
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class PriceGoodsChangeRecordBo extends PageQuery {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 商品id
     */
    @NotNull(message = "商品id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long itemId;

    /**
     * skuId
     */
    @NotNull(message = "skuId不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long skuId;

    /**
     * 商品名称
     */
    @NotBlank(message = "商品名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String itemName;

    /**
     * 平台编号 jd-京东 tmall-天猫 suning-苏宁
     */
    private String platformCode;
    /**
     * 平台名称
     */
    private String platformName;

    /**
     * 销售价格
     */
    @NotNull(message = "销售价格不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal salePrice;

    /**
     * 平台价格
     */
    @NotNull(message = "平台价格不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal platformPrice;

    /**
     * 项目id
     */
    @NotNull(message = "项目id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long projectId;

    /**
     * 项目名称
     */
    @NotBlank(message = "项目名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String projectName;

    /**
     * 对比状态 up-涨 down-跌
     */
    @NotBlank(message = "对比状态 up-涨 down-跌不能为空", groups = { AddGroup.class, EditGroup.class })
    private String comparisonStatus;


    private List<String> comparisonStatusList;

}
