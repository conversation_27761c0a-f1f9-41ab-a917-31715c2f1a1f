package com.oms.domain.bo;

import com.oms.common.core.domain.BaseEntityBo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

/**
 * 项目sku信息拓展对象 goods_prj_sku_extends
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GoodsPrjSkuExtendsBo extends BaseEntityBo {

	private static final long serialVersionUID=1L;

	/**
	 * 项目id
	 */
    private Long prjId;
	/**
	 * 商品id
	 */
    private Long itemId;
	/**
	 * skuId
	 */
    private Long skuId;
	/**
	 * 项目品牌id
	 */
    private Long prjBrandId;
	/**
	 * 项目外部品牌id
	 */
	private String prjOutBrandId;
	/**
	 * 项目分类id
	 */
    private Long prjCateId;
	/**
	 * 项目外部分类id
	 */
	private String prjOutCateId;
	/**
	 * 税率
	 */
	private String vatrate;
	/**
	 * 税收分类编码
	 */
	private String taxCode;
	/**
	 * 税收分类名称
	 */
	private String taxName;
	/**
	 * 市场价
	 */
	private BigDecimal marketPrice;
	/**
	 * 状态 (正常1 禁用-1)
	 */
    private String status;
	/**
	 * 拓展属性值集合
	 */
	private List<GoodsPrjSkuExtendsAttributeBo> extendsAttributeBoList;
}
