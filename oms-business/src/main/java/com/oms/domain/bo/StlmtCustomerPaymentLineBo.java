package com.oms.domain.bo;

import com.oms.common.core.domain.BaseEntity;
import com.oms.common.core.validate.AddGroup;
import com.oms.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Set;

/**
 * 客户收款明细业务对象 stlmt_customer_payment_line
 *
 * <AUTHOR>
 * @date 2025-02-20
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class StlmtCustomerPaymentLineBo extends BaseEntity {

    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 收款单编号
     */
    @NotBlank(message = "收款单编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String paymentNo;

    /**
     * 销售单号
     */
    @NotBlank(message = "销售单号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String sellerOrderNo;

    /**
     * 销售单行号
     */
    @NotBlank(message = "销售单行号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String sellerOrderLineNo;

    /**
     * 发货单号
     */
    @NotBlank(message = "发货单号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String deliveryOrderNo;

    /**
     * 外部单号
     */
    @NotBlank(message = "外部单号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String outId;

    /**
     * 客户名称
     */
    @NotBlank(message = "客户名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String customerName;

    /**
     * 商品ID
     */
    @NotNull(message = "商品ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long itemId;

    /**
     * 商品SKUID
     */
    @NotNull(message = "商品SKUID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long skuId;

    /**
     * 商品SKU名称
     */
    @NotBlank(message = "商品SKU名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String skuName;

    /**
     * 商品SKU编码
     */
    @NotBlank(message = "商品SKU编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String skuCode;

    /**
     * 商品图片URL
     */
    @NotBlank(message = "商品图片URL不能为空", groups = { AddGroup.class, EditGroup.class })
    private String itemImage;

    /**
     * 商品名称
     */
    @NotBlank(message = "商品名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String itemName;

    /**
     * 销售数量
     */
    @NotNull(message = "销售数量不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal salesQuantity;

    /**
     * 发货数量
     */
    @NotNull(message = "发货数量不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal deliveryQuantity;

    /**
     * 销售金额
     */
    @NotNull(message = "销售金额不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal salesAmount;

    /**
     * 发货金额
     */
    @NotNull(message = "发货金额不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal deliveryAmount;

    /**
     * 已收金额
     */
    @NotNull(message = "已收金额不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal receivedAmount;

    /**
     * 是否销售单据查询
     */
    private Boolean isSellerOrder;

    /**
     * 收款单编码列表
     */
    private Set<String> paymentNoList;


}
