package com.oms.domain.bo;

import com.oms.common.core.domain.BaseEntity;
import com.oms.common.core.validate.AddGroup;
import com.oms.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 客户收款明细业务对象 添加销售商品
 *
 * <AUTHOR>
 * @date 2025-02-20
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class StlmtCustomerPaymentLineCreateItemBo extends BaseEntity {


    /**
     * 收款单编号
     */
    @NotBlank(message = "收款单编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String paymentNo;

    /**
     * 结算销售单据号行ID
     */
    private List<Long> stlmtSellerOrderLineIds;

}
