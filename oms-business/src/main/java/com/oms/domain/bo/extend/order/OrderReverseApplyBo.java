package com.oms.domain.bo.extend.order;

import com.oms.common.core.domain.BaseEntity;
import com.oms.common.core.validate.AddGroup;
import com.oms.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 逆向退货单业务对象 order_reverse
 *
 * <AUTHOR>
 * @date 2025-03-11
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class OrderReverseApplyBo extends BaseEntity {
    /**
     * 项目ID
     */
    @NotNull(message = "项目ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long prjId;

    /**
     * 项目名称
     */
    @NotBlank(message = "项目名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String prjName;

    /**
     * 逆向单类型(cancel,return,only_refund)
     */
    @NotBlank(message = "逆向单类型(cancel,return,only_refund)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String reverseType;

    /**
     * 订单ID
     */
    @NotNull(message = "订单ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long orderId;

    /**
     * 发起逆向时的退款金额
     */
    @NotNull(message = "退款金额不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal refundAmount;

    /**
     * 买家id;
     */
    private Long buyerId;

    /**
     * 买家名称;
     */
    private String buyerName;


    /**
     * 供应商id;
     */
    @NotNull(message = "供应商id;不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long shopId;

    /**
     * 供应商名称;
     */
    @NotBlank(message = "供应商名称;不能为空", groups = { AddGroup.class, EditGroup.class })
    private String shopName;

    /**
     * 发起逆向的原因
     */
    private Integer reasonId;

    /**
     * 逆向原因文案
     */
    private String reasonText;

    /**
     * 买家留言;
     */
    private String buyerNotes;

    /**
     * 卖家留言;
     */
    private String sellerNotes;

    /**
     * 外部id;
     */
    @NotBlank(message = "外部id;不能为空", groups = { AddGroup.class, EditGroup.class })
    private String outId;

    /**
     * 退货方式 1.上门取件;2.客户发货;3客户送货
     */
    private Integer returnType;


    /**
     * 售后申请单行
     */
    private List<OrderReverseLineApplyBo> orderReverseLineApplyBoList;
}
