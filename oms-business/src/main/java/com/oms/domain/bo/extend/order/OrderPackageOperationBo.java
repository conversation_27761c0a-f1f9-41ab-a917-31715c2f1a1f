package com.oms.domain.bo.extend.order;

import com.oms.common.core.domain.BaseEntity;
import com.oms.common.core.validate.AddGroup;
import com.oms.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * 采购订单业务对象 order
 *
 * <AUTHOR>
 * @date 2024-09-20
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class OrderPackageOperationBo extends BaseEntity {
    /**
     * 项目ID
     */
    @NotNull(message = "项目ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long prjId;

    /**
     * 包裹单id
     */
    private Long id;
}
