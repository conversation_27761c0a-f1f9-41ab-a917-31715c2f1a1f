package com.oms.domain.bo;

import com.oms.common.core.domain.BaseEntityBo;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * api商品业务对象 api_item
 *
 * <AUTHOR>
 * @date 2025-05-07
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class ApiItemV1Bo extends BaseEntityBo {

    /**
     * 商品id
     */
    private Long itemId;
    /**
     * 中台类目id
     */
    private Long categoryId;
    /**
     * 中台品牌id
     */
    private Long brandId;
    /**
     * 外部商品id
     */
    private String outItemId;
    /**
     * 外部品牌id
     */
    private String outBrandId;
    /**
     * 外部分类id
     */
    private String outCateId;
    /**
     * 商品名称
     */
    private String itemName;
    /**
     * 商品编码
     */
    private String itemCode;
    /**
     * 状态 (1上架  -1下架)
     */
    private String status;
    /**
     * skuId
     */
    private Long skuId;
    /**
     * sku编码
     */
    private String skuCode;
}
