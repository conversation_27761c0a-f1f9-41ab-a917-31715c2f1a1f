package com.oms.domain.bo;

import com.oms.common.core.domain.BaseEntity;
import com.oms.common.core.validate.AddGroup;
import com.oms.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 销售订单行业务对象 seller_order_line
 *
 * <AUTHOR>
 * @date 2024-08-28
 */

@Data
public class OutSellerOrderLineBo {
    /**
     * sku id
     */
    private Long skuId;

    /**
     * sku 名称
     */
    private String skuName;

    /**
     * 数量
     */
    private Long quantity;

    /**
     * 实际支付金额
     */
    private BigDecimal paidAmount;
}
