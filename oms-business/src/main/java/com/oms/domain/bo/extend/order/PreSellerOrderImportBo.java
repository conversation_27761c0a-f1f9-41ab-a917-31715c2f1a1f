package com.oms.domain.bo.extend.order;

import com.oms.common.core.domain.BaseEntity;
import com.oms.common.core.validate.AddGroup;
import com.oms.common.core.validate.EditGroup;
import com.oms.domain.bo.SellerOrderLineBo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 手工下单简易对象
 *
 * <AUTHOR>
 * @date 2024-08-28
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class PreSellerOrderImportBo extends BaseEntity {


    /**
     * 项目id
     */
    @NotNull(message = "项目id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long prjId;

    /**
     * 区域运营商名称
     */
    @NotBlank(message = "项目名称", groups = { AddGroup.class, EditGroup.class })
    private String prjName;

    /**
     * 外部订单id
     */
    @NotBlank(message = "外部订单id不能为空", groups = { AddGroup.class, EditGroup.class })
    private String outId;

    /**
     * 买家id
     */
    @NotNull(message = "买家id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long buyerId;

    /**
     * 买家姓名
     */
    @NotBlank(message = "买家姓名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String buyerName;

    /**
     * 省id
     */
    @NotBlank(message = "省id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long provinceId;
    /**
     * 省名称
     */
    @NotBlank(message = "省名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String provinceName;
    /**
     * 市id
     */
    @NotBlank(message = "市id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long cityId;

    /**
     * 市名称
     */
    @NotBlank(message = "市名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String cityName;
    /**
     * 区域Id
     */
    @NotBlank(message = "区域id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long regionId;
    /**
     * 区名称
     */
    @NotBlank(message = "区域名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String regionName;
    /**
     * 街道id
     */
    @NotBlank(message = "街道id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long streetId;
    /**
     * 街道名称
     */
    @NotBlank(message = "区域名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String streetName;
    /**
     * 收货详细地址
     */
    @NotBlank(message = "详细地址不能为空", groups = { AddGroup.class, EditGroup.class })
    private String receiveAddressDetail;

    /**
     * 买家留言
     */
    private String buyerNotes;

    /**
     * 外部订单日期(有则填，没有默认当前日期)
     */
    @NotBlank(message = "详细地址不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date outDate;

    /**
     * 发票抬头
     */
    private String invoiceTitle;

    /**
     * 采购单位名称
     */
    private String purchaseCompany;

    /**
     * 来源单号
     */
    private String sourceId;

    /**
     * 申购单号
     */
    private String applyCode;

    /**
     * 收货人名称
     */
    @NotBlank(message = "收货人名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String receiveUserName;

    /**
     * 收货人电话
     */
    @NotBlank(message = "收货人电话不能为空", groups = { AddGroup.class, EditGroup.class })
    private String receiveMobile;


    /**
     * 下单人姓名
     */
    @NotBlank(message = "下单人姓名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String placeOrderName;

    /**
     * 下单人电话
     */
    @NotBlank(message = "下单人电话不能为空", groups = { AddGroup.class, EditGroup.class })
    private String placeOrderMobile;

    /**
     * 订单行列表BO
     */
    private List<SellerOrderLineBo> sellerOrderLines;
}
