package com.oms.domain.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.oms.common.core.domain.PageQuery;
import com.oms.common.core.validate.AddGroup;
import com.oms.common.core.validate.EditGroup;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 投标单中标BO
 *
 * <AUTHOR>
 * @date 2024-10-15
 */

@Data
public class TendersLoseBo extends PageQuery {

    /**
     * 
     */
    @NotNull(message = "不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 信息id
     */
    @NotBlank(message = "信息id不能为空")
    private String infoId;

    /**
     * 信息标题
     */
    @NotBlank(message = "信息标题不能为空", groups = { AddGroup.class, EditGroup.class })
    private String infoTitle;

    /**
     * 省份id
     */
    private Long provinceId;

    /**
     * 省份名称
     */
    private String provinceName;

    /**
     * 市名称
     */
    private String cityName;

    /**
     * 区县名称
     */
    private String districtName;


    /**
     * 所属区域
     */
    @NotBlank(message = "所属区域不能为空", groups = { AddGroup.class, EditGroup.class })
    private String belongingArea;

    /**
     * 物资分类
     */
    @NotBlank(message = "物资分类不能为空", groups = { AddGroup.class, EditGroup.class })
    private String materialCategory;

    /**
     * 支撑部门
     */
    private String supportDepartment;

    /**
     * 项目等级
     */
    @NotNull(message = "项目等级不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer projectLevel;

    @NotBlank(message = "行业不能为空", groups = { EditGroup.class })
    private String industry ;

    /**
     * 服务期限
     */
    private String servicePeriod;

    /**
     * 开标时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date openBidingTime;

    /**
     * 预算金额
     */
    private BigDecimal bidBudget;

    /**
     * 中标公司数量
     */
    private Integer companyNumber;

    /**
     * 招标网站地址
     */
    private String websiteUrl ;

    /**
     * 结果公示时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date resultShowDate;


    /**
     * 结果公示链接/中标链接
     */

    private String bidWinUrl;

    /**
     * 销售名称
     */
    private String salesName;

    /**
     * 投标结果分析
     */
    private String bidCauseAnalysis;

    /**
     * 未中标附件,
     */
    private List<Map<String, String>> loseBidFileList ;

    /**
     * 未中标备注
     */
    private String loseBidRemark ;



}
