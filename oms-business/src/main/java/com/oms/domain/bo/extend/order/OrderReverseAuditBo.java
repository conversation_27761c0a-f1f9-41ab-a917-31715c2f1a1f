package com.oms.domain.bo.extend.order;

import com.oms.common.core.domain.BaseEntityBo;
import com.oms.common.core.validate.AddGroup;
import com.oms.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

@Data
@EqualsAndHashCode(callSuper = true)
public class OrderReverseAuditBo extends BaseEntityBo {

    @NotNull(message = "逆向单ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long id;

    /**
     * 0 拒绝 1 同意
     */
    @NotNull(message = "是否同意售后不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer auditStatus;

    private Long provinceId;

    private Long cityId;

    private Long areaId;

    private String returnAddr;

    private String returnContact;

    private String returnPhone;

    private String auditRemark;

    /**
     * 返件方式 1 客户发货  2 上门取件
     */
    private Integer returnType;

}
