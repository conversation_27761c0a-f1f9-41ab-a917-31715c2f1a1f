package com.oms.domain.bo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.oms.common.core.domain.BaseEntity;
import com.oms.common.core.validate.AddGroup;
import com.oms.common.core.validate.EditGroup;
import javax.validation.constraints.*;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 导入任务业务对象 sys_async_import
 *
 * <AUTHOR>
 * @date 2025-01-14
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class SysAsyncImportBo extends BaseEntity {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 应用系统 OMS/VENDOR/PRICE_PARITY/TENDERS/API
     */
    private String systemApp;

    /**
     * 项目id
     */
//    @NotNull(message = "项目id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long prjId;

    /**
     * 项目名称
     */
//    @NotBlank(message = "项目名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String prjName;

    /**
     * 供应商id
     */
    private Long vendorId;
    /**
     * 供应商名称
     */
    private String vendorName;

    /**
     * 导入类型
     */
    @NotBlank(message = "导入类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String importType;

    /**
     * 导入参数
     */
    private String importParam;

    /**
     * 导入文件
     */
    @NotBlank(message = "导入文件不能为空", groups = { AddGroup.class, EditGroup.class })
    private String importFile;

    /**
     * 导入时长
     */
    private Long duration;

    /**
     * 失败原因
     */
    private String failureReason;

    /**
     * 失败文件
     */
    private String failureFile;

    /**
     * 文件大小
     */
    private Long fileSize;

    /**
     * 状态 (NOT-未开始，EXEC-执行中，SUCC-成功，FAIL-失败)
     */
    private String status;

}
