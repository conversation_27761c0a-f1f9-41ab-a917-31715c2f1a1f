package com.oms.domain.bo;

import com.oms.common.core.domain.BaseEntity;
import com.oms.common.core.validate.AddGroup;
import com.oms.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 供应商资质审核业务对象
 *
 * <AUTHOR>
 * @date 2024-10-14
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class VendorCertificationAuditBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 供应商id
     */
    private Long vendorId;

    /**
     * 供应商名称
     */
    private String vendorName;

    /**
     * 资质类型 0.通用授权 1.项目授权
     */
    @NotNull(message = "资质类型 0.通用授权 1.项目授权不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long type;

    /**
     * 证书类型 1-资质 2-授权 3-商标 4-商标+授权
     */
    @NotNull(message = "证书类型 1-资质 2-授权 3-商标 4-商标+授权不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long certificateType;

    /**
     * 证书类别 0-其他 1-ccc认证 2-经营许可证 3-质检报告 4-开户许可证 
     */
    private String certificateCategory;

    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 品牌id
     */
    private Long brandId;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 分类id
     */
    private Long categoryId;

    /**
     * 分类名称
     */
    private String categoryName;

    /**
     * 供应商商品id(,分隔)
     */
    private String itemId;

    /**
     * 有效期开始
     */
    @NotNull(message = "有效期开始不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date validityStart;

    /**
     * 有效期截止
     */
    @NotNull(message = "有效期截止不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date validityEnd;

    /**
     * 有效期永久状态： 0-否 1-是
     */
    private Integer permanentFlag;

    /**
     * 资质证书附件url
     */
    private List<Map<String, String>> fileJson;

    /**
     * 状态 0-生效中 1-过期 2-过期待供应商修改 3-待生效
     */
    private Integer status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 开发者id
     */
    private Long developerId;

    /**
     * 开发者(姓名 + 手机号码)
     */
    private String developer;

    /**
     * 是否首选授权
     */
    private Integer isFirst;

    /**
     * 资质授权编号
     */
    private String certificationCode;

    /**
     * 授权等级
     */
    private Integer certificationLevel;

    /**
     * 授权是否一致 0否 1是
     */
    private Integer isAgree;

    /**
     * 授权人
     */
    private String certificationName;


    /**
     * 审核状态 0-待审批 1-审核通过 2-审核拒绝
     */
    private Integer auditStatus;

    /**
     * 审核时间
     */
    private Date auditDate;

    /**
     * 审核人名
     */
    private String auditName;

    /**
     * 审核描述
     */
    private String auditMsg;

    /**
     * ids
     */
    private Set<Long> ids ;

    /**
     * 分类Ids
     */
    private List<Long> categoryIds ;


    /**
     * 商品ids
     */
    private List<Long> itemIds ;

    /**
     * 分类List
     */
    private List<VendorCertificationItemBo> categoryList ;

    /**
     * 商品List
     */
    private List<VendorCertificationItemBo> itemList ;

    /**
     * 前端查询id
     */
    private String queryId ;


}
