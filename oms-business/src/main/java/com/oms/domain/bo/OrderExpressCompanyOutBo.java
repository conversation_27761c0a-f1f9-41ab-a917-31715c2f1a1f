package com.oms.domain.bo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.oms.common.core.domain.BaseEntity;
import com.oms.common.core.validate.AddGroup;
import com.oms.common.core.validate.EditGroup;
import javax.validation.constraints.*;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 项目物流公司信息业务对象 order_express_company_out
 *
 * <AUTHOR>
 * @date 2024-11-02
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class OrderExpressCompanyOutBo extends BaseEntity {

    /**
     * 主键id
     */
    @NotNull(message = "主键id不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 系统物流ID
     */
    @NotNull(message = "系统物流ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long expressCompanyId;

    /**
     * 系统物流名称
     */
    @NotBlank(message = "系统物流名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String expressCompanyName;

    /**
     * 系统物流编码
     */
    @NotBlank(message = "系统物流编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String expressCompanyCode;

    /**
     * 外部系统物流名称
     */
    @NotBlank(message = "外部系统物流名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String outCompanyName;

    /**
     * 外部系统物流编码
     */
    @NotBlank(message = "外部系统物流编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String outCompanyCode;

    /**
     * 状态
     */
    private Integer sts;

    /**
     * 区域运营id
     */
    @NotNull(message = "区域运营id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long prjId;

    /**
     * 区域运营名称
     */
    @NotBlank(message = "区域运营名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String prjName;

}
