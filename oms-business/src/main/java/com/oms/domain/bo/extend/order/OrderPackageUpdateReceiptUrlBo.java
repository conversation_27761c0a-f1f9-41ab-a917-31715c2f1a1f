package com.oms.domain.bo.extend.order;

import com.oms.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 采购订单业务对象 order
 *
 * <AUTHOR>
 * @date 2024-09-20
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class OrderPackageUpdateReceiptUrlBo extends BaseEntity {
    /**
     * 项目ID
     */
    private Long prjId;

    /**
     * 包裹单id
     */
    private Long id;


    /**
     * 面单url
     */
    private String receiptUrl;
}
