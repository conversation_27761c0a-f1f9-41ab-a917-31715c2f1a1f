package com.oms.domain.bo;

import com.oms.common.core.domain.BaseEntityBo;
import com.oms.common.core.validate.AddGroup;
import com.oms.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 项目销售类目业务对象 goods_prj_category
 *
 * <AUTHOR>
 * @date 2024-10-06
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class GoodsPrjCategoryBo extends BaseEntityBo {

    /**
     * 父id
     */
//    @NotNull(message = "父id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long pid;

    /**
     * 项目id
     */
    private Long prjId;
    /**
     * 类目名称
     */
//    @NotBlank(message = "类目名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String cateName;
    /**
     * 类目图标
     */
    private String icon;

    /**
     * 是否有子类
     */
//    @NotBlank(message = "是否有子类不能为空", groups = { AddGroup.class, EditGroup.class })
    private String hasChildren;

    /**
     * 类目等级
     */
//    @NotNull(message = "类目等级不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long cateLevel;

    /**
     * 状态 (正常-NOR，关闭-DIS)
     */
    @NotBlank(message = "状态 (正常-NOR，关闭-DIS)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String status;
}
