package com.oms.domain.bo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.oms.common.core.domain.BaseEntity;
import com.oms.common.core.validate.AddGroup;
import com.oms.common.core.validate.EditGroup;
import javax.validation.constraints.*;


/**
 * 订单核心信息更新记录业务对象 order_date_update_log
 *
 * <AUTHOR>
 * @date 2024-11-13
 */

@Data
public class OrderDataUpdateLogBo extends BaseEntity{

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 租户id
     */
    @NotNull(message = "租户id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long tenantId;

    /**
     * 项目ID
     */
    @NotNull(message = "项目ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long prjId;

    /**
     * 项目名称
     */
    @NotBlank(message = "项目名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String prjName;

    /**
     * 外部订单id
     */
    @NotBlank(message = "外部订单id不能为空", groups = { AddGroup.class, EditGroup.class })
    private String outId;

    /**
     * 业务单id
     */
    @NotNull(message = "业务单ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long businessId;

    /**
     * 业务类型：1：销售单修改地址 .....
     */
    @NotBlank(message = "业务类型：1：销售单修改地址 .....不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer businessType;

    /**
     * 原始信息 存JSON
     */
    @NotBlank(message = "原始信息 存JSON不能为空", groups = { AddGroup.class, EditGroup.class })
    private String orgData;

    /**
     * 更新信息 存JSON
     */
    @NotBlank(message = "更新信息 存JSON不能为空", groups = { AddGroup.class, EditGroup.class })
    private String updateData;

    /**
     * 创建人名
     */
    @NotBlank(message = "创建人名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String createName;


}
