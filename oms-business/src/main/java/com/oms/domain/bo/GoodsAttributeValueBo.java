package com.oms.domain.bo;

import com.oms.common.core.domain.BaseEntityBo;
import lombok.Data;
import lombok.EqualsAndHashCode;

//import javax.validation.constraints.*;

/**
 * 商品属性值业务对象 goods_attribute_value
 *
 * <AUTHOR>
 * @date 2024-08-31
 */


@Data
@EqualsAndHashCode(callSuper = true)
public class GoodsAttributeValueBo extends BaseEntityBo {

	private static final long serialVersionUID = 1L;
    /**
     * 属性值id
     */
	private Long attributeId;

    /**
     * 属性值编码
     */
	private String valueCode;

    /**
     * 属性值名称
     */
	private String valueLabel;

    /**
     * 排序
     */
	private Long orderNum;

    /**
     * 状态 (正常-NOR，关闭-DIS)
     */
	private String status;
}
