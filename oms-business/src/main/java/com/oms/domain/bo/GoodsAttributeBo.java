package com.oms.domain.bo;

import com.oms.common.core.domain.BaseEntityBo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

//import javax.validation.constraints.*;

/**
 * 商品属性业务对象 goods_attribute
 *
 * <AUTHOR>
 * @date 2024-08-31
 */


@Data
@EqualsAndHashCode(callSuper = true)
public class GoodsAttributeBo extends BaseEntityBo {

	private static final long serialVersionUID = 1L;
    /**
     * 属性名称
     */
	private String attributeName;

    /**
     * 是否必填
     */
	private String canRequired;

    /**
     * 是否销售属性
     */
	private String canSalable;

    /**
     * 是否可自定义
     */
	private String canCustomizable;

    /**
     * 是否可搜索
     */
	private String canSearchable;

    /**
     * 是否显示
     */
	private String canDisplayable;

    /**
     * 属性值类型 TEXT=文本，NUM=数字，DATE=日期
     */
	private String valueType;


    /**
     * 状态 (正常-NOR，关闭-DIS)
     */
	private String status;

	private String query;

	private List<GoodsAttributeValueBo> goodsAttributeValueBoList;
}
