package com.oms.domain.bo.extend.vendor;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 供应商账号绑定-- oms 端
 *
 * <AUTHOR>
 * @date 2024-09-05
 */
@Data
@ExcelIgnoreUnannotated
public class SysVendorRoleBindingBo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 手机号
     */
    @ExcelProperty(value = "手机号")
    private String mobile;

    /**
     * 权限列表Ids
     */
    private List<Long> roleIds;


}
