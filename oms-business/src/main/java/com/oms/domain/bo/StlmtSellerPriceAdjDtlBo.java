package com.oms.domain.bo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.oms.common.core.domain.BaseEntity;
import com.oms.common.core.validate.AddGroup;
import com.oms.common.core.validate.EditGroup;
import javax.validation.constraints.*;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 结算-销售调价明细业务对象 stlmt_seller_price_adj_dtl
 *
 * <AUTHOR>
 * @date 2025-01-13
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class StlmtSellerPriceAdjDtlBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 状态
     */
    private String status;

    /**
     * 销售调价ID
     */
    private Long sellerPriceAdjId;

    /**
     * 发货单号ID
     */
    private Long packageOrderId;

    /**
     * 采购订单号ID
     */
    private Long purchaseOrderId;

    /**
     * 外部订单号
     */
    private String outId;

    /**
     * OMS销售订单号ID
     */
    private Long sellerOrderId;

    /**
     * OMS销售订单行ID
     */
    private Long sellerOrderLineId;

    /**
     * 销售单据ID
     */
    private Long stlmtSellerOrderId;

    /**
     * 销售单据明细ID
     */
    private Long stlmtSellerOrderLineId;

    /**
     * 销售时间
     */
    private Date sellerTime;

    /**
     * 出货日期
     */
    private Date packageDate;

    /**
     * 送货客户ID
     */
    private Long deliverCustomerId;

    /**
     * 送货客户名称
     */
    private String deliverCustomerName;

    /**
     * 开票客户ID
     */
    private Long invoiceCustomerId;

    /**
     * 开票客户名称
     */
    private String invoiceCustomerName;

    /**
     * 应收客户ID
     */
    private Long receivableCustomerId;
    /**
     * 应收客户名称
     */
    private String receivableCustomerName;

    /**
     * 销售类型;1:销售  2:销退
     */
    private Long sellerType;

    /**
     * 单据类型;1:销售出库  2:销退退回 3.销售调价
     */
    private Long documentType;

    /**
     * 订单类型;0服务商,1自营
     */
    private Long orderType;

    /**
     * 商品id
     */
    private Long itemId;

    /**
     * 商品名称
     */
    private String itemName;

    /**
     * sku id
     */
    private Long skuId;

    /**
     * 商品名称
     */
    private String skuName;

    /**
     * 计价单位
     */
    private String pricingUnit;

    /**
     * 税率
     */
    private BigDecimal taxRate;

    /**
     * 销售数量
     */
    private BigDecimal sellerQuantity;

    /**
     * 原含税箱价
     */
    private BigDecimal priceFrom;

    /**
     * 新含税箱价
     */
    private BigDecimal priceTo;

    /**
     * 原销售金额
     */
    private BigDecimal sellerAmountFrom;
    /**
     * 新销售金额
     */
    private BigDecimal sellerAmountTo;

    /**
     * 销售单价
     */
    private BigDecimal sellerPrice;

    /**
     * 销售金额
     */
    private BigDecimal sellerAmount;


}
