package com.oms.domain.bo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class JDInvoiceCallbackBo {


    /**
     * 作废原因
     */
    private String abolishReason;

    /**
     * 批次号
     */
    private String batch;


    /**
     * 单据编号
     */
    private String billNo;


    /**
     * 发票代码
     */
    private String invoiceCode;


    /**
     * 发票号码
     */
    private String invoiceNum;


    /**
     * 发票状态
     */
    private String invoiceStatus;


    /**
     * 开票日期
     */
    private String invoiceDate;

    /**
     * PDF转图片预览地址
     */
    private String invoiceImageUrl;


    /**
     * 数电票为ofd文件
     */
    private String invoiceFileUrl;


    /**
     * 数电票pdf地址
     */
    private String invoicePdfFileUrl;


    /**
     * 数电票xml地址
     */
    private String invoiceXmlFileUrl;


    /**
     * 开票类型 ，0蓝票，1红票 【长度：1】
     */
    private String invoiceProperty;

    /**
     * 发票种类
     */
    private String invoiceType;

}



