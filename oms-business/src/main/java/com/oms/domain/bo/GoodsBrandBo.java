package com.oms.domain.bo;

import com.oms.common.core.domain.BaseEntityBo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;


/**
 * 品牌业务对象 goods_brand
 *
 * <AUTHOR>
 * @date 2024-08-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GoodsBrandBo extends BaseEntityBo {

	private static final long serialVersionUID = 1L;
    /**
     * 品牌名称
     */
	private String brandName;

    /**
     * 品牌logo
     */
	private String logo;

    /**
     * 品牌英文名
     */
	private String enName;

    /**
     * 备注
     */
	private String remark;

    /**
     * 状态 (正常-NOR，关闭-DIS)
     */
	private String status;

	private String query;

	/**
	 * 修改时间开始
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date updateTimeStart;
	/**
	 * 修改间结束
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date updateTimeEnd;
}
