package com.oms.domain.bo.extend.vendor;

import com.oms.common.core.domain.PageQuery;
import com.oms.common.core.validate.AddGroup;
import com.oms.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * 供应商信息业务对象 vendor_info
 *
 * <AUTHOR>
 * @date 2024-09-01
 */

@Data
public class VendorRegisterVerifyBo {
    /**
     * 联系电话
     */
    @NotBlank(message = "联系电话不能为空", groups = { AddGroup.class, EditGroup.class })
    private String phone;

    /**
     * 验证码
     */
    @NotBlank(message = "验证码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String smsCode;

    /**
     * 统一社会信用代码
     */
    @NotBlank(message = "统一社会信用代码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String unifiedSocialCreditCode;

    /**
     * 供应商类型
     */
    @NotBlank(message = "供应商类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String vendorType;


}
