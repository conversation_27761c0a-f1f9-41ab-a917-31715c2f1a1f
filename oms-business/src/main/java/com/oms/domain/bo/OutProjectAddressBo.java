package com.oms.domain.bo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.oms.common.core.domain.BaseEntity;
import com.oms.common.core.validate.AddGroup;
import com.oms.common.core.validate.EditGroup;
import javax.validation.constraints.*;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 外部系统自有地址库业务对象 out_project_address
 *
 * <AUTHOR>
 * @date 2024-10-13
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class OutProjectAddressBo extends BaseEntity {

    /**
     * 
     */
    @NotNull(message = "不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 父级编码
     */
    @NotBlank(message = "父级编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String pCode;

    /**
     * 项目ID
     */
    @NotNull(message = "项目ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long prjId;

    /**
     * 租户ID
     */
    @NotNull(message = "租户ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long tenantId;

    /**
     * 地址名称
     */
    @NotBlank(message = "地址名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String name;

    /**
     * 地址编码
     */
    @NotBlank(message = "地址编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String code;

    /**
     * 级别
     */
    @NotNull(message = "级别不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long level;

    /**
     * 状态 1:可用0:不可用
     */
    @NotNull(message = "状态 1:可用0:不可用不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long status;

}
