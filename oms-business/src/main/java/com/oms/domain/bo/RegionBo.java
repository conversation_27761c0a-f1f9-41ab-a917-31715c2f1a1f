package com.oms.domain.bo;

import com.oms.common.core.domain.BaseEntityBo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.oms.common.core.domain.BaseEntity;
import com.oms.common.core.validate.AddGroup;
import com.oms.common.core.validate.EditGroup;
import javax.validation.constraints.*;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 系统地区业务对象 region
 *
 * <AUTHOR>
 * @date 2024-10-09
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class RegionBo extends BaseEntityBo {

    /**
     * id
     */
//    @NotNull(message = "id不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 父id
     */
//    @NotNull(message = "父id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long pid;

    /**
     * 国家编码(cn-中国)countrycode
     */
//    @NotBlank(message = "国家编码(cn-中国)countrycode不能为空", groups = { AddGroup.class, EditGroup.class })
    private String countryCode;

    /**
     * 地区编号
     */
//    @NotBlank(message = "地区编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String regionCode;

    /**
     * 地区名称
     */
//    @NotBlank(message = "地区名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String regionName;

    /**
     * 地区缩写
     */
//    @NotBlank(message = "地区缩写不能为空", groups = { AddGroup.class, EditGroup.class })
    private String regionAbbreviation;

    /**
     * 中文名
     */
//    @NotBlank(message = "中文名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String regionNameI18n;

    /**
     * 显示名
     */
//    @NotBlank(message = "显示名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String regionNameLocal;

    /**
     * 层级1-国家，2-省，3-市，4-区 5-街道
     */
//    @NotNull(message = "层级1-国家，2-省，3-市，4-区 5-街道不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long regionLevel;

    /**
     * 显示顺序，正序显示
     */
//    @NotNull(message = "显示顺序，正序显示不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long orderNum;

    /**
     * 状态 (正常-NOR，关闭-DIS)
     */
//    @NotBlank(message = "状态 (正常-NOR，关闭-DIS)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String status;

}
