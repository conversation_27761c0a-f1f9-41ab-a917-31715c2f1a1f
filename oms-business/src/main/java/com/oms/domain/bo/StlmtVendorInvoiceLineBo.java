package com.oms.domain.bo;

import com.oms.common.core.domain.BaseEntityBo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.oms.common.core.domain.BaseEntity;
import com.oms.common.core.validate.AddGroup;
import com.oms.common.core.validate.EditGroup;
import javax.validation.constraints.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * 结算供应商发票行业务对象 stlmt_vendor_invoice_line
 *
 * <AUTHOR>
 * @date 2025-04-04
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class StlmtVendorInvoiceLineBo extends BaseEntityBo {

    /**
     * 源单类型 1对账单 2采购单
     */
    private String vendorBillType;

    /**
     * 行数据类型 1.采购，2.采退
     */
    private String lineType;

    /**
     * 结算供应商发票头id
     */
    private Long invoiceHeaderId;

    /**
     * 客户预占订单号
     */
    private String outId;

    /**
     * 预占订单id
     */
    private Long preSellerId;

    /**
     * 销售订单id
     */
    private Long orderSellerId;

    /**
     * 销售订单行id
     */
    private Long orderSellerLineId;

    /**
     * 采购订单id
     */
    private Long orderId;

    /**
     * 采购订单id集合 用于新增发票明细 选择采购单数据
     */
    private List<Long> orderIds;

    /**
     * 采购订单行id
     */
    private Long orderLineId;
    /**
     * 包裹单id
     */
    private Long packageOrderId;

    /**
     * 对账单id
     */
    private Long billId;

    /**
     * 对账单行id
     */
    private Long billLineId;
    /**
     * 结算采购单id
     */
    private Long stlmtOrderId;
    /**
     * 结算采购单行id
     */
    private Long stlmtOrderLineId;

    /**
     * 商品id
     */
    private Long goodsItemId;

    /**
     * 商品id查询 多个逗号分割
     */
    private String goodsItemIds;

    /**
     * 商品名称
     */
    private String goodsItemName;

    /**
     * skuid
     */
    private Long goodsSkuId;

    /**
     * skuid查询 多个逗号分割
     */
    private String goodsSkuIds;

    /**
     * sku编码
     */
    private String goodsSkuCode;

    /**
     * 商品型号
     */
    private String goodsItemSpec;

    /**
     * 商品单位
     */
    private String goodsItemUnit;

    /**
     * 税收分类编码
     */
    private String taxTypeCode;

    /**
     * 税收分类名称
     */
    private String taxTypeName;

    /**
     * 开票商品税率
     */
    private BigDecimal goodsItemTax;

    /**
     * 开票商品数量
     */
    private Long invoiceQuantity;

    /**
     * 开票商品单价
     */
    private BigDecimal invoicePrice;

    /**
     * 开票商品税额
     */
    private BigDecimal invoiceTaxAmount;

    /**
     * 状态 正常NOR 作废DIS
     */
    private String status;
}
