package com.oms.domain.bo;

import com.oms.common.core.domain.BaseEntity;
import com.oms.common.core.validate.AddGroup;
import com.oms.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.Set;

/**
 * 规则字段业务对象 rule_field
 *
 * <AUTHOR>
 * @date 2024-11-09
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class RuleFieldBo extends BaseEntity {

    /**
     * 规则字段ID
     */
    @NotNull(message = "规则字段ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 规则维度ID
     */
    @NotNull(message = "规则维度ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long dimId;

    /**
     * 规则类型id
     */
    @NotNull(message = "规则类型id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long ruleTypeId;

    /**
     * 字段类型：0 默认匹配条件,1固定条件,2固定返回
     */
    @NotNull(message = "字段类型：0 默认匹配条件,1固定条件,2固定返回不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long filedType;

    /**
     * 排序
     */
    @NotNull(message = "排序不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long sort;

    /**
     * 是否必填 ：0否 1是
     */
    @NotNull(message = "是否必填 ：0否 1是不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long required;

    /**
     * 返回类型：1 固定值,2 表达式,3动态调用方法
     */
    @NotNull(message = "返回类型：1 固定值,2 表达式,3动态调用方法不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long returnType;

    /**
     * 状态 1启用 -1禁用
     */
    @NotNull(message = "状态 1启用 -1禁用", groups = { AddGroup.class, EditGroup.class })
    private Integer status;

    /**
     * 中台编码: item商品 ,order 订单
     */
    @NotNull(message = "中台编码", groups = { AddGroup.class, EditGroup.class })
    private String centerCode;


    /**
     * 规则类型id集合
     */
    private Set<Long> ruleTypeIdList;


}
