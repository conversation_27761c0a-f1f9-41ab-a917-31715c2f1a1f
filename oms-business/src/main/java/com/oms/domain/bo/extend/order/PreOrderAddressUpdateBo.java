package com.oms.domain.bo.extend.order;

import com.oms.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 采购订单申请业务对象 pre_order
 *
 * <AUTHOR>
 * @date 2024-09-08
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class PreOrderAddressUpdateBo extends BaseEntity {

    /**
     * 采购申请单id
     */
    private Long id;

    /**
     * 项目ID
     */
    private Long prjId;

    /**
     * 省id
     */
    private Long provinceId;

    /**
     * 省名称
     */
    private String provinceName;

    /**
     * 市id
     */
    private Long cityId;

    /**
     * 市名称
     */
    private String cityName;

    /**
     * 区域Id
     */
    private Long regionId;

    /**
     * 区名称
     */
    private String regionName;

    /**
     * 街道id
     */
    private Long streetId;

    /**
     * 街道名称
     */
    private String streetName;

    /**
     * 收货详细地址
     */
    private String receiveAddressDetail;

    /**
     * 收货人名称
     */
    private String receiveUserName;

    /**
     * 收货人电话
     */
    private String receiveMobile;


    public String getFullAddress(){
        return this.provinceName + this.cityName + this.regionName + this.receiveAddressDetail;
    }

}
