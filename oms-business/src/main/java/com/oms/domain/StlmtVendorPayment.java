package com.oms.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.oms.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 供应商付款单对象 stlmt_vendor_payment
 *
 * <AUTHOR>
 * @date 2025-03-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("stlmt_vendor_payment")
public class StlmtVendorPayment extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 付款编号
     */
    private String paymentNo;
    /**
     * 供应商ID
     */
    private Long vendorId;
    /**
     * 供应商名称
     */
    private String vendorName;
    /**
     * 客户发票号
     */
    private String invoiceNo;
    /**
     * 付款日期
     */
    private Date paymentDate;
    /**
     * 付款金额
     */
    private BigDecimal paymentAmount;
    /**
     * 付款备注
     */
    private String paymentRemark;
    /**
     * 付款类型: PURCHASE_PAYMENT(采购付款) EXPENSE_PAYMENT(费用付款)
     */
    private String paymentType;
    /**
     * 状态: PENDING-待提交, SUBMITTED-已提交, CANCELED-已作废
     */
    private String status;
    /**
     * 收款人
     */
    private String payerName;
    /**
     * 收款人银行账号
     */
    private String payerAccount;
    /**
     * 收款人银行名称
     */
    private String payerBank;
    /**
     * 银行流水号
     */
    private String txnSn;
    /**
     * 附件链接
     */
    private String attachmentUrl;
    /**
     * 提交人ID
     */
    private Long submitId;
    /**
     * 提交人名称
     */
    private String submitName;
    /**
     * 提交时间
     */
    private Date submitTime;

}


