package com.oms.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.oms.common.core.domain.BaseEntityVo;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 商品税收视图对象 goods_tax_type
 *
 * <AUTHOR>
 * @date 2024-08-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ExcelIgnoreUnannotated
public class GoodsTaxTypeVo extends BaseEntityVo {

	private static final long serialVersionUID = 1L;
    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;

    /**
     * 税收分类编码
     */
    @ExcelProperty(value = "税收分类编码")
    private String taxTypeCode;

    /**
     * 税收分类名称
     */
    @ExcelProperty(value = "税收分类名称")
    private String taxTypeName;

    /**
     * 说明
     */
    @ExcelProperty(value = "说明")
    private String remark;

    /**
     * 关键字
     */
    @ExcelProperty(value = "关键字")
    private String keyword;

    /**
     * 编码版本
     */
    @ExcelProperty(value = "编码版本")
    private String ver;

    /**
     * 父节点ID
     */
    @ExcelProperty(value = "父节点ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long pid;

    /**
     * 编
     */
    @ExcelProperty(value = "编")
    private String avtlist;

    /**
     * 类
     */
    @ExcelProperty(value = "类")
    private String avtclass;

    /**
     * 章
     */
    @ExcelProperty(value = "章")
    private String avtchapter;

    /**
     * 节
     */
    @ExcelProperty(value = "节")
    private String avtsection;

    /**
     * 条
     */
    @ExcelProperty(value = "条")
    private String avtiterm;

    /**
     * 款
     */
    @ExcelProperty(value = "款")
    private String avtclause;

    /**
     * 项
     */
    @ExcelProperty(value = "项")
    private String avtarticle;

    /**
     * 目
     */
    @ExcelProperty(value = "目")
    private String avtdetail;

    /**
     * 子目
     */
    @ExcelProperty(value = "子目")
    private String avtchilddetail;

    /**
     * 细目
     */
    @ExcelProperty(value = "细目")
    private String avtcarefuldetail;

    /**
     * 状态 (正常-NOR，关闭-DIS)
     */
    @ExcelProperty(value = "状态 (正常-NOR，关闭-DIS)")
    private String status;
}
