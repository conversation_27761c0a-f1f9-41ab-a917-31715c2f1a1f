package com.oms.domain.vo.extend.order;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.oms.domain.OrderOperationInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 包裹单视图对象 order_package
 *
 * <AUTHOR>
 * @date 2024-09-27
 */
@Data
@ExcelIgnoreUnannotated
public class OrderPackageQueryVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 包裹单id
     */
    @ExcelProperty(value = "包裹单id")
    private Long id;

    /**
     * 项目ID
     */
    @ExcelProperty(value = "项目ID")
    private Long prjId;

    /**
     * 项目名称
     */
    @ExcelProperty(value = "项目名称")
    private String prjName;

    /**
     * 采购单id
     */
    @ExcelProperty(value = "采购单id")
    private Long orderId;

    /**
     * 外部客户预占订单号
     */
    @ExcelProperty(value = "外部客户预占订单号")
    private String outId;

    /**
     * 供应商id
     */
    @ExcelProperty(value = "供应商id")
    private Long shopId;


    /**
     * 供应商id
     */
    private String shopName;

    /**
     * 物流单状态. 1, 已发货; 2: 已确认收货
     */
    @ExcelProperty(value = "物流单状态. 1, 已发货; 2: 已确认收货")
    private Integer status;

    /**
     * 运单号
     */
    @ExcelProperty(value = "运单号")
    private String trackingNumber;

    /**
     * 物流公司编号
     */
    @ExcelProperty(value = "物流公司编号")
    private String corpCode;

    /**
     * 物流公司名称
     */
    @ExcelProperty(value = "物流公司名称")
    private String corpName;

    /**
     * 扩展字段
     */
    @ExcelProperty(value = "扩展字段")
    private String extraJson;

    /**
     * 销售订单id
     */
    @ExcelProperty(value = "销售订单id")
    private Long sellerOrderId;

    /**
     * 妥投状态:  UN_DELIVERED 未妥投  DELIVERED 已妥投， PART_DELIVERED 部分妥投
     */
    @ExcelProperty(value = "妥投状态:  UN_DELIVERED 未妥投  DELIVERED 已妥投， PART_DELIVERED 部分妥投")
    private String deliveredStatus;

    /**
     * 妥投时间
     */
    @ExcelProperty(value = "妥投时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date deliveredTime;


    /**
     * 可妥投时间时间
     */
    @ExcelProperty(value = "可妥投时间时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date canDeliveredTime;

    /**
     * 妥投url
     */
    @ExcelProperty(value = "妥投url")
    private String deliveredUrl;


    /**
     * 发货凭证url
     */
    private String deliveryUrl;

//    /**
//     * 拒收状态: 0未拒收 1已拒收，2部分拒收
//     */
//    @ExcelProperty(value = "拒收状态")
//    private String rejectionStatus;
//
//    /**
//     * 拒收时间
//     */
//    @ExcelProperty(value = "拒收时间")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    private Date rejectionTime;


    /**
     * 虚拟妥投状态:  UN_DELIVERED  DELIVERED， PART_DELIVERED
     */
    private String virtualDeliveredStatus;
    /**
     * 虚拟妥投时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date virtualDeliveredTime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;


    /**
     * 收货地址详细信息
     */
    private String shippingAddress;


    /**
     * 收货人手机号
     */
    private String receiveMobile;

    /**
     * 面单URL
     */
    private String receiptUrl;


    /**
     * 可操作的按钮列表
     */
    private List<OrderOperationInfo> operations;

}
