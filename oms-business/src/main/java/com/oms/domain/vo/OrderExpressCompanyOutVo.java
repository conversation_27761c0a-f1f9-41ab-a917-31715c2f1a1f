package com.oms.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.oms.common.annotation.ExcelDictFormat;
import com.oms.common.convert.ExcelDictConvert;
import lombok.Data;

import java.io.Serializable;

/**
 * 项目物流公司信息视图对象 order_express_company_out
 *
 * <AUTHOR>
 * @date 2024-11-02
 */
@Data
@ExcelIgnoreUnannotated
public class OrderExpressCompanyOutVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @ExcelProperty(value = "主键id")
    private Long id;

    /**
     * 租户ID
     */
    @ExcelProperty(value = "租户ID")
    private Long tenantId;

    /**
     * 系统物流ID
     */
    @ExcelProperty(value = "系统物流ID")
    private Long expressCompanyId;

    /**
     * 系统物流名称
     */
    @ExcelProperty(value = "系统物流名称")
    private String expressCompanyName;

    /**
     * 系统物流编码
     */
    @ExcelProperty(value = "系统物流编码")
    private String expressCompanyCode;

    /**
     * 外部系统物流名称
     */
    @ExcelProperty(value = "外部系统物流名称")
    private String outCompanyName;

    /**
     * 外部系统物流编码
     */
    @ExcelProperty(value = "外部系统物流编码")
    private String outCompanyCode;

    /**
     * 状态
     */
    @ExcelProperty(value = "状态")
    private Integer sts;

    /**
     * 区域运营id
     */
    @ExcelProperty(value = "区域运营id")
    private Long prjId;

    /**
     * 区域运营名称
     */
    @ExcelProperty(value = "区域运营名称")
    private String prjName;

    /**
     * 更新人名
     */
    @ExcelProperty(value = "更新人名")
    private String updateName;

    /**
     * 创建人名
     */
    @ExcelProperty(value = "创建人名")
    private String createName;

    /**
     * 逻辑删除 (1-已删除，0-未删除)
     */
    @ExcelProperty(value = "逻辑删除 (1-已删除，0-未删除)")
    private Long deleted;

    /**
     * 删除时间
     */
    @ExcelProperty(value = "删除时间")
    private Date deleteTime;

    /**
     * 删除人主键
     */
    @ExcelProperty(value = "删除人主键")
    private Long deleteBy;

    /**
     * 删除人名
     */
    @ExcelProperty(value = "删除人名")
    private String deleteName;


}
