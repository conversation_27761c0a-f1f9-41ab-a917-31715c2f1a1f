package com.oms.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.oms.common.annotation.ExcelDictFormat;
import com.oms.common.convert.ExcelDictConvert;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 供应商付款单视图对象 stlmt_vendor_payment
 *
 * <AUTHOR>
 * @date 2025-03-24
 */
@Data
@ExcelIgnoreUnannotated
public class StlmtVendorPaymentVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 付款编号
     */
    @ExcelProperty(value = "付款编号")
    private String paymentNo;

    /**
     * 供应商ID
     */
    @ExcelProperty(value = "供应商ID")
    private Long vendorId;

    /**
     * 供应商名称
     */
    @ExcelProperty(value = "供应商名称")
    private String vendorName;

    /**
     * 客户发票号
     */
    @ExcelProperty(value = "客户发票号")
    private String invoiceNo;

    /**
     * 付款日期
     */
    @ExcelProperty(value = "付款日期")
    private Date paymentDate;

    /**
     * 付款金额
     */
    @ExcelProperty(value = "付款金额")
    private BigDecimal paymentAmount;

    /**
     * 付款备注
     */
    @ExcelProperty(value = "付款备注")
    private String paymentRemark;

    /**
     * 付款类型: PURCHASE_PAYMENT(采购付款) EXPENSE_PAYMENT(费用付款)
     */
//    @ExcelProperty(value = "付款类型: PURCHASE_PAYMENT(采购付款) EXPENSE_PAYMENT(费用付款)", converter = ExcelDictConvert.class)
//    @ExcelDictFormat(dictType = "vendor_payment_type")
//    private String paymentType;

    /**
     * 状态: PENDING-待提交, SUBMITTED-已提交, CANCELED-已作废
     */
    @ExcelProperty(value = "状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "customer_payment_status")
    private String status;

    /**
     * 收款人
     */
    @ExcelProperty(value = "收款人")
    private String payerName;

    /**
     * 收款人银行账号
     */
    @ExcelProperty(value = "收款人银行账号")
    private String payerAccount;

    /**
     * 收款人银行名称
     */
    @ExcelProperty(value = "收款人银行名称")
    private String payerBank;

    /**
     * 银行流水号
     */
    @ExcelProperty(value = "银行流水号")
    private String txnSn;

    /**
     * 提交人ID
     */
    @ExcelProperty(value = "提交人ID")
    private Long submitId;

    /**
     * 提交人名称
     */
    @ExcelProperty(value = "提交人名称")
    private String submitName;

    /**
     * 提交时间
     */
    @ExcelProperty(value = "提交时间")
    private Date submitTime;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 创建人姓名
     */
    @ExcelProperty(value = "创建人姓名")
    private String createName;

    /**
     * 最后更新人姓名
     */
    @ExcelProperty(value = "最后更新人姓名")
    private String updateName;


}
