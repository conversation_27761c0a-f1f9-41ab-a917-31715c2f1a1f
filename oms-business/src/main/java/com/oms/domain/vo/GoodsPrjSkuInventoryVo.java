package com.oms.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.oms.common.annotation.ExcelDictFormat;
import com.oms.common.convert.ExcelDictConvert;
import com.oms.common.core.domain.BaseEntityVo;
import lombok.Data;

import java.io.Serializable;

/**
 * 项目商品sku库存视图对象 goods_prj_sku_inventory
 *
 * <AUTHOR>
 * @date 2025-03-20
 */
@Data
@ExcelIgnoreUnannotated
public class GoodsPrjSkuInventoryVo extends BaseEntityVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 项目id
     */
    @ExcelProperty(value = "项目id")
    private Long prjId;

    /**
     * 商品id
     */
    @ExcelProperty(value = "商品id")
    private Long itemId;

    /**
     * skuid
     */
    @ExcelProperty(value = "skuid")
    private Long skuId;

    /**
     * 区域id 全国为1 默认库存为-1
     */
    @ExcelProperty(value = "区域id 全国为1 默认库存为-1")
    private Long areaId;

    /**
     * 库存数量
     */
    @ExcelProperty(value = "库存数量")
    private Long inventory;

    /**
     * 有效期开始
     */
    @ExcelProperty(value = "有效期开始")
    private Date validityStart;

    /**
     * 有效期截止
     */
    @ExcelProperty(value = "有效期截止")
    private Date validityEnd;

    /**
     * 是否长期 Y是 N否
     */
    @ExcelProperty(value = "是否长期 Y是 N否")
    private String isLong;

    /**
     * 状态 (正常-NOR，关闭-DIS)
     */
    @ExcelProperty(value = "状态 (正常-NOR，关闭-DIS)")
    private String status;
}
