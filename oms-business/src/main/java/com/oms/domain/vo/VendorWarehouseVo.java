package com.oms.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.oms.common.annotation.ExcelDictFormat;
import com.oms.common.convert.ExcelDictConvert;
import lombok.Data;

import java.io.Serializable;

/**
 * 供应商仓库绑定视图对象 vendor_warehouse
 *
 * <AUTHOR>
 * @date 2025-01-04
 */
@Data
@ExcelIgnoreUnannotated
public class VendorWarehouseVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 租户
     */
    @ExcelProperty(value = "租户")
    private Long tenantId;

    /**
     * 供应商id
     */
    @ExcelProperty(value = "供应商id")
    private Long vendorId;

    /**
     * 仓库id
     */
    @ExcelProperty(value = "仓库id")
    private Long warehouseId;

    /**
     * 仓库code
     */
    @ExcelProperty(value = "仓库code")
    private String warehouseCode;

    /**
     * 仓库code
     */
    @ExcelProperty(value = "仓库名称")
    private String warehouseName;

    /**
     * 区域code
     */
    @ExcelProperty(value = "区域code")
    private String areaCode;

    /**
     * 区域名称
     */
    @ExcelProperty(value = "区域名称")
    private String areaName;

    /**
     * 创建人名
     */
    @ExcelProperty(value = "创建人名")
    private String createName;

    /**
     * 更新人名
     */
    @ExcelProperty(value = "更新人名")
    private String updateName;

    /**
     * 逻辑删除 (1-已删除，0-未删除)
     */
    @ExcelProperty(value = "逻辑删除 (1-已删除，0-未删除)")
    private Integer deleted;

    /**
     * 删除时间
     */
    @ExcelProperty(value = "删除时间")
    private Date deleteTime;

    /**
     * 删除人
     */
    @ExcelProperty(value = "删除人")
    private Long deleteBy;

    /**
     * 删除人名
     */
    @ExcelProperty(value = "删除人名")
    private String deleteName;


}
