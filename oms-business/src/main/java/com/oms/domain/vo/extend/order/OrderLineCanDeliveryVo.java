package com.oms.domain.vo.extend.order;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.oms.domain.OrderOperationInfo;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 采购订单行视图对象 order_line
 *
 * <AUTHOR>
 * @date 2024-09-20
 */
@Data
@ExcelIgnoreUnannotated
public class OrderLineCanDeliveryVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 采购申请单行id
     */
    @ExcelProperty(value = "采购申请单行id")
    private Long id;

    /**
     * 项目ID
     */
    @ExcelProperty(value = "项目ID")
    private Long prjId;

    /**
     * 项目名称
     */
    @ExcelProperty(value = "项目名称")
    private String prjName;

    /**
     * 客户预占订单号
     */
    @ExcelProperty(value = "客户预占订单号")
    private String outId;

    /**
     * 采购订单ID
     */
    @ExcelProperty(value = "采购订单ID")
    private Long orderId;

    /**
     * 客户id
     */
    @ExcelProperty(value = "客户id")
    private Long buyerId;

    /**
     * 客户名称
     */
    @ExcelProperty(value = "客户名称")
    private String buyerName;

    /**
     * 供应商id
     */
    @ExcelProperty(value = "供应商id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long shopId;

    /**
     * 供应商名称
     */
    @ExcelProperty(value = "供应商名称")
    private String shopName;


    /**
     * sku id
     */
    @ExcelProperty(value = "sku id")
    private Long skuId;

    /**
     * sku code
     */
    @ExcelProperty(value = "sku code")
    private String skuCode;

    /**
     * sku 名称
     */
    @ExcelProperty(value = "sku 名称")
    private String skuName;

    /**
     * sku 缩率图
     */
    @ExcelProperty(value = "sku 缩率图")
    private String skuImage;

    /**
     * sku 销售属性
     */
    @ExcelProperty(value = "sku 销售属性")
    private String skuAttr;

    /**
     * 数量
     */
    @ExcelProperty(value = "数量")
    private Long quantity;

    /**
     * 实际支付金额
     */
    @ExcelProperty(value = "实际支付金额")
    private BigDecimal paidAmount;

    /**
     * 商品原价总价;
     */
    @ExcelProperty(value = "商品原价总价")
    private BigDecimal skuOriginTotalAmount;

    /**
     * 商品总优惠;
     */
    @ExcelProperty(value = "商品总优惠;")
    private BigDecimal skuDiscountTotalAmount;

    /**
     * 运费总价;
     */
    @ExcelProperty(value = "运费总价;")
    private BigDecimal shipFeeOriginAmount;

    /**
     * 运费金额总优惠;
     */
    @ExcelProperty(value = "运费金额总优惠;")
    private BigDecimal shipFeeDiscountTotalAmount;

    /**
     * 优惠明细;
     */
    @ExcelProperty(value = "优惠明细;")
    private String discountDetail;


    /**
     * 组合商品的主商品id, 如果该商品为主商品, 则为null
     */
    @ExcelProperty(value = "组合商品的主商品id, 如果该商品为主商品, 则为null")
    private Long masterId;

    /**
     * 拓展字段
     */
    @ExcelProperty(value = "拓展字段")
    private String extraJson;

    /**
     * 扩展信息;
     */
    @ExcelProperty(value = "扩展信息;")
    private String skuExtra;

    /**
     * 商品id
     */
    @ExcelProperty(value = "商品id")
    private Long itemId;

    /**
     * 商品名称
     */
    @ExcelProperty(value = "商品名称")
    private String itemName;

    /**
     * 可以近似理解为order按sku维度拆分的Line id
     */
    @ExcelProperty(value = "可以近似理解为order按sku维度拆分的Line id")
    private Long skuLineId;

    /**
     * sku 品牌ID
     */
    @ExcelProperty(value = "sku 品牌ID")
    private Long skuBrandId;

    /**
     * sku 品牌名称
     */
    @ExcelProperty(value = "sku 品牌名称")
    private String skuBrandName;

    /**
     * sku类目ID
     */
    @ExcelProperty(value = "sku类目ID")
    private Long skuCategoryId;

    /**
     * sku 类目名称
     */
    @ExcelProperty(value = "sku 类目名称")
    private String skuCategoryName;

    /**
     * sku 一级类目ID
     */
    @ExcelProperty(value = "sku 一级类目ID")
    private Long skuCategoryIdOneLevel;

    /**
     * sku 一级类目名称
     */
    @ExcelProperty(value = "sku 一级类目名称")
    private String skuCategoryNameOneLevel;

    /**
     * sku 二级类目ID
     */
    @ExcelProperty(value = "sku 二级类目ID")
    private Long skuCategoryIdTwoLevel;

    /**
     * sku 二级类目名称
     */
    @ExcelProperty(value = "sku 二级类目名称")
    private String skuCategoryNameTwoLevel;

    /**
     * sku 三级类目ID
     */
    @ExcelProperty(value = "sku 三级类目ID")
    private Long skuCategoryIdThreeLevel;

    /**
     * sku 三级类目名称
     */
    @ExcelProperty(value = "sku 三级类目名称")
    private String skuCategoryNameThreeLevel;

    /**
     * sku 四级类目ID
     */
    @ExcelProperty(value = "sku 四级类目ID")
    private Long skuCategoryIdFourLevel;

    /**
     * sku 四级类目名称
     */
    @ExcelProperty(value = "sku 四级类目名称")
    private String skuCategoryNameFourLevel;

    /**
     * 已发货数量
     */
    @ExcelProperty(value = "已发货数量")
    private Long shippedQuantity;

    /**
     * 区域商品ID
     */
    @ExcelProperty(value = "区域商品ID")
    private Long areaSkuId;

    /**
     * 是否支持退货
     */
    @ExcelProperty(value = "是否支持退货")
    private Integer supportReturn;

    /**
     * 组合商品ID;
     */
    @ExcelProperty(value = "组合商品ID;")
    private String bundleId;

    /**
     * sku 规格属性组名称(逗号分隔)
     */
    @ExcelProperty(value = "sku 规格属性组名称(逗号分隔)")
    private String skuAttrKey;

    /**
     * sku 规格属性名称(逗号分隔)
     */
    @ExcelProperty(value = "sku 规格属性名称(逗号分隔)")
    private String skuAttrVal;

    /**
     * 底价
     */
    @ExcelProperty(value = "底价")
    private BigDecimal basePrice;

    /**
     * 费率
     */
    @ExcelProperty(value = "费率")
    private BigDecimal feeRate;


    /**
     * 可发货数量
     */
    private Long canShipQuantity;



}
