package com.oms.domain.vo;


import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 审批变量视图对象 approval_config_variable
 *
 * <AUTHOR>
 * @date 2024-10-15
 */
@Data
@ExcelIgnoreUnannotated
public class ApprovalConfigVariableVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 
     */
    @ExcelProperty(value = "")
    private Long id;

    /**
     * 审批表id
     */
    @ExcelProperty(value = "审批表id")
    private Long approvalId;

    /**
     * 变量 1-项目等级  2-采购金额
     */
    @ExcelProperty(value = "变量 1-项目等级  2-采购金额")
    private String variableType;

    /**
     * 条件 = 等于，&gt; 大于, = 大于等于, &lt;= 小于等于, in 在列表,  not in 不在列表
     */
    @ExcelProperty(value = "条件 = 等于，&gt; 大于, = 大于等于, &lt;= 小于等于, in 在列表,  not in 不在列表")
    private String requirementType;

    /**
     * 变量值
     */
    @ExcelProperty(value = "变量值")
    private String value;


}
