package com.oms.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.oms.common.annotation.ExcelDictFormat;
import com.oms.common.convert.ExcelDictConvert;
import lombok.Data;

import java.io.Serializable;

/**
 * 对外平台消息视图对象 message_pool
 *
 * <AUTHOR>
 * @date 2024-09-29
 */
@Data
@ExcelIgnoreUnannotated
public class MessagePoolVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 租户id
     */
    @ExcelProperty(value = "租户id")
    private Long tenantId;

    /**
     * 项目ID
     */
    @ExcelProperty(value = "项目ID")
    private Long prjId;

    /**
     * 项目名称
     */
    @ExcelProperty(value = "项目名称")
    private String prjName;

    /**
     * 业务id(跟着消息类型走)
     */
    @ExcelProperty(value = "业务id(跟着消息类型走)")
    private Long businessId;

    /**
     * 业务id(跟着消息类型走)
     */
    @ExcelProperty(value = "业务id(跟着消息类型走)")
    private String outBusinessId;

    /**
     * 消息类型 1：商品消息 2：订单消息
     */
    @ExcelProperty(value = "消息类型 1：商品消息 2：订单消息")
    private Long messageType;

    /**
     * 操作类型，如同步商品,同步订单状态(系统中自定义)
     */
    @ExcelProperty(value = "操作类型，如同步商品,同步订单状态(系统中自定义)")
    private String operationType;

    /**
     * 0:未处理；1:已处理
     */
    @ExcelProperty(value = "0:未处理；1:已处理")
    private Long status;

    /**
     * 拓展字段
     */
    @ExcelProperty(value = "拓展字段")
    private String content;

    /**
     * 更新人名
     */
    @ExcelProperty(value = "更新人名")
    private String updateName;

    /**
     * 创建人名
     */
    @ExcelProperty(value = "创建人名")
    private String createName;

    /**
     * 逻辑删除 (1-已删除，0-未删除)
     */
    @ExcelProperty(value = "逻辑删除 (1-已删除，0-未删除)")
    private Long deleted;

    /**
     * 删除时间
     */
    @ExcelProperty(value = "删除时间")
    private Date deleteTime;

    /**
     * 删除人主键
     */
    @ExcelProperty(value = "删除人主键")
    private Long deleteBy;

    /**
     * 删除人名
     */
    @ExcelProperty(value = "删除人名")
    private String deleteName;


}
