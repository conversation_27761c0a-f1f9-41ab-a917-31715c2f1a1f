package com.oms.domain.vo.extend.order;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.oms.domain.OrderOperationInfo;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 采购订单视图对象 order
 *
 * <AUTHOR>
 * @date 2024-09-20
 */
@Data
@ExcelIgnoreUnannotated
public class ShipContractLineVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 序号
     */
    @ExcelProperty(value = "序号")
    private Integer index;

    /**
     * 项目ID
     */
    @ExcelProperty(value = "商品编码")
    private String skuId;

    /**
     * 商品名称
     */
    @ExcelProperty(value = "商品名称")
    private String skuName;


    @ExcelProperty(value = "发货数量")
    private Long quantity;


    @ExcelProperty(value = "采购单价")
    private BigDecimal poPrice;


    @ExcelProperty(value = "采购金额")
    private BigDecimal totalPoPrice;


}
