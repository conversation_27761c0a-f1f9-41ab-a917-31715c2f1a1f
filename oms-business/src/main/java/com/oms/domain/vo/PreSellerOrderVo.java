package com.oms.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.oms.common.annotation.ExcelEnumFormat;
import com.oms.common.convert.ExcelEnumConvert;
import com.oms.common.enums.OrderTypeEnum;
import com.oms.domain.OrderOperationInfo;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 预销售订单视图对象 seller_order
 *
 * <AUTHOR>
 * @date 2024-08-28
 */
@Data
@ExcelIgnoreUnannotated
public class PreSellerOrderVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "预占订单ID")
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 外部订单id
     */
    @ExcelProperty(value = "预占订单")
    private String outId;

    /**
     * 购物单id
     */
    private String purchaseOrderId;


    /**
     * 客户ID
     */
    private Long buyerId;

    /**
     * 买家姓名
     */
    @ExcelProperty(value = "客户名称")
    private String buyerName;

    /**
     * 实际支付金额
     */
    @ExcelProperty(value = "实际支付金额")
    private BigDecimal paidAmount;

    /**
     * 运费原价总价
     */
    private BigDecimal shipFeeOriginAmount;

    /**
     * 商品原价总价
     */
    private BigDecimal skuOriginTotalAmount;

    /**
     * 省id
     */
    private Long provinceId;
    /**
     * 省名称
     */
    private String provinceName;
    /**
     * 市id
     */
    private Long cityId;
    /**
     * 区域Id
     */
    private Long regionId;
    /**
     * 区名称
     */
    private String regionName;
    /**
     * 市名称
     */
    private String cityName;
    /**
     * 街道id
     */
    private Long streetId;
    /**
     * 街道名称
     */
    private String streetName;
    /**
     * 收货详细地址
     */
    private String receiveAddressDetail;


    /**
     * 买家留言
     */
    @ExcelProperty(value = "买家留言")
    private String buyerNotes;

    /**
     * 发货描述
     */
    @ExcelProperty(value = "发货描述")
    private String shopNotes;

    /**
     * 打标
     */
    private Long tag;

    /**
     * 拓展字段
     */
    private String extraJson;


    /**
     * 项目id
     */
    private Long prjId;

    /**
     * 区域运营商名称
     */
    @ExcelProperty(value = "项目名称")
    private String prjName;


    /**
     * 数电发票类型：1：数电专票(纸质) 2：数电专票(电子)   3:数电普票(纸质)  4：数电普票(电子)
     */
    @ExcelProperty(value = "发票类型")
    private Long invoiceType;

    /**
     * 发票抬头
     */
    @ExcelProperty(value = "发票抬头")
    private String invoiceTitle;

    /**
     * OMS订单号
     */
    @ExcelProperty(value = "OMS订单号")
    private String sourceId;

    /**
     * 收货人名称
     */
    @ExcelProperty(value = "收货人名称")
    private String receiveUserName;

    /**
     * 收货人电话
     */
    @ExcelProperty(value = "收货人电话")
    private String receiveMobile;

    /**
     * 采购单位名称
     */
    @ExcelProperty(value = "采购单位名称")
    private String purchaseCompany;

    /**
     * 下单人姓名
     */
    @ExcelProperty(value = "下单人姓名")
    private String placeOrderName;

    /**
     * 下单人电话
     */
    @ExcelProperty(value = "下单人电话")
    private String placeOrderMobile;

    /**
     * 订单类型：0：接口订单  1：手工订单   2：商城订单  3：手工导入订单
     */
    @ExcelProperty(value = "订单类型", converter = ExcelEnumConvert.class)
    @ExcelEnumFormat(enumClass = OrderTypeEnum.class, textField = "info")
    private Long orderType;

    /**
     * 取消原因
     */
    private String cancelReason;

    /**
     * 价格类型：0含税，1不含税  默认含税
     */
    private Long priceType;

    /**
     * 客户采购单号
     */
    @ExcelProperty(value = "客户采购单号")
    private String applyCode;

    /**
     * 虚拟发货单号
     */
    private String virtualShipCode;

    /**
     * 收货单位
     */
    private String receiveCompany;


    /**
     * 预占单审核状态：0 :未确认  1：已确认待审核  2：已审核
     */
    @ExcelProperty(value = "确认状态")
    private Long preAuditStatus;

    /**
     * OMS订单创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;


    /**
     * 客户订单下单日期
     */
    @ExcelProperty(value = "外部订单日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date outDate;

    /**
     * 客户订单确认时间
     *
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date preConfirmAt;

    /**
     * 客户确认订单日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date outConfirmDate;

    /**
     * 发票信息json
     */
    private String invoiceExtra;

    /**
     * 订单行
     */
    private List<PreSellerOrderLineVo> orderLines;

    /**
     * 可以操作的按钮
     */
    private List<OrderOperationInfo> operations;

}
