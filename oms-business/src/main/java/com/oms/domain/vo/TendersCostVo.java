package com.oms.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 投标费用视图对象 tenders_cost
 *
 * <AUTHOR>
 * @date 2024-10-25
 */
@Data
@ExcelIgnoreUnannotated
public class TendersCostVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 投标id
     */
    @ExcelProperty(value = "投标id")
    private Long infoId;

    /**
     * 投标标题
     */
    @ExcelProperty(value = "投标标题")
    private String infoTitle;

    /**
     * 费用类型
     */
    @ExcelProperty(value = "费用类型")
    private String costType;

    /**
     * 付款金额
     */
    @ExcelProperty(value = "付款金额")
    private String payAmount;

    /**
     * 付款账号名称
     */
    @ExcelProperty(value = "付款账号名称")
    private String payAccountName;

    /**
     * 付款银行开户行
     */
    @ExcelProperty(value = "付款银行开户行")
    private String payBank;

    /**
     * 付款账号
     */
    @ExcelProperty(value = "付款账号")
    private String payAccount;

    /**
     * 付款备注
     */
    @ExcelProperty(value = "付款备注")
    private String payRemark;

    /**
     * 付款截止时间
     */
    @ExcelProperty(value = "付款截止时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date payDeadline;

    /**
     * 付款凭证
     */
    @ExcelProperty(value = "付款凭证")
    private String payCert;

    /**
     * 费用退回日期
     */
    @ExcelProperty(value = "费用退回日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date costReturnDate;

    /**
     * 发票提供日期
     */
    @ExcelProperty(value = "发票提供日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date invoiceDate;

    /**
     * 状态 NEW-新建 WAIT_AUDIT-审核中 AUDIT_SUCCESS-审核成功 AUDIT_REJECT-审核拒绝
     */
    @ExcelProperty(value = "状态 NEW-新建 WAIT_AUDIT-审核中 AUDIT_SUCCESS-审核成功 AUDIT_REJECT-审核拒绝")
    private String status;

    /**
     * 审核消息
     */
    private String auditMsg;

    /**
     * 付款凭证
     */
    private List<Map<String, Object>> payCertUrlData;
    /**
     * 其他凭证
     */
    private List<Map<String, Object>> otherUrlData;

    /**
     * 是否存在支付凭证
     */
    private Boolean isExitsPayUrl;

    /**
     * 创建人名
     */
    @ExcelProperty(value = "创建人名")
    private String createName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createTime;

    /**
     * 更新人名
     */
    @ExcelProperty(value = "更新人名")
    private String updateName;


    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date updateTime;

}
