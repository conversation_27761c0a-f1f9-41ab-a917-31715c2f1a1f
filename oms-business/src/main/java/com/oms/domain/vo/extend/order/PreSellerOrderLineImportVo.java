package com.oms.domain.vo.extend.order;

import com.alibaba.excel.annotation.ExcelProperty;
import com.oms.common.core.domain.BaseEntity;
import com.oms.common.core.validate.AddGroup;
import com.oms.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 销售订单行业务对象 seller_order_line
 *
 * <AUTHOR>
 * @date 2024-08-28
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class PreSellerOrderLineImportVo extends BaseEntity {

    /**
     * 外部订单号
     */
    @ExcelProperty("客户订单号(必填)")
    @NotBlank(message = "外部订单号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String outId;

    /**
     * sku id
     */
    @ExcelProperty("skuId(必填)")
    @NotNull(message = "sku id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long skuId;

    /**
     * 数量
     */
    @ExcelProperty("数量(必填)")
    @NotNull(message = "数量不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long quantity;

    /**
     * 单价
     */
    @ExcelProperty("单价(必填)")
    @NotNull(message = "单价", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal unitPaidAmount;

    /**
     * 项目ID
     */
    @ExcelProperty("项目id(必填)")
    @NotNull(message = "项目ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long prjId;

    /**
     * 项目名称
     */
    @ExcelProperty("项目名称(必填)")
    @NotBlank(message = "项目名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String prjName;

    /**
     * 采购单位名称
     */
    @ExcelProperty("采购单位名称(必填)")
    @NotBlank(message = "采购单位名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String purchaseCompany;

    /**
     * 发票抬头
     */
    @ExcelProperty("发票抬头(必填)")
    @NotBlank(message = "发票抬头不能为空", groups = { AddGroup.class, EditGroup.class })
    private String invoiceTitle;


    /**
     * 下单人姓名
     */
    @ExcelProperty("下单人姓名(必填)")
    @NotBlank(message = "下单人姓名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String placeOrderName;

    /**
     * 下单人电话
     */
    @ExcelProperty("下单人电话(必填)")
    @NotBlank(message = "下单人电话不能为空", groups = { AddGroup.class, EditGroup.class })
    private String placeOrderMobile;


    /**
     * 收货人姓名
     */
    @ExcelProperty("收货人姓名(必填)")
    @NotBlank(message = "收货人姓名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String receiveUserName;

    /**
     * 收货人电话
     */
    @ExcelProperty("收货人电话(必填)")
    @NotBlank(message = "收货人电话不能为空", groups = { AddGroup.class, EditGroup.class })
    private String receiveMobile;


    /**
     * 外部订单日期
     */
    @ExcelProperty("客户订单日期(必填)")
    @NotBlank(message = "外部订单日期不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date outDate;


    /**
     * 省名称
     */
    @ExcelProperty("省份(必填)")
    @NotBlank(message = "省名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String provinceName;

    /**
     * 市名称
     */
    @ExcelProperty("市(必填)")
    @NotBlank(message = "市名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String cityName;

    /**
     * 区名称
     */
    @ExcelProperty("区(必填)")
    @NotBlank(message = "区名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String regionName;


    /**
     * 收货详细地址
     */
    @ExcelProperty("地址详细信息(必填)")
    @NotBlank(message = "地址详细信息不能为空", groups = { AddGroup.class, EditGroup.class })
    private String receiveAddressDetail;



    /**
     * 发票类型：1增值税普通发票, 2增值税专用发票
     */
    @ExcelProperty("发票类型(专票、普票、电子)(非必填,缺省普票)")
    private Long invoiceType;


    /**
     * 发票注册地址
     */
    @ExcelProperty("注册地址(非必填)")
    private String invoiceRegisterAddress;


    /**
     * 纳税人识别
     */
    @ExcelProperty("纳税人识别号(非必填)")
    private String itins;

    /**
     * 注册电话
     */
    @ExcelProperty("注册电话(非必填)")
    private String registerTelephone;

    /**
     * 开户银行，增值税专票时必填
     */
    @ExcelProperty("开户行名称(非必填)")
    private String regBankName;

    /**
     * 开户银行，增值税专票时必填
     */
    @ExcelProperty("开户行账户(非必填)")
    private String bankAccount;

    /**
     * 客户采购单号
     */
    @ExcelProperty("客户采购订单号")
    private String applyCode;

    /**
     * 买家留言
     */
    @ExcelProperty("备注(非必填)")
    private String buyerNotes;



}
