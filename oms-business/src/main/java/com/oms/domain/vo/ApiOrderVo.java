package com.oms.domain.vo;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.oms.common.annotation.ExcelDictFormat;
import com.oms.common.convert.ExcelDictConvert;
import lombok.Data;

import java.io.Serializable;

/**
 * 预销售订单视图对象 api_order
 *
 * <AUTHOR>
 * @date 2025-05-06
 */
@Data
@ExcelIgnoreUnannotated
public class ApiOrderVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 租户id
     */
    @ExcelProperty(value = "租户id")
    private Long tenantId;

    /**
     * 项目id
     */
    @ExcelProperty(value = "项目id")
    private Long prjId;

    /**
     * 项目名称
     */
    @ExcelProperty(value = "项目名称")
    private String prjName;

    /**
     * 外部订单id
     */
    @ExcelProperty(value = "外部订单id")
    private String outId;

    /**
     * 购物单id
     */
    @ExcelProperty(value = "购物单id")
    private Long purchaseOrderId;

    /**
     * 买家id
     */
    @ExcelProperty(value = "买家id")
    private Long buyerId;

    /**
     * 买家姓名
     */
    @ExcelProperty(value = "买家姓名")
    private String buyerName;

    /**
     * 是否可见, 1: 可见; 0: 不可见
     */
    @ExcelProperty(value = "是否可见, 1: 可见; 0: 不可见")
    private Integer enableStatus;

    /**
     * 支付状态
     */
    @ExcelProperty(value = "支付状态")
    private String payStatus;

    /**
     * 发货状态
     */
    @ExcelProperty(value = "发货状态")
    private String deliveryStatus;

    /**
     * 收货状态
     */
    @ExcelProperty(value = "收货状态")
    private String receiveStatus;

    /**
     * 售后单状态
     */
    @ExcelProperty(value = "售后单状态")
    private String reverseStatus;

    /**
     * 支付完成时间
     */
    @ExcelProperty(value = "支付完成时间")
    private Date payAt;

    /**
     * 发货时间
     */
    @ExcelProperty(value = "发货时间")
    private Date shippingAt;

    /**
     * 确认收货时间
     */
    @ExcelProperty(value = "确认收货时间")
    private Date confirmAt;

    /**
     * 实际支付金额
     */
    @ExcelProperty(value = "实际支付金额")
    private BigDecimal paidAmount;

    /**
     * 原价总价
     */
    @ExcelProperty(value = "原价总价")
    private BigDecimal skuOriginTotalAmount;

    /**
     * 实际总价
     */
    @ExcelProperty(value = "实际总价")
    private BigDecimal skuAdjustAmount;

    /**
     * 优惠总价
     */
    @ExcelProperty(value = "优惠总价")
    private BigDecimal skuDiscountTotalAmount;

    /**
     * 运费原价总价
     */
    @ExcelProperty(value = "运费原价总价")
    private BigDecimal shipFeeOriginAmount;

    /**
     * 运费优惠总价
     */
    @ExcelProperty(value = "运费优惠总价")
    private BigDecimal shipFeeAdjustAmount;

    /**
     * 运费实际总价
     */
    @ExcelProperty(value = "运费实际总价")
    private BigDecimal shipFeeDiscountTotalAmount;

    /**
     * 优惠明细
     */
    @ExcelProperty(value = "优惠明细")
    private String discountDetail;

    /**
     * 买家留言
     */
    @ExcelProperty(value = "买家留言")
    private String buyerNotes;

    /**
     * 发货描述
     */
    @ExcelProperty(value = "发货描述")
    private String shopNotes;

    /**
     * 打标
     */
    @ExcelProperty(value = "打标")
    private Long tag;

    /**
     * 拓展字段
     */
    @ExcelProperty(value = "拓展字段")
    private String extraJson;

    /**
     * 订单完成时间
     */
    @ExcelProperty(value = "订单完成时间")
    private Date accomplishAt;

    /**
     * 区域运营商与供应商合作模式
     */
    @ExcelProperty(value = "区域运营商与供应商合作模式")
    private Long cooperationMode;

    /**
     * 外部订单下单日期(保存接口传入时间)
     */
    @ExcelProperty(value = "外部订单下单日期(保存接口传入时间)")
    private Date outDate;

    /**
     * 外部订单确认时间(保存接口传入时间)
     */
    @ExcelProperty(value = "外部订单确认时间(保存接口传入时间)")
    private Date outConfirmDate;

    /**
     * 发票抬头
     */
    @ExcelProperty(value = "发票抬头")
    private String invoiceTitle;

    /**
     * 发票扩展信息JSON
     */
    @ExcelProperty(value = "发票扩展信息JSON")
    private String invoiceExtra;

    /**
     * 来源单号
     */
    @ExcelProperty(value = "来源单号")
    private String sourceId;

    /**
     * 收货确认时间
     */
    @ExcelProperty(value = "收货确认时间")
    private String receiveConfirmAt;

    /**
     * 收货人名称
     */
    @ExcelProperty(value = "收货人名称")
    private String receiveUserName;

    /**
     * 收货人电话
     */
    @ExcelProperty(value = "收货人电话")
    private String receiveMobile;

    /**
     * 采购单位名称
     */
    @ExcelProperty(value = "采购单位名称")
    private String purchaseCompany;

    /**
     * 省id
     */
    @ExcelProperty(value = "省id")
    private Long provinceId;

    /**
     * 省名称
     */
    @ExcelProperty(value = "省名称")
    private String provinceName;

    /**
     * 市id
     */
    @ExcelProperty(value = "市id")
    private Long cityId;

    /**
     * 市名称
     */
    @ExcelProperty(value = "市名称")
    private String cityName;

    /**
     * 区域Id
     */
    @ExcelProperty(value = "区域Id")
    private Long regionId;

    /**
     * 区名称
     */
    @ExcelProperty(value = "区名称")
    private String regionName;

    /**
     * 街道id
     */
    @ExcelProperty(value = "街道id")
    private Long streetId;

    /**
     * 街道名称
     */
    @ExcelProperty(value = "街道名称")
    private String streetName;

    /**
     * 收货详细地址
     */
    @ExcelProperty(value = "收货详细地址")
    private String receiveAddressDetail;

    /**
     * 下单人姓名
     */
    @ExcelProperty(value = "下单人姓名")
    private String placeOrderName;

    /**
     * 下单人电话
     */
    @ExcelProperty(value = "下单人电话")
    private String placeOrderMobile;

    /**
     * 0：接口订单  1：手工订单   2：商城订单
     */
    @ExcelProperty(value = "0：接口订单  1：手工订单   2：商城订单")
    private Long orderType;

    /**
     * 0.扣费模式 1.成本模式
     */
    @ExcelProperty(value = "0.扣费模式 1.成本模式")
    private Long pattern;

    /**
     * 是否紧急订单标识 1时为紧急订单 默认0非紧急
     */
    @ExcelProperty(value = "是否紧急订单标识 1时为紧急订单 默认0非紧急")
    private Long isEmergency;

    /**
     * 取消原因
     */
    @ExcelProperty(value = "取消原因")
    private String cancelReason;

    /**
     * 价格类型：0含税，1不含税
     */
    @ExcelProperty(value = "价格类型：0含税，1不含税")
    private Long priceType;

    /**
     * 客户采购单号
     */
    @ExcelProperty(value = "客户采购单号")
    private String applyCode;

    /**
     * 虚拟发货单号
     */
    @ExcelProperty(value = "虚拟发货单号")
    private String virtualShipCode;

    /**
     * 收货单位
     */
    @ExcelProperty(value = "收货单位")
    private String receiveCompany;

    /**
     * 报备订单ID
     */
    @ExcelProperty(value = "报备订单ID")
    private Long exanteOrderId;

    /**
     * 销售单审核状态： 1：待审核  2：已审核
     */
    @ExcelProperty(value = "销售单审核状态： 1：待审核  2：已审核")
    private Long auditStatus;

    /**
     * 预占单审核状态：0 :未确认  1：已确认待审核  2：已审核 3:已取消
     */
    @ExcelProperty(value = "预占单审核状态：0 :未确认  1：已确认待审核  2：已审核 3:已取消")
    private Long preAuditStatus;

    /**
     * 订单阶段(走索引 方便查询) ：0：预账单阶段  1：销售单阶段  
     */
    @ExcelProperty(value = "订单阶段(走索引 方便查询) ：0：预账单阶段  1：销售单阶段  ")
    private Long orderStep;

    /**
     * 规则引擎返回信息
     */
    @ExcelProperty(value = "规则引擎返回信息")
    private String ruleExtra;

    /**
     * 预订单审核时间
     */
    @ExcelProperty(value = "预订单审核时间")
    private Date preAuditAt;

    /**
     * 销售单审核时间
     */
    @ExcelProperty(value = "销售单审核时间")
    private Date auditAt;

    /**
     * 预占单确认时间(系统确认订单时间)
     */
    @ExcelProperty(value = "预占单确认时间(系统确认订单时间)")
    private Date preConfirmAt;

    /**
     * 妥投状态:  UN_DELIVERED   DELIVERED，PART_DELIVERED
     */
    @ExcelProperty(value = "妥投状态:  UN_DELIVERED   DELIVERED，PART_DELIVERED")
    private String deliveredStatus;

    /**
     * 妥投时间
     */
    @ExcelProperty(value = "妥投时间")
    private Date deliveredTime;

    /**
     * 虚拟妥投状态 ：UN_DELIVERED   DELIVERED，PART_DELIVERED
     */
    @ExcelProperty(value = "虚拟妥投状态 ：UN_DELIVERED   DELIVERED，PART_DELIVERED")
    private String virtualDeliveredStatus;

    /**
     * 虚拟妥投时间
     */
    @ExcelProperty(value = "虚拟妥投时间")
    private Date virtualDeliveredTime;

    /**
     * 更新人名
     */
    @ExcelProperty(value = "更新人名")
    private String updateName;

    /**
     * 创建人名
     */
    @ExcelProperty(value = "创建人名")
    private String createName;

    /**
     * 订单同步中台标记  0 ：未同步  1：已同步
     */
    private Integer omsSyncStatus;


    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;


}
