package com.oms.domain.vo.extend.settlement;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 结算客户发票行视图对象 stlmt_csr_invoice_line
 *
 * <AUTHOR>
 * @date 2025-02-20
 */
@Data
@ExcelIgnoreUnannotated
public class StlmtCsrInvoiceGroupLineVo implements Serializable {

    private static final long serialVersionUID = 1L;


    private Long id;

    /**
     * 销售单id
     */
    private Long sellerOrderId;


    /**
     * 外部单号
     */
    private String outId;

    /**
     * 包裹单id
     */
    private Long packageOrderId;


    /**
     * 客户code
     */
    private String csrCode;


    /**
     * 客户名称
     */
    private String csrName;


    /**
     * 项目id
     */
    private Long prjId;


    /**
     * 项目名称
     */
    private String prjName;


    /**
     * 开票金额
     */
    private BigDecimal invoiceAmount;


    /**
     * 开票数量
     */
    private BigDecimal invoiceQuantity;


    /**
     * 销售日期
     */
    private Date sellTime;


    /**
     * 出货日期
     */
    private Date packageDate;

    /**
     * 数电发票类型：1：数电专票(纸质) 2：数电专票(电子)   3:数电普票(纸质)  4：数电普票(电子)
     */
    private String elecType;


    /**
     * 创建来源 1.客户发起 2.中台创建 3.服务商代客开票
     */
    private String createType;

}
