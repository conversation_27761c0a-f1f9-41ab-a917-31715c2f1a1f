package com.oms.domain.vo.extend.vendor;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.oms.common.core.domain.entity.SysRole;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 供应商用户 视图对象 sys_vendor_user
 *
 * <AUTHOR>
 * @date 2024-09-05
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ExcelIgnoreUnannotated
public class SysVendorUserBindingVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 供应商名称
     */
    @ExcelProperty(value = "供应商名称")
    private String name;

    /**
     * 供应商简称
     */
    @ExcelProperty(value = "供应商简称")
    private String shortName;

    /**
     * 类型 1-供应商 2-服务商
     */
    private Integer type ;

    /**
     * 用户账号
     */
    @ExcelProperty(value = "用户账号")
    private String userName;

    /**
     * 手机号
     */
    @ExcelProperty(value = "手机号")
    private String mobile;

    /**
     * 权限列表
     */
    private List<SysRole> roles;

    /**
     * 权限名称
     */
    private String roleNames ;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long vendorId ;

    private String sysApp ;


}
