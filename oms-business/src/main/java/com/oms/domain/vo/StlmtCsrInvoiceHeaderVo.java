package com.oms.domain.vo;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.oms.common.annotation.ExcelDictFormat;
import com.oms.common.convert.ExcelDictConvert;
import lombok.Data;

import java.io.Serializable;

/**
 * 结算客户发票头视图对象 stlmt_csr_invoice_header
 *
 * <AUTHOR>
 * @date 2025-02-19
 */
@Data
@ExcelIgnoreUnannotated
public class StlmtCsrInvoiceHeaderVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 租户id
     */
    @ExcelProperty(value = "租户id")
    private Long tenantId;

    /**
     * 项目id
     */
    @ExcelProperty(value = "项目id")
    private Long prjId;

    /**
     * 项目名称
     */
    @ExcelProperty(value = "项目名称")
    private String prjName;

    /**
     * 单据来源 1.订单 2.账单
     */
    @ExcelProperty(value = "单据来源 1.订单 2.账单")
    private String sourceType;

    /**
     * 创建来源 1.客户发起 2.中台创建 3.服务商代客开票
     */
    @ExcelProperty(value = "创建来源 1.客户发起 2.中台创建 3.服务商代客开票")
    private String createType;

    /**
     * 发票编号（外部发票编号）
     */
    @ExcelProperty(value = "发票编号", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "外部发票编号")
    private String invoiceCode;

//    /**
//     * 发票类型;1.专票，2.普票
//     */
//    @ExcelProperty(value = "发票类型;1.专票，2.普票")
//    private String invoiceType;


    /**
     * 数电发票类型：1：数电专票(纸质) 2：数电专票(电子)   3:数电普票(纸质)  4：数电普票(电子)
     */
    private String elecType;

    /**
     * 销方Id
     */
    private Long sellerId;

    /**
     * 销方名称
     */
    @ExcelProperty(value = "销方名称")
    private String sellerName;

    /**
     * 销方税号
     */
    @ExcelProperty(value = "销方税号")
    private String sellerTaxNum;

    /**
     * 销方银行
     */
    @ExcelProperty(value = "销方银行")
    private String sellerBank;

    /**
     * 销方银行账号
     */
    @ExcelProperty(value = "销方银行账号")
    private String sellerBankAccount;

    /**
     * 销方地址
     */
    @ExcelProperty(value = "销方地址")
    private String sellerAddress;

    /**
     * 销方电话
     */
    @ExcelProperty(value = "销方电话")
    private String sellerTel;

    /**
     * 客户编码
     */
    @ExcelProperty(value = "客户编码")
    private String csrCode;

    /**
     * 客户名称
     */
    @ExcelProperty(value = "客户名称")
    private String csrName;

    /**
     * 客户税号
     */
    @ExcelProperty(value = "客户税号")
    private String csrTaxNum;

    /**
     * 客户银行
     */
    @ExcelProperty(value = "客户银行")
    private String csrBank;

    /**
     * 客户银行账号
     */
    @ExcelProperty(value = "客户银行账号")
    private String csrBankAccount;

    /**
     * 客户地址
     */
    @ExcelProperty(value = "客户地址")
    private String csrAddress;

    /**
     * 客户电话
     */
    @ExcelProperty(value = "客户电话")
    private String csrTel;

    /**
     * 是否电子发票;Y.是，N.否
     */
    @ExcelProperty(value = "是否电子发票;Y.是，N.否")
    private String isEinv;

    /**
     * 客户电子邮箱
     */
    @ExcelProperty(value = "客户电子邮箱")
    private String csrEmail;

    /**
     * 客户抄送邮箱
     */
    @ExcelProperty(value = "客户抄送电子邮箱")
    private String csrCcEmail;

    /**
     * 发票不含税总金额（裸价）
     */
    @ExcelProperty(value = "发票不含税总金额", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "发票不含税总金额")
    private BigDecimal untaxAmount;

    /**
     * 发票含税总金额
     */
    @ExcelProperty(value = "发票含税总金额")
    private BigDecimal amount;

    /**
     * 税额
     */
    @ExcelProperty(value = "税额")
    private BigDecimal taxAmount;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remarks;

    /**
     * 审核备注
     */
    @ExcelProperty(value = "审核备注")
    private String auditRemark;

    /**
     * 开票人
     */
    @ExcelProperty(value = "开票人")
    private String makeUser;


    /**
     * 审核状态 1.待提交  2.待审核 3.审核通过 4.审核驳回
     */
    @ExcelProperty(value = "审核状态 1.待提交  2.待审核 3.审核通过 4.审核驳回")
    private String auditStatus;

    /**
     * 开票状态;1.待开票，2.已开票，3.拒绝开票
     */
    @ExcelProperty(value = "开票状态;1.待开票，2.已开票，3.拒绝开票")
    private Long makeStatus;


    /**
     * 收款状态;1.待收款，2.已收款
     */
    @ExcelProperty(value = "收款状态;1.待收款，2.已收款")
    private Long payeeStatus;



    /**
     * 发票推送状态;1.未推送，2.已推送
     */
    @ExcelProperty(value = "发票推送状态;1.未推送，2.已推送")
    private String pushStatus;

    /**
     * 开票备注
     */
    @ExcelProperty(value = "开票备注")
    private String makeRemark;

    /**
     * 申请开票时间
     */
    @ExcelProperty(value = "申请开票时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date makeCreateTime;

    /**
     * 收款人
     */
    @ExcelProperty(value = "收款人")
    private String payeeUser;

    /**
     * 收款备注
     */
    @ExcelProperty(value = "收款备注")
    private String payeeRemark;

    /**
     * 更新人名
     */
    @ExcelProperty(value = "更新人名")
    private String updateName;

    /**
     * 创建人名
     */
    @ExcelProperty(value = "创建人名")
    private String createName;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createTime;


    /**
     * 客户开票完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date invoiceFinishTime;

    /**
     * 创建人
     */
    private Long createBy;

    /**
     * 创建人
     */
    private Long submitBy;

    /**
     * 提交人名
     */
    private String submitName;


    /**
     * 驳回时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date rejectTime;


    /**
     * 审核人
     */
    private Long auditBy;


    /**
     * 审核人名
     */
    private String auditName;


    /**
     * 审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date auditTime;
}
