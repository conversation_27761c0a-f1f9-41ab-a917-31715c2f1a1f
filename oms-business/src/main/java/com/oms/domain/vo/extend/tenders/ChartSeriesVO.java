package com.oms.domain.vo.extend.tenders;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;


/**
 * 图标系列实体类
* @Title: ChartSeriesVO
* @Description:
* @author: rqb
* @date 
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(value = ChartSeriesVO.class)
public class ChartSeriesVO extends PropertyNamingStrategy implements Serializable {

    /**
	 * 
	 */
	private static final long serialVersionUID = 6014387818403215953L;

    private String name;

    /**
     * 线/柱 数据
     */
    private List<Integer> data;

    /**
     * 每个数据 名字
     */
    private List<String> localName;

}
