package com.oms.domain.vo.extend.settlement;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.oms.domain.vo.StlmtSellerOrderDtlVo;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 结算销售单据明细视图对象 stlmt_seller_order_dtl
 *
 * <AUTHOR>
 * @date 2025-01-23
 */
@Data
@ExcelIgnoreUnannotated
public class StlmtSellerOrderDtlExtendVo extends StlmtSellerOrderDtlVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 发货单号ID
     */
    private Long packageOrderId;

    /**
     * 销售单据ID
     */
    private Long sellerOrderId;

    /**
     * 外部单号
     */
    private String outId;

    /**
     * 送货客户ID
     */
    private Long deliverCustomerId;

    /**
     * 送货客户名称
     */
    private String deliverCustomerName;
    /**
     * 开票客户ID
     */
    private Long invoiceCustomerId;

    /**
     * 开票客户名称
     */
    private String invoiceCustomerName;

    /**
     * 销售时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date sellerTimes;

    /**
     * 发货时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date packageDate;

    /**
     * 单据类型;1:销售出库  2:销退退回 3.销售调价
     */
    private String documentType;

    /**
     * 订单类型;0服务商,1自营
     */
    private String orderType;
}
