package com.oms.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.oms.common.annotation.ExcelDictFormat;
import com.oms.common.convert.ExcelDictConvert;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * api消息视图对象 api_message
 *
 * <AUTHOR>
 * @date 2025-05-07
 */
@Data
@ExcelIgnoreUnannotated
public class ApiMessageVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 租户id
     */
    @ExcelProperty(value = "租户id")
    private Long tenantId;

    /**
     * 项目ID
     */
    @ExcelProperty(value = "项目ID")
    private Long prjId;

    /**
     * 项目名称
     */
    @ExcelProperty(value = "项目名称")
    private String prjName;

    /**
     * 中台消息id
     */
    @ExcelProperty(value = "中台消息id")
    private String omsMessageId;

    /**
     * 消息类型 1：商品消息 2：订单消息
     */
    @ExcelProperty(value = "消息类型 1：商品消息 2：订单消息")
    private Long messageType;

    /**
     * 操作类型
     */
    @ExcelProperty(value = "操作类型")
    private String operationType;

    /**
     * 中台处理状态 0:未处理；1:处理成功 2.处理失败 3.忽略
     */
    @ExcelProperty(value = "中台处理状态 0:未处理；1:处理成功 2.处理失败 3.忽略")
    private Long status;

    /**
     * 客户处理状态 0:未处理；1:处理成功 2.处理失败 3.忽略
     */
    @ExcelProperty(value = "客户处理状态 0:未处理；1:处理成功 2.处理失败 3.忽略")
    private Long clientStatus;

    /**
     * 消息内容
     */
    @ExcelProperty(value = "消息内容")
    private String content;

    /**
     * 失败原因
     */
    @ExcelProperty(value = "失败原因")
    private String reson;

    /**
     * 更新人名
     */
    @ExcelProperty(value = "更新人名")
    private String updateName;

    /**
     * 创建人名
     */
    @ExcelProperty(value = "创建人名")
    private String createName;


    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}
