package com.oms.domain.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.oms.common.core.domain.BaseEntityVo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 后台类目视图对象 goods_back_category
 *
 * <AUTHOR>
 * @date 2024-08-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GoodsBackCategoryVo extends BaseEntityVo {

	private static final long serialVersionUID = 1L;
    /**
     * 父id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long pid;

    /**
     * 类目名称
     */
    private String cateName;

    /**
     * 是否有子类
     */
    private String hasChildren;

    /**
     * 类目等级
     */
    private Long cateLevel;

    /**
     * 状态 (正常-NOR，关闭-DIS)
     */
    private String status;

    /**
     * 类目路径集合  按照类目等级排序
     */
    private List<Long> path;

    /**
     * 是否存在售后服务政策/是否绑定税收分类  Y是 N否
     */
    private String isHav;
}
