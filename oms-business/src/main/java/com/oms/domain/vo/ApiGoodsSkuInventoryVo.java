package com.oms.domain.vo;

import lombok.Data;

import java.util.Date;

/**
 * api商品sku库存信息vo
 *
 * <AUTHOR>
 */
@Data
public class ApiGoodsSkuInventoryVo {

	private static final long serialVersionUID=1L;


	/**
	 * 项目id
	 */
	private Long prjId;
	/**
	 * skuid
	 */
	private Long skuId;
	/**
	 * 区域id 全国为1 默认库存为-1
	 */
	private Long areaId;
	/**
	 * 库存数量
	 */
	private Long inventory;
	/**
	 * 有效期开始
	 */
	private Date validityStart;
	/**
	 * 有效期截止
	 */
	private Date validityEnd;
	/**
	 * 是否长期 Y是 N否
	 */
	private String isLong;
	/**
	 * 状态 (正常-NOR，关闭-DIS)
	 */
	private String status;
}
