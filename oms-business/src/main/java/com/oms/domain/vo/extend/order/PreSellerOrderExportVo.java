package com.oms.domain.vo.extend.order;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.oms.common.annotation.ExcelEnumFormat;
import com.oms.common.convert.ExcelEnumConvert;
import com.oms.common.enums.InvoiceElecTypeEnum;
import com.oms.common.enums.OrderAuditStatusEnum;
import com.oms.common.enums.OrderTypeEnum;
import com.oms.domain.OrderOperationInfo;
import com.oms.domain.vo.PreSellerOrderLineVo;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 预销售订单视图对象 seller_order
 *
 * <AUTHOR>
 * @date 2024-08-28
 */
@Data
@ExcelIgnoreUnannotated
public class PreSellerOrderExportVo implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long orderId;
    /**
     * 外部订单id
     */
    @ExcelProperty(value = "客户预占订单号")
    private String outId;


    /**
     * 客户订单下单日期
     */
    @ExcelProperty(value = "客户下单时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date outDate;

    /**
     * 客户订单确认时间
     *
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "客户确认下单时间")
    private Date outConfirmDate;

    /**
     * 客户采购单号
     */
    @ExcelProperty(value = "客户采购单号")
    private String applyCode;


    /**
     * OMS订单号
     */
    @ExcelProperty(value = "OMS订单号")
    private String sourceId;



    /**
     * 订单编号
     */
    @ExcelProperty(value = "订单编号")
    private Long id;


    /**
     * 区域运营商名称
     */
    @ExcelProperty(value = "项目名称")
    private String prjName;



    /**
     * 买家姓名
     */
    @ExcelProperty(value = "客户名称")
    private String buyerName;


    /**
     * 发票抬头
     */
    @ExcelProperty(value = "发票抬头")
    private String invoiceTitle;


    /**
     * 采购单位名称
     */
    @ExcelProperty(value = "采购单位名称")
    private String purchaseCompany;


    /**
     * sku id
     */
    @ExcelProperty(value = "skuId")
    private String skuId;

    /**
     * sku 名称
     */
    @ExcelProperty(value = "sku名称")
    private String skuName;

    /**
     * 实际支付金额(单价)
     */
    @ExcelProperty(value = "销售单价")
    private BigDecimal unitPaidAmount;


    /**
     * 数量
     */
    @ExcelProperty(value = "数量")
    private Long quantity;

    /**
     * 商品行总价
     */
    @ExcelProperty(value = "商品行总价")
    private BigDecimal paidAmount;

    /**
     * 订单总额
     */
    @ExcelProperty(value = "订单总额")
    private BigDecimal totalPaidAmount;

    /**
     * 税率
     */
    @ExcelProperty(value = "税率")
    private String taxRate;


    /**
     * 预占单审核状态：0 :未确认  1：已确认待审核  2：已审核  3: 已取消
     */
    @ExcelEnumFormat(enumClass = OrderAuditStatusEnum.class, textField = "info")
    @ExcelProperty(value = "订单状态", converter = ExcelEnumConvert.class)
    private Long preAuditStatus;


    /**
     * 取消原因
     */
    @ExcelProperty(value = "取消原因")
    private String cancelReason;


    /**
     * 订单类型：0：接口订单  1：手工订单   2：商城订单  3：手工导入订单
     */
    @ExcelProperty(value = "订单类型", converter = ExcelEnumConvert.class)
    @ExcelEnumFormat(enumClass = OrderTypeEnum.class, textField = "info")
    private Long orderType;


    /**
     * 下单人姓名
     */
    @ExcelProperty(value = "下单人姓名")
    private String placeOrderName;

    /**
     * 下单人电话
     */
    @ExcelProperty(value = "下单人电话")
    private String placeOrderMobile;



    /**
     * 买家留言
     */
    @ExcelProperty(value = "买家备注")
    private String buyerNotes;

    /**
     * 发货描述
     */
    @ExcelProperty(value = "卖家备注")
    private String shopNotes;


    /**
     * 收货人名称
     */
    @ExcelProperty(value = "收货人名称")
    private String receiveUserName;


    /**
     * 收货人电话
     */
    @ExcelProperty(value = "收货人电话")
    private String receiveMobile;



    /**
     * 省名称
     */
    @ExcelProperty(value = "收货省")
    private String provinceName;


    /**
     * 市名称
     */
    @ExcelProperty(value = "收货市")
    private String cityName;


    /**
     * 区名称
     */
    @ExcelProperty(value = "收货区")
    private String regionName;


//    /**
//     * 街道名称
//     */
//    @ExcelProperty(value = "收货街道")
//    private String streetName;

    /**
     * 区名称
     */
    @ExcelProperty(value = "收货详细地址")
    private String receiveAddressDetail;

    /**
     * 数电发票类型：1：数电专票(纸质) 2：数电专票(电子)   3:数电普票(纸质)  4：数电普票(电子)
     */
    @ExcelProperty(value = "发票类型",converter = ExcelEnumConvert.class)
    @ExcelEnumFormat(enumClass = InvoiceElecTypeEnum.class, textField = "info")
    private Integer invoiceType;

    /**
     * 纳税人识别号
     */
    @ExcelProperty(value = "纳税人识别号")
    private String itins;

    /**
     * 开户银行，增值税专票时必填
     */
    @ExcelProperty(value = "开票银行")
    private String bank;

    /**
     * 银行账户
     */
    @ExcelProperty(value = "银行账号")
    private String bankAccount;














}
