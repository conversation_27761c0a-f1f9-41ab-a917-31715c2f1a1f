package com.oms.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 投标单明细视图对象 tenders_dtl
 *
 * <AUTHOR>
 * @date 2024-11-01
 */
@Data
@ExcelIgnoreUnannotated
public class TendersDtlVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ExcelProperty(value = "id")
    private Long id;

    /**
     * 租户ID
     */
    @ExcelProperty(value = "租户ID")
    private Long tenantId;

    /**
     * 投标单表id
     */
    @ExcelProperty(value = "投标单表id")
    private Long tendersId;

    /**
     * 投标单位
     */
    @ExcelProperty(value = "投标单位")
    private String tendersCompany;

    /**
     * 商品名称
     */
    @ExcelProperty(value = "商品名称")
    private String itemName;

    /**
     * 商品编码
     */
    @ExcelProperty(value = "商品编码")
    private String itemCode;

    /**
     * 分类名称
     */
    @ExcelProperty(value = "分类名称")
    private String categoryName;

    /**
     * 品牌名称
     */
    @ExcelProperty(value = "品牌名称")
    private String brandName;

    /**
     * 型号
     */
    @ExcelProperty(value = "型号")
    private String model;

    /**
     * 规格
     */
    @ExcelProperty(value = "规格")
    private String specs;

    /**
     * 单位
     */
    @ExcelProperty(value = "单位")
    private String unit;

    /**
     * 产地
     */
    @ExcelProperty(value = "产地")
    private String producingArea;

    /**
     * 竞标数量
     */
    @ExcelProperty(value = "竞标数量")
    private Long competitiveTenderQuantity;

    /**
     * 报价(元)
     */
    @ExcelProperty(value = "报价(元)")
    private BigDecimal quotedPrice;

    /**
     * 扩展字段
     */
    @ExcelProperty(value = "扩展字段")
    private String extendFieldJson;

    /**
     * 相关要求
     */
    @ExcelProperty(value = "相关要求")
    private String relatedRequirement;

    /**
     * 其他说明
     */
    @ExcelProperty(value = "其他说明")
    private String remark;

    /**
     * 创建人名
     */
    @ExcelProperty(value = "创建人名")
    private String createName;

    /**
     * 更新人名
     */
    @ExcelProperty(value = "更新人名")
    private String updateName;

    /**
     * 逻辑删除 (1-已删除，0-未删除)
     */
    @ExcelProperty(value = "逻辑删除 (1-已删除，0-未删除)")
    private Long deleted;

    /**
     * 删除时间
     */
    @ExcelProperty(value = "删除时间")
    private Date deleteTime;

    /**
     * 删除人
     */
    @ExcelProperty(value = "删除人")
    private Long deleteBy;

    /**
     * 删除人名
     */
    @ExcelProperty(value = "删除人名")
    private String deleteName;


}
