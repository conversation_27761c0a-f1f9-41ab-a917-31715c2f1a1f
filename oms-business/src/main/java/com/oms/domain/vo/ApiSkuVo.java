package com.oms.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.oms.common.core.domain.BaseEntityVo;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * api sku视图对象 api_sku
 *
 * <AUTHOR>
 * @date 2025-05-07
 */
@Data
@ExcelIgnoreUnannotated
public class ApiSkuVo extends BaseEntityVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 项目id
     */
    @ExcelProperty(value = "项目id")
    private Long prjId;

    /**
     * 项目名称
     */
    @ExcelProperty(value = "项目名称")
    private String prjName;

    /**
     * api商品id
     */
    @ExcelProperty(value = "api商品id")
    private Long apiItemId;

    /**
     * skuid
     */
    @ExcelProperty(value = "skuid")
    private Long skuId;


    /**
     * 外部skuid
     */
    @ExcelProperty(value = "外部skuid")
    private String outSkuId;

    /**
     * sku编码
     */
    @ExcelProperty(value = "sku编码")
    private String skuCode;

    /**
     * sku条码
     */
    @ExcelProperty(value = "sku条码")
    private String barcode;

    /**
     * 规格
     */
    @ExcelProperty(value = "规格")
    private String specification;

    /**
     * 型号
     */
    @ExcelProperty(value = "型号")
    private String communityModel;

    /**
     * 官网价
     */
    @ExcelProperty(value = "官网价")
    private BigDecimal officialPrice;

    /**
     * 销售价
     */
    @ExcelProperty(value = "销售价")
    private BigDecimal salePrice;

    /**
     * 市场价
     */
    @ExcelProperty(value = "市场价")
    private BigDecimal marketPrice;

    /**
     * 组合品明细{skuid:数量}
     */
    @ExcelProperty(value = "组合品明细{skuid:数量}")
    private String skuJson;

    /**
     * 起售数量
     */
    @ExcelProperty(value = "起售数量")
    private Long minQuantity;

    /**
     * 起售倍数
     */
    @ExcelProperty(value = "起售倍数")
    private Long salesMultiple;
}
