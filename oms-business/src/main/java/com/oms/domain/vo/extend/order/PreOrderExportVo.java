package com.oms.domain.vo.extend.order;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.oms.common.annotation.ExcelEnumFormat;
import com.oms.common.convert.ExcelEnumConvert;
import com.oms.common.enums.*;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 预销售订单视图对象 seller_order
 *
 * <AUTHOR>
 * @date 2024-08-28
 */
@Data
@ExcelIgnoreUnannotated
public class PreOrderExportVo implements Serializable {

    private static final long serialVersionUID = 1L;


    private Long orderId;
    /**
     * 外部订单id
     */
    @ExcelProperty(value = "客户预占订单号")
    private String outId;

    /**
     * 订单编号
     */
    @ExcelProperty(value = "销售单号")
    private Long sellerOrderId;

    /**
     * 采购申请单行编号
     */
    @ExcelProperty(value = "采购申请单号")
    private Long preOrderId;

    /**
     * 采购申请单创建时间
     */
    @ExcelProperty(value = "采购申请单创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;


    /**
     * 采购申请单审核时间
     */
    @ExcelProperty(value = "采购申请单审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date confirmAt;


    /**
     * 客户订单下单日期
     */
    @ExcelProperty(value = "客户下单时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date outDate;

    /**
     * 客户订单确认时间
     *
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "客户确认下单时间")
    private Date outConfirmDate;


    /**
     * 客户采购单号
     */
    @ExcelProperty(value = "客户采购单号")
    private String applyCode;

    /**
     * OMS订单号
     */
    @ExcelProperty(value = "OMS订单号")
    private String sourceId;



    /**
     * 区域运营商名称
     */
    @ExcelProperty(value = "项目名称")
    private String prjName;



    /**
     * 买家姓名
     */
    @ExcelProperty(value = "客户名称")
    private String buyerName;


    /**
     * 发票抬头
     */
    @ExcelProperty(value = "发票抬头")
    private String invoiceTitle;


    /**
     * 采购单位名称
     */
    @ExcelProperty(value = "采购单位名称")
    private String purchaseCompany;


    /**
     * sku id
     */
    @ExcelProperty(value = "skuId")
    private String skuId;

    /**
     * sku 名称
     */
    @ExcelProperty(value = "sku名称")
    private String skuName;


    /**
     * 实际支付金额(单价)
     */
    @ExcelProperty(value = "销售单价")
    private BigDecimal unitPaidAmount;


    /**
     * 数量
     */
    @ExcelProperty(value = "数量")
    private Long quantity;

    /**
     * 供应商名称
     */
    @ExcelProperty(value = "供应商名称")
    private String shopName;


    /**
     * 总成单价
     */
    @ExcelProperty(value = "成本单价")
    private BigDecimal unitBasePrice;

    /**
     * 费率
     */
    @ExcelProperty(value = "费率")
    private BigDecimal feeRate;

    /**
     * 税率
     */
    @ExcelProperty(value = "税率")
    private String taxRate;

    /**
     * 实际支付金额
     */
    @ExcelProperty(value = "支付金额")
    private BigDecimal paidAmount;

    /**
     * 总成本价
     */
    @ExcelProperty(value = "总成本价")
    private BigDecimal basePrice;

    /**
     * 审核状态：0待确认 1 待审核  2已审核
     */
    @ExcelProperty(value = "订单状态",converter = ExcelEnumConvert.class)
    @ExcelEnumFormat(enumClass = OrderAuditStatusEnum.class, textField = "info")
    private Long auditStatus;

    /**
     * 下单人姓名
     */
    @ExcelProperty(value = "下单人姓名")
    private String placeOrderName;

    /**
     * 下单人电话
     */
    @ExcelProperty(value = "下单人电话")
    private String placeOrderMobile;



    /**
     * 买家留言
     */
    @ExcelProperty(value = "买家备注")
    private String buyerNotes;

    /**
     * 发货描述
     */
    @ExcelProperty(value = "卖家备注")
    private String shopNotes;



    /**
     * 报备单ID
     */
    @ExcelProperty(value = "报备单ID")
    private Long exanteOrderId;

    /**
     * 收货人名称
     */
    @ExcelProperty(value = "收货人名称")
    private String receiveUserName;


    /**
     * 收货人电话
     */
    @ExcelProperty(value = "收货人电话")
    private String receiveMobile;



    /**
     * 省名称
     */
    @ExcelProperty(value = "收货省")
    private String provinceName;


    /**
     * 市名称
     */
    @ExcelProperty(value = "收货市")
    private String cityName;


    /**
     * 区名称
     */
    @ExcelProperty(value = "收货区")
    private String regionName;


//    /**
//     * 街道名称
//     */
//    @ExcelProperty(value = "收货街道")
//    private String streetName;

    /**
     * 区名称
     */
    @ExcelProperty(value = "收货详细地址")
    private String receiveAddressDetail;
}
