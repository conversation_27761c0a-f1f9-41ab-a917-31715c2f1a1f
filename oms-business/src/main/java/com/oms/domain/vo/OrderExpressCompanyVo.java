package com.oms.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.oms.common.annotation.ExcelDictFormat;
import com.oms.common.convert.ExcelDictConvert;
import lombok.Data;

import java.io.Serializable;

/**
 * 物流信息公司视图对象 order_express_company
 *
 * <AUTHOR>
 * @date 2024-11-02
 */
@Data
@ExcelIgnoreUnannotated
public class OrderExpressCompanyVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 租户id
     */
    @ExcelProperty(value = "租户id")
    private Long tenantId;

    /**
     * 公司名称
     */
    @ExcelProperty(value = "公司名称")
    private String companyName;

    /**
     * 公司编码
     */
    @ExcelProperty(value = "公司编码")
    private String companyCode;

    /**
     * 公司描述
     */
    @ExcelProperty(value = "公司描述")
    private String description;

    /**
     * 快递公司类型
     */
    @ExcelProperty(value = "快递公司类型")
    private String type;

    /**
     * 国家代码
     */
    @ExcelProperty(value = "国家代码")
    private String countryCode;

    /**
     * 是否常用物流 0否 1是
     */
    @ExcelProperty(value = "是否常用物流 0否 1是")
    private Integer isFrequencyUsed;

    /**
     * 是否使用正则校验单号 0-否 1-是
     */
    @ExcelProperty(value = "是否使用正则校验单号 0-否 1-是")
    private Integer isRegexValidate;

    /**
     * 正则校验表达式内容
     */
    @ExcelProperty(value = "正则校验表达式内容")
    private String regexRule;

    /**
     * 更新人名
     */
    @ExcelProperty(value = "更新人名")
    private String updateName;

    /**
     * 创建人名
     */
    @ExcelProperty(value = "创建人名")
    private String createName;

    /**
     * 逻辑删除 (1-已删除，0-未删除)
     */
    @ExcelProperty(value = "逻辑删除 (1-已删除，0-未删除)")
    private Long deleted;

    /**
     * 删除时间
     */
    @ExcelProperty(value = "删除时间")
    private Date deleteTime;

    /**
     * 删除人主键
     */
    @ExcelProperty(value = "删除人主键")
    private Long deleteBy;

    /**
     * 删除人名
     */
    @ExcelProperty(value = "删除人名")
    private String deleteName;


}
