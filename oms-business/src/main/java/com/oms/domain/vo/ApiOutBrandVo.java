package com.oms.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.oms.common.core.domain.BaseEntityVo;
import lombok.Data;

import java.io.Serializable;

/**
 * api外部品牌视图对象 api_out_brand
 *
 * <AUTHOR>
 * @date 2025-05-07
 */
@Data
@ExcelIgnoreUnannotated
public class ApiOutBrandVo extends BaseEntityVo implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 项目id
     */
    @ExcelProperty(value = "项目id")
    private Long prjId;

    /**
     * 项目名称
     */
    @ExcelProperty(value = "项目名称")
    private String prjName;

    /**
     * 外部品牌id
     */
    @ExcelProperty(value = "外部品牌id")
    private String outId;

    /**
     * 外部品牌名称
     */
    @ExcelProperty(value = "外部品牌名称")
    private String outName;

    /**
     * 品牌logo
     */
    @ExcelProperty(value = "品牌logo")
    private String logo;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 状态 (正常-NOR，关闭-DIS)
     */
    @ExcelProperty(value = "状态 (正常-NOR，关闭-DIS)")
    private String status;
}
