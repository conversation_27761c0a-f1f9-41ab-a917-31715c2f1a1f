package com.oms.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 预销售订单视图对象 seller_order
 *
 * <AUTHOR>
 * @date 2024-08-28
 */
@Data
@ExcelIgnoreUnannotated
public class OutSellerOrderVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;


    /**
     * 外部订单id
     */
    @ExcelProperty(value = "外部订单id")
    private String outId;

    /**
     * 购物单id
     */
    @ExcelProperty(value = "购物单id")
    private String purchaseOrderId;



    /**
     * 买家id
     */
    @ExcelProperty(value = "买家id")
    private Long buyerId;

    /**
     * 买家姓名
     */
    @ExcelProperty(value = "买家姓名")
    private String buyerName;

    /**
     * 卖家姓名
     */
    @ExcelProperty(value = "卖家姓名")
    private String shopName;

    /**
     * 卖家id
     */
    @ExcelProperty(value = "卖家id")
    private Long shopId;

    /**
     * 项目id
     */
    @ExcelProperty(value = "项目id")
    private Long prjId;

    /**
     * 区域运营商名称
     */
    @ExcelProperty(value = "区域运营商名称")
    private String prjName;

    /**
     * 外部订单日期
     */
    @ExcelProperty(value = "外部订单日期")
    private String outDate;


    @ExcelProperty(value = "订单行列表")
    List<OutSellerOrderLineVo> orderLineVoList ;



}
