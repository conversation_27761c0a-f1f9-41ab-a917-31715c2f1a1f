package com.oms.domain.vo;

import cn.hutool.json.JSONArray;
import lombok.Data;

import java.math.BigDecimal;

/**
 * api大客户商品图片信息vo
 *
 * <AUTHOR>
 */
@Data
public class ApiDkhItemPicVo {

	private static final long serialVersionUID=1L;

	/**
	 * 图片地址
	 */
	private String picUrl;
	/**
	 * 排序
	 */
	private Integer picSort;

	public ApiDkhItemPicVo(){

	}

	public ApiDkhItemPicVo(String picUrl,Integer picSort){
		this.picUrl = picUrl;
		this.picSort = picSort;
	}

}
