package com.oms.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.oms.common.annotation.ExcelDictFormat;
import com.oms.common.convert.ExcelDictConvert;
import com.oms.common.core.domain.BaseEntityVo;
import lombok.Data;

import java.io.Serializable;

/**
 * 品牌申请视图对象 goods_brand_apply
 *
 * <AUTHOR>
 * @date 2024-10-06
 */
@Data
@ExcelIgnoreUnannotated
public class GoodsBrandApplyVo extends BaseEntityVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 品牌名称
     */
    @ExcelProperty(value = "品牌名称")
    private String brandName;

    /**
     * 品牌logo
     */
    @ExcelProperty(value = "品牌logo")
    private String logo;

    /**
     * 品牌英文名
     */
    @ExcelProperty(value = "品牌英文名")
    private String enName;

    /**
     * 供应商id
     */
    @ExcelProperty(value = "供应商id")
    private Long vendorId;

    /**
     * 供应商名称
     */
    @ExcelProperty(value = "供应商名称")
    private String vendorName;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 状态 0待提交 1审核中 2审核通过 3驳回
     */
    @ExcelProperty(value = "状态 0待提交 1审核中 2审核通过 3驳回")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long status;
}
