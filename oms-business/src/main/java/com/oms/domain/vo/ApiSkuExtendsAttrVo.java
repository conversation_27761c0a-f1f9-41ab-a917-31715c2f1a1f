package com.oms.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.oms.common.core.domain.BaseEntityVo;
import lombok.Data;

import java.io.Serializable;

/**
 * api商品属性拓展视图对象 api_sku_extends_attr
 *
 * <AUTHOR>
 * @date 2025-05-07
 */
@Data
@ExcelIgnoreUnannotated
public class ApiSkuExtendsAttrVo extends BaseEntityVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * api商品id
     */
    private Long apiItemId;
    /**
     * 中台商品id
     */
    private Long itemId;
    /**
     * 项目外部属性id
     */
    private String outAttributeId;
    /**
     * 项目属性编码
     */
    private String outAttributeCode;
    /**
     * 项目属性名称
     */
    private String outAttributeName;
    /**
     * 项目属性值
     */
    private String outAttributeValue;
    /**
     * 是否自定义属性
     */
    private String hasCustom;
    /**
     * 状态 (正常-NOR，关闭-DIS)
     */
    private String status;
}
