package com.oms.domain.vo;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.oms.common.annotation.ExcelDictFormat;
import com.oms.common.convert.ExcelDictConvert;
import lombok.Data;

import java.io.Serializable;

/**
 * 结算-销售调价明细视图对象 stlmt_seller_price_adj_dtl
 *
 * <AUTHOR>
 * @date 2025-01-13
 */
@Data
@ExcelIgnoreUnannotated
public class StlmtSellerPriceAdjDtlVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 租户
     */
    @ExcelProperty(value = "租户")
    private Long tenantId;

    /**
     * 项目ID
     */
    @ExcelProperty(value = "项目ID")
    private Long projectId;

    /**
     * 项目名称
     */
    @ExcelProperty(value = "项目名称")
    private String projectName;

    /**
     * 状态
     */
    @ExcelProperty(value = "状态")
    private String status;

    /**
     * 销售调价ID
     */
    @ExcelProperty(value = "销售调价ID")
    private Long sellerPriceAdjId;

    /**
     * 发货单号ID
     */
    @ExcelProperty(value = "发货单号ID")
    private Long packageOrderId;

    /**
     * 采购订单号ID
     */
    @ExcelProperty(value = "采购订单号ID")
    private Long purchaseOrderId;

    /**
     * 外部订单号
     */
    @ExcelProperty(value = "外部订单号")
    private String outId;

    /**
     * OMS销售订单号ID
     */
    @ExcelProperty(value = "OMS销售订单号ID")
    private Long sellerOrderId;

    /**
     * OMS销售订单行ID
     */
    @ExcelProperty(value = "OMS销售订单行ID")
    private Long sellerOrderLineId;

    /**
     * 销售单据ID
     */
    @ExcelProperty(value = "销售单据ID")
    private Long stlmtSellerOrderId;

    /**
     * 销售单据明细ID
     */
    @ExcelProperty(value = "销售单据明细ID")
    private Long stlmtSellerOrderLineId;

    /**
     * 销售时间
     */
    @ExcelProperty(value = "销售时间")
    private Date sellerTime;

    /**
     * 出货日期
     */
    @ExcelProperty(value = "出货日期")
    private Date packageDate;

    /**
     * 送货客户ID
     */
    @ExcelProperty(value = "送货客户ID")
    private Long deliverCustomerId;
    /**
     * 送货客户名称
     */
    @ExcelProperty(value = "送货客户名称")
    private String deliverCustomerName;

    /**
     * 开票客户ID
     */
    @ExcelProperty(value = "开票客户ID")
    private Long invoiceCustomerId;

    /**
     * 开票客户名称
     */
    @ExcelProperty(value = "开票客户名称")
    private String invoiceCustomerName;
    /**
     * 应收客户ID
     */
    @ExcelProperty(value = "应收客户ID")
    private Long receivableCustomerId;

    /**
     * 应收客户名称
     */
    @ExcelProperty(value = "应收客户名称")
    private String receivableCustomerName;

    /**
     * 销售类型;1:销售  2:销退
     */
    @ExcelProperty(value = "销售类型;1:销售  2:销退")
    private Long sellerType;

    /**
     * 单据类型;1:销售出库  2:销退退回 3.销售调价
     */
    @ExcelProperty(value = "单据类型;1:销售出库  2:销退退回 3.销售调价")
    private Long documentType;

    /**
     * 订单类型;0服务商,1自营
     */
    @ExcelProperty(value = "订单类型;0服务商,1自营")
    private Long orderType;

    /**
     * 商品id
     */
    @ExcelProperty(value = "商品id")
    private Long itemId;

    /**
     * 商品名称
     */
    @ExcelProperty(value = "商品名称")
    private String itemName;

    /**
     * sku id
     */
    @ExcelProperty(value = "sku id")
    private Long skuId;

    /**
     * 商品名称
     */
    @ExcelProperty(value = "商品名称")
    private String skuName;

    /**
     * 计价单位
     */
    @ExcelProperty(value = "计价单位")
    private String pricingUnit;

    /**
     * 税率
     */
    @ExcelProperty(value = "税率")
    private BigDecimal taxRate;

    /**
     * 销售数量
     */
    @ExcelProperty(value = "销售数量")
    private BigDecimal sellerQuantity;

    /**
     * 原含税箱价
     */
    @ExcelProperty(value = "原含税箱价")
    private BigDecimal priceFrom;

    /**
     * 新含税箱价
     */
    @ExcelProperty(value = "新含税箱价")
    private BigDecimal priceTo;

    /**
     * 原销售金额
     */
    @ExcelProperty(value = "原销售金额")
    private BigDecimal sellerAmountFrom;

    /**
     * 新销售金额
     */
    @ExcelProperty(value = "新销售金额")
    private BigDecimal sellerAmountTo;

    /**
     * 创建人名
     */
    @ExcelProperty(value = "创建人名")
    private String createName;

    /**
     * 更新人名
     */
    @ExcelProperty(value = "更新人名")
    private String updateName;

}
