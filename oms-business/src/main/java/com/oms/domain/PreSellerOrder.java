package com.oms.domain;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.oms.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 预销售订单对象 seller_order
 *
 * <AUTHOR>
 * @date 2024-08-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("order_pre_seller")
public class PreSellerOrder extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 外部订单id
     */
    private String outId;
    /**
     * 购物单id
     */
    private Long purchaseOrderId;
    /**
     * 买家id
     */
    private Long buyerId;
    /**
     * 买家姓名
     */
    private String buyerName;
    /**
     * 是否可见, 1: 可见; 0: 不可见
     */
    private Integer enableStatus;
    /**
     * 支付状态
     */
    private String payStatus;
    /**
     * 发货状态
     */
    private String deliveryStatus;
    /**
     * 收货状态
     */
    private String receiveStatus;
    /**
     * 售后单状态
     */
    private String reverseStatus;
    /**
     * 支付完成时间
     */
    private Date payAt;
    /**
     * 发货时间
     */
    private Date shippingAt;
    /**
     * 确认收货时间
     */
    private Date confirmAt;
    /**
     * 实际支付金额
     */
    private BigDecimal paidAmount;
    /**
     * 原价总价
     */
    private BigDecimal skuOriginTotalAmount;
    /**
     * 实际总价
     */
    private BigDecimal skuAdjustAmount;
    /**
     * 优惠总价
     */
    private BigDecimal skuDiscountTotalAmount;
    /**
     * 运费原价总价
     */
    private BigDecimal shipFeeOriginAmount;
    /**
     * 运费优惠总价
     */
    private BigDecimal shipFeeAdjustAmount;
    /**
     * 运费实际总价
     */
    private BigDecimal shipFeeDiscountTotalAmount;
    /**
     * 优惠明细
     */
    private String discountDetail;
    /**
     * 买家留言
     */
    private String buyerNotes;
    /**
     * 发货描述
     */
    private String shopNotes;
    /**
     * 打标
     */
    private Long tag;
    /**
     * 拓展字段
     */
    private String extraJson;
    /**
     * 乐观锁
     */
    @Version
    private Long version;
    /**
     * 订单完成时间
     */
    private Date accomplishAt;
    /**
     * 区域运营商与供应商合作模式
     */
    private Long cooperationMode;
    /**
     * 项目id
     */
    private Long prjId;
    /**
     * 区域运营商名称
     */
    private String prjName;
    /**
     * 外部订单日期
     */
    private Date outDate;


    /**
     * 客户确认订单日期
     */
    private Date outConfirmDate;
    /**
     * 发票抬头
     */
    private String invoiceTitle;
    /**
     * 来源单号
     */
    private String sourceId;

    /**
     * 收货确认时间
     */
    private String receiveConfirmAt;

    /**
     * 收货人名称
     */
    private String receiveUserName;
    /**
     * 收货人电话
     */
    private String receiveMobile;
    /**
     * 采购单位名称
     */
    private String purchaseCompany;
    /**
     * 省id
     */
    private Long provinceId;
    /**
     * 省名称
     */
    private String provinceName;
    /**
     * 市id
     */
    private Long cityId;
    /**
     * 区域Id
     */
    private Long regionId;
    /**
     * 区名称
     */
    private String regionName;
    /**
     * 市名称
     */
    private String cityName;
    /**
     * 街道id
     */
    private Long streetId;
    /**
     * 街道名称
     */
    private String streetName;
    /**
     * 收货详细地址
     */
    private String receiveAddressDetail;
    /**
     * 下单人姓名
     */
    private String placeOrderName;
    /**
     * 下单人电话
     */
    private String placeOrderMobile;
    /**
     * 0：接口订单  1：手工订单   2：商城订单 3：手工导入订单
     */
    private Long orderType;
    /**
     * 0.扣费模式 1.成本模式
     */
    private Long pattern;
    /**
     * 是否紧急订单标识 1时为紧急订单 默认0非紧急
     */
    private Long isEmergency;
    /**
     * 取消原因
     */
    private String cancelReason;
    /**
     * 价格类型：0含税，1不含税
     */
    private Long priceType;

    /**
     * 客户采购单号
     */
    private String applyCode;
    /**
     * 虚拟发货单号
     */
    private String virtualShipCode;
    /**
     * 收货单位
     */
    private String receiveCompany;
    /**
     * 报备订单ID
     */
    private Long exanteOrderId;
    /**
     * 销售单审核状态：0 :未确认  1：已确认待审核  2：已审核
     */
    private Long auditStatus;

    /**
     * 预占单审核状态：0 :未确认  1：已确认待审核  2：已审核   3：已取消
     */
    private Long preAuditStatus;

    /**
     * 订单阶段(走索引 方便查询) ：0：预账单阶段  1：销售单阶段  
     */
    private Long orderStep;
    /**
     * 规则引擎返回信息
     */
    private String ruleExtra;
    /**
     * 销售单审核时间
     */
    private String auditAt;
    /**
     * 预订单审核时间
     */
    private String preAuditAt;
    /**
     * 预账单确认时间
     */
    private Date preConfirmAt;
    /**
     * 妥投状态
     * TO_DELIVERED 待妥投
     * PART_DELIVERED 部分妥投
     * ALL_DELIVERED 全部妥投
     */
    private String deliveredStatus;

    /**
     * 妥投时间
     */
    private Date deliveredTime;
    /**
     * 虚拟妥投状态
     * TO_DELIVERED 待妥投
     * PART_DELIVERED 部分妥投
     * ALL_DELIVERED 全部妥投
     */
    private String virtualDeliveredStatus;
    /**
     * 虚拟妥投时间
     */
    private Date virtualDeliveredTime;

    /**
     * 发票信息json
     */
    private String invoiceExtra;

    /**
     * 详细地址
     * @return
     */
    public String getFullAddress(){
        return this.provinceName+this.getCityName()+this.getRegionName()+this.getReceiveAddressDetail();
    }

}
