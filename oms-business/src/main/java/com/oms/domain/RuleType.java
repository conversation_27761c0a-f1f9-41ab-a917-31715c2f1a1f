package com.oms.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.oms.common.core.domain.BaseEntity;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 规则类型对象 rule_type
 *
 * <AUTHOR>
 * @date 2024-11-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("rule_type")
public class RuleType extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 规则类型ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 中台编码: item商品 ,order订单
     */
    private String centerCode;
    /**
     * 规则类型编码
     */
    private String typeCode;
    /**
     * 规则头ID
     */
    private Long ruleId;
    /**
     * 规则类型名称
     */
    private String typeName;

    /**
     * 状态 1启用 -1禁用
     */
    private Integer status;

}
