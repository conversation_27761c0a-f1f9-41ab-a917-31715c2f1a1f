package com.oms.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.oms.common.core.domain.BaseEntity;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 项目客户地址对象 goods_prj_csr_address
 *
 * <AUTHOR>
 * @date 2024-12-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("goods_prj_csr_address")
public class GoodsPrjCsrAddress extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 项目id
     */
    private Long prjId;
    /**
     * 客户id
     */
    private Long csrId;
    /**
     * 省id
     */
    private Long provinceId;
    /**
     * 市id
     */
    private Long cityId;
    /**
     * 区id
     */
    private Long regionId;
    /**
     * 街道id
     */
    private Long streetId;
    /**
     * 详细地址
     */
    private String address;
    /**
     * 邮政编码
     */
    private String postalCode;
    /**
     * 地址类型 1送货地址 2发票地址
     */
    private Long addressType;
    /**
     * 固定电话
     */
    private String fixedPhone;
    /**
     * 手机号
     */
    private String phone;
    /**
     * 传真
     */
    private String fax;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 联系人名称
     */
    private String userName;
    /**
     * 备注
     */
    private String remark;
    /**
     * 状态
     */
    private String status;
}
