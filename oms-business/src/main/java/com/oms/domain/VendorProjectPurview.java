package com.oms.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.oms.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 供应商项目权限对象 vendor_project_purview
 *
 * <AUTHOR>
 * @date 2024-09-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("vendor_project_purview")
public class VendorProjectPurview extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 项目id
     */
    private Long vendorId;
    /**
     * 供应商名称 
     */
    private String vendorName;
    /**
     * 项目id
     */
    private Long projectId;
    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 状态 (正常-NOR，关闭-DIS)
     */
    private String status ;


}
