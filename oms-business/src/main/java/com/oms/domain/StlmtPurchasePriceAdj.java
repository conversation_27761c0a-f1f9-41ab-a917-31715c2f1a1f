package com.oms.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.oms.common.core.domain.BaseEntity;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 结算-采购调价对象 stlmt_purchase_price_adj
 *
 * <AUTHOR>
 * @date 2025-01-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("stlmt_purchase_price_adj")
public class StlmtPurchasePriceAdj extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 项目ID
     */
    private Long projectId;
    /**
     * 项目名称
     */
    private String projectName;
    /**
     * 调价编号
     */
    private String code;
    /**
     * 调价时间
     */
    private Date adjustTime;
    /**
     * 供应商ID
     */
    private Long vendorId;
    /**
     * 供应商名称
     */
    private String vendorName;
    /**
     * 状态 0待提交 1审核中 2审核通过 3驳回
     */
    private Integer status;
    /**
     * 备注
     */
    private String remark;
    /**
     * 提交人
     */
    private Long commitedBy;
    /**
     * 提交人名称
     */
    private String commitedName;
    /**
     * 提交时间
     */
    private Date commitedTime;
    /**
     * 审核人
     */
    private Long auditBy;
    /**
     * 审核人名称
     */
    private String auditName;
    /**
     * 审核时间
     */
    private Date auditTime;

    /**
     * 审核消息
     */
    private String auditMsg ;

}
