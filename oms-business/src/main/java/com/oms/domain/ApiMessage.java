package com.oms.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.oms.common.core.domain.BaseEntity;


/**
 * api消息对象 api_message
 *
 * <AUTHOR>
 * @date 2025-05-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("api_message")
public class ApiMessage extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 租户id
     */
    private Long tenantId;
    /**
     * 项目ID
     */
    private Long prjId;
    /**
     * 项目名称
     */
    private String prjName;
    /**
     * 中台消息id
     */
    private String omsMessageId;
    /**
     * 消息类型 1：商品消息 2：订单消息
     */
    private Long messageType;
    /**
     * 操作类型
     */
    private String operationType;
    /**
     * 中台处理状态 0:未处理；1:处理成功 2.处理失败 3.忽略
     */
    private Long status;
    /**
     * 客户处理状态 0:未处理；1:处理成功 2.处理失败 3.忽略
     */
    private Long clientStatus;
    /**
     * 消息内容
     */
    private String content;
    /**
     * 失败原因
     */
    private String reson;
    /**
     * 更新人名
     */
    private String updateName;
    /**
     * 创建人名
     */
    private String createName;
}
