package com.oms.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.oms.common.core.domain.BaseEntity;

import java.math.BigDecimal;

/**
 * 比价项目商品对象 price_project_goods
 *
 * <AUTHOR>
 * @date 2025-02-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("price_project_goods")
public class PriceProjectGoods extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 商品id
     */
    private Long itemId;
    /**
     * skuId
     */
    private Long skuId;
    /**
     * 商品名称
     */
    private String itemName;
    /**
     * 平台编号 jd-京东 tmall-天猫 suning-苏宁
     */
    private String platformCode;
    /**
     * 平台方名称
     */
    private String platformName;
    /**
     * 折扣率
     */
    private BigDecimal discount;

    /**
     * 是否满足折扣率  0-否 1-是
     */
    private Integer discountStatus;
    /**
     * 转换因子
     */
    private BigDecimal conversionFactor;
    /**
     * 项目id
     */
    private Long projectId;
    /**
     * 项目名称
     */
    private String projectName;
    /**
     * 销售价格
     */
    private BigDecimal salePrice;
    /**
     * 平台价格
     */
    private BigDecimal platformPrice;

    /**
     * 0-待处理 1-已处理 2-处理失败
     */
    private Integer status ;

}
