package com.oms.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.oms.common.core.domain.BaseEntity;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 项目外部属性值对象 goods_prj_out_attribute_value
 *
 * <AUTHOR>
 * @date 2025-02-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("goods_prj_out_attribute_value")
public class GoodsPrjOutAttributeValue extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 项目id
     */
    private Long prjId;
    /**
     * 外部属性值id
     */
    private String outAttributeId;
    /**
     * 属性值编码
     */
    private String valueCode;
    /**
     * 属性值名称
     */
    private String valueLabel;
    /**
     * 排序
     */
    private Long orderNum;
    /**
     * 状态 (正常-NOR，关闭-DIS)
     */
    private String status;
}
