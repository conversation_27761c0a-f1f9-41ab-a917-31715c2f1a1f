package com.oms.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.oms.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 投标单对象 tenders_info
 *
 * <AUTHOR>
 * @date 2024-10-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tenders_info")
public class TendersInfo extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 游标
     */
    private String cursorMark;
    /**
     * 信息id
     */
    private String infoId;
    /**
     * 信息标题
     */
    private String infoTitle;
    /**
     * 信息内容
     */
    private String infoContent;
    /**
     * 状态 0-新建  1-未参与  2-已提报，审批中  3-已提报，审批同意  4-已提报，审批驳回  5-正在参与  6-结果未出  7-中标  8-落标  9-弃标  10-流标  
     */
    private Integer status;

    /**
     * 备注
     */
    private String remark;
    /**
     * 信息类型 0：公告 1：预告 2：变更 3：结果 5：其他
     */
    private Integer infoType;
    /**
     * 二级信息类型细分：  1：意见征集  2：招标预告  3：招标公告  4：重新招标  5：信息变更  6：答疑公告  7：废标公告  8：流标公告  9：开标公示  10：候选人公示  11：中标通知  12：合同公告  13：验收合同  14：违规公告  15：其他公告
     */
    private Integer infoTypeSegment;
    /**
     * 发布时间
     */
    private Date infoPublishTime;

    /**
     * 省份id
     */
    private Long provinceId;

    /**
     * 省份名称
     */
    private String provinceName;

    /**
     * 市名称
     */
    private String cityName;

    /**
     * 区县名称
     */
    private String districtName;
    /**
     * 招标编号
     */
    private String tendersNumber;
    /**
     * 标书获取开始时间
     */
    private Date bidingAcquireTime;
    /**
     * 标书截止时间
     */
    private Date bidingEndTime;
    /**
     * 投标开始时间
     */
    private Date tenderBeginTime;
    /**
     * 投标截止时间
     */
    private Date tenderEndTime;
    /**
     * 开标时间
     */
    private Date openBidingTime;
    /**
     * 招标方式  0：公开招标  1：邀请招标  2：竞争性谈判或竞争性磋商  3：单一来源采购  4：询价  5：国务院政府采购监督管理部门认定的其他采购方式  6：电子反拍
     */
    private Integer biddingType;
    /**
     * 是否电子招标  1：是  0：否
     */
    private Integer isElectronic;
    /**
     * 招标单位
     */
    private String tenderCompany;
    /**
     * 招标单位联系人
     */
    private String tenderRelationName;
    /**
     * 招标单位联系方式
     */
    private String tenderRelationWay;
    /**
     * 中标单位
     */
    private String winBiddingUnit;
    /**
     * 中标单位联系人
     */
    private String winBiddingRelationName;
    /**
     * 中标单位联系方式
     */
    private String winBiddingRelationWay;
    /**
     * 中标金额
     */
    private BigDecimal bidWinnerAmount;
    /**
     * 预算金额
     */
    private BigDecimal bidBudget;
    /**
     * 资金来源
     */
    private String fundsSource;
    /**
     * 评标专家
     */
    private String bidEvaluationExpert;
    /**
     * 所属区域
     */
    private String belongingArea;
    /**
     * 物资分类
     */
    private String materialCategory;
    /**
     * 支撑部门
     */
    private String supportDepartment;
    /**
     * 项目等级
     */
    private Integer projectLevel;

    /**
     * 行业
     */
    private String industry ;


    /**
     * 中标公司数量
     */
    private Integer companyNumber;

    /**
     * 合同状态 0-合同签署中 1-合同签署完成 2-无合同,入驻中 3-无合同,入驻成功
     */
    private Integer contractStatus;

    /**
     * 中标状态 0-有效 1-失效
     */
    private Integer winBidStatus;

    /**
     * 操作模式 e_commerce-电商 purchar-采购
     */
    private String operationModel;

    /**
     * 结果公示时间
     */
    private Date resultShowDate;

    /**
     * 来源类型 interface-接口 bid-投标单 offline-线下
     */
    private String sourceType;

    /**
     * 是否需要审批 0-否 1-是
     */
    private String isNeedApprove ;

    /**
     * 下级审批用户id
     */
    private String nextApproveUserId ;

    /**
     * 匹配配置id
     */
    private Long approvalConfigId ;

    /**
     * 扩展数据
     */
    private String extraData;


}
