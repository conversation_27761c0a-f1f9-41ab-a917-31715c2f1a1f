package com.oms.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.oms.common.core.validate.AddGroup;
import com.oms.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.oms.common.core.domain.BaseEntity;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

import javax.validation.constraints.NotBlank;

/**
 * 比价商品折扣对象 price_project_goods_discount
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("price_project_goods_discount")
public class PriceProjectGoodsDiscount extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 商品id
     */
    private Long itemId;
    /**
     * skuId
     */
    private Long skuId;
    /**
     * 商品名称
     */
    private String itemName;

    /**
     * 平台编号 jd-京东 tmall-天猫 suning-苏宁
     */
    @NotBlank(message = "平台编号 jd-京东 tmall-天猫 suning-苏宁不能为空", groups = { AddGroup.class, EditGroup.class })
    private String platformCode;

    /**
     * 平台名称
     */
    @NotBlank(message = "平台名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String platformName;

    /**
     * 折扣率
     */
    private BigDecimal discount;
    /**
     * 项目id
     */
    private Long projectId;
    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 单位
     */
    private String unit ;

}
