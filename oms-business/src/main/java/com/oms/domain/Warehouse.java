package com.oms.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.oms.common.core.domain.BaseEntity;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 仓库对象 warehouse
 *
 * <AUTHOR>
 * @date 2025-01-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("warehouse")
public class Warehouse extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 仓库外部编号
     */
    private String outerCode;
    /**
     * 编码
     */
    private String warehouseCode;
    /**
     * 名称
     */
    private String warehouseName;
    /**
     * 类型 1-第三方
     */
    private Long warehouseType;
    /**
     * 省市区Id
     */
    private String divisionId;
    /**
     * 省市区地址
     */
    private String divisionAddress;
    /**
     * 详细地址
     */
    private String detailAddress;
    /**
     * 项目id
     */
    private Long projectId;
    /**
     * 项目名称
     */
    private String projectName;
    /**
     * 负责人
     */
    private String responsiblePerson;
    /**
     * 联系电话
     */
    private String phone;
    /**
     * 车辆信息json
     */
    private String deliveryJson;
    /**
     * 扩展json
     */
    private String extraJson;
    /**
     * 发货区域（省id)
     */
    private String deliveryAreas;
    /**
     * 备注
     */
    private String remark;
    /**
     * 退货省市区地址Id
     */
    private String returnDivisionId;
    /**
     * 退货省市区地址名称
     */
    private String returnDivisionAddress;
    /**
     * 退货详细地址
     */
    private String returnAddress;
    /**
     * 退货联系人
     */
    private String returnContact;
    /**
     * 退货联系电话
     */
    private String returnMobile;
    /**
     * 退货备注
     */
    private String returnRemarks;
    /**
     * 第三方仓库流水号
     */
    private String thridWno;
    /**
     * 状态 (正常-NOR，关闭-DIS)
     */
    private String status;
    /**
     * 创建人名
     */
    private String createName;
    /**
     * 更新人名
     */
    private String updateName;
    /**
     * 逻辑删除 (1-已删除，0-未删除)
     */
    private Integer deleted;
    /**
     * 删除时间
     */
    private Date deleteTime;
    /**
     * 删除人
     */
    private Long deleteBy;
    /**
     * 删除人名
     */
    private String deleteName;

}
