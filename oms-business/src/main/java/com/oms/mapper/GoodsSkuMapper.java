package com.oms.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.oms.common.core.mapper.BaseMapperPlus;
import com.oms.domain.GoodsSku;
import com.oms.domain.vo.GoodsSkuVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * skuMapper接口
 *
 * <AUTHOR>
 * @date 2024-09-21
 */
public interface GoodsSkuMapper extends BaseMapperPlus<GoodsSkuMapper, GoodsSku, GoodsSkuVo> {
    List<GoodsSkuVo> querySkuList(@Param(Constants.WRAPPER) Wrapper<GoodsSkuVo> queryWrapper);
}
