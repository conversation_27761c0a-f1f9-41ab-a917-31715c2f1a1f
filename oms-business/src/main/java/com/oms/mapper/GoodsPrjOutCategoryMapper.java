package com.oms.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.oms.common.core.mapper.BaseMapperPlus;
import com.oms.domain.GoodsPrjOutCategory;
import com.oms.domain.vo.GoodsPrjOutCategoryVo;
import org.apache.ibatis.annotations.Param;

/**
 * 项目外部类目Mapper接口
 *
 * <AUTHOR>
 * @date 2025-02-14
 */
public interface GoodsPrjOutCategoryMapper extends BaseMapperPlus<GoodsPrjOutCategoryMapper, GoodsPrjOutCategory, GoodsPrjOutCategoryVo> {
    Page<GoodsPrjOutCategoryVo> listByItem(@Param("page") Page<GoodsPrjOutCategory> page, @Param(Constants.WRAPPER) Wrapper<GoodsPrjOutCategory> queryWrapper);
}
