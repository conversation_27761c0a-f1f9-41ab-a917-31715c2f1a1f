package com.oms.esdump;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.api.client.util.Lists;
import com.google.api.client.util.Maps;
import com.oms.common.core.domain.BaseEntity;
import com.oms.common.enums.DomainDeletedType;
import com.oms.common.utils.StringUtils;
import com.oms.domain.GoodsItem;
import com.oms.domain.GoodsSku;
import com.oms.domain.GoodsUnit;
import com.oms.esmapper.GoodsItemEsMapper;
import com.oms.esmodel.GoodsItemEs;
import com.oms.mapper.GoodsItemMapper;
import com.oms.mapper.GoodsSkuMapper;
import com.oms.mapper.GoodsUnitMapper;
import com.oms.service.IGoodsBackCategoryService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 商品信息构造
 */
@Component
@RequiredArgsConstructor
public class GoodsItemEsDump {

    private final GoodsItemEsMapper goodsItemEsMapper;
    private final GoodsItemMapper goodsItemMapper;
    private final GoodsSkuMapper goodsSkuMapper;
    private final GoodsUnitMapper goodsUnitMapper;
    private final IGoodsBackCategoryService goodsBackCategoryService;

    public void buildEs(Long itemId){
        if(itemId == null){
            return;
        }
        GoodsItem item = goodsItemMapper.selectById(itemId);
        if(item == null){
            return;
        }
        List<GoodsSku> skus = goodsSkuMapper.selectList(new LambdaQueryWrapper<GoodsSku>()
                .eq(GoodsSku::getItemId, itemId)
                .eq(BaseEntity::getDeleted, DomainDeletedType.NORMAL.code()));
        Map<Long, List<GoodsSku>> skuMap = Maps.newHashMap();
        if(CollectionUtil.isNotEmpty(skus)){
            skuMap = skus.stream().collect(Collectors.groupingBy(GoodsSku::getItemId));
        }

        List<Long> ids = goodsBackCategoryService.queryFullParentById(item.getCategoryId());

        GoodsUnit unit = goodsUnitMapper.selectById(item.getUnit());

        GoodsItemEs es = new GoodsItemEs();
        es.setId(item.getId());
        es.setVendorId(item.getVendorId());
        es.setVendorName(item.getVendorName());
        es.setCategoryId(item.getCategoryId());
        es.setItemCode(item.getItemCode());
        es.setBrandId(item.getBrandId());
        es.setBrandName(item.getBrandName());
        es.setItemName(item.getItemName());
        es.setUniversalName(item.getUniversalName());
        es.setTitleName(item.getTitleName());
        es.setMainImage(item.getMainImage());
        es.setStatus(item.getStatus());
        if(item.getDeleted() == 1){
            es.setStatus("DELETE");
        }
        es.setItemType(item.getItemType());
        es.setCanGroup(item.getCanGroup());
        es.setCreateBy(item.getCreateBy());
        es.setCreateName(item.getCreateName());
        es.setCreateTime(item.getCreateTime() != null ?DateUtil.format(item.getCreateTime(), DatePattern.NORM_DATETIME_PATTERN) + "" : null);
        es.setUpdateBy(item.getUpdateBy());
        es.setUpdateName(item.getUpdateName());
        es.setUpdateTime(item.getUpdateTime() != null ? DateUtil.format(item.getUpdateTime(), DatePattern.NORM_DATETIME_PATTERN) + "" : null);
        es.setKeyword(item.getKeyword());
        if(CollectionUtil.isNotEmpty(ids)){
            Collections.reverse(ids);
            es.setCategoryIds(ids);
        }
        es.setUnit(item.getUnit());
        if(unit != null){
            es.setUnitName(unit.getUnitName());
        }
        es.setTaxCode(item.getTaxCode());
        es.setTaxName(item.getTaxName());
        es.setVatrate(item.getVatrate());
        es.setDataType(item.getDataType());
        es.setItemColor(item.getItemColor());
        es.setPushStatus(item.getPushStatus());
        es.setSupportReturn(item.getSupportReturn());
        es.setVideoUrl(item.getVideoUrl());
        es.setItemTag(item.getItemTag());
        List<GoodsSku> goodsSkus = skuMap.get(item.getId());
        if (CollectionUtil.isNotEmpty(goodsSkus)) {
            GoodsSku goodsSku = goodsSkus.get(0);
            es.setSkuId(goodsSku.getId());
            es.setMinQuantity(goodsSku.getMinQuantity());
            es.setSalesMultiple(goodsSku.getSalesMultiple());
            es.setSkuCode(goodsSku.getSkuCode());
            es.setBarcode(goodsSku.getBarcode());
            es.setSpecification(goodsSku.getSpecification());
            es.setCommunityModel(goodsSku.getCommunityModel());
            es.setSkuJson(goodsSku.getSkuJson());
            es.setLowSalePrice(Collections.min(goodsSkus, Comparator.comparing(GoodsSku::getSalePrice)).getSalePrice());
            es.setHighSalePrice(Collections.max(goodsSkus, Comparator.comparing(GoodsSku::getSalePrice)).getSalePrice());
//            es.setLowBasePrice(Collections.min(goodsSkus, Comparator.comparing(GoodsSku::getBasePrice)).getBasePrice());
//            es.setHighBasePrice(Collections.max(goodsSkus, Comparator.comparing(GoodsSku::getBasePrice)).getBasePrice());
            es.setLowMarketPrice(Collections.min(goodsSkus, Comparator.comparing(GoodsSku::getMarketPrice)).getMarketPrice());
            es.setHighMarketPrice(Collections.max(goodsSkus, Comparator.comparing(GoodsSku::getMarketPrice)).getMarketPrice());
        }
        goodsItemEsMapper.insert(es);
    }

    public void buildEs(List<Long> itemIds){
        if(CollectionUtil.isEmpty(itemIds)){
            return;
        }
        List<GoodsItem> itemList = goodsItemMapper.selectBatchIds(itemIds);
        if(CollectionUtil.isEmpty(itemList)){
            return;
        }
        List<GoodsSku> skus = goodsSkuMapper.selectList(new LambdaQueryWrapper<GoodsSku>()
                .in(GoodsSku::getItemId, itemIds)
                .eq(BaseEntity::getDeleted, DomainDeletedType.NORMAL.code()));
        Map<Long, List<GoodsSku>> skuMap = Maps.newHashMap();
        if(CollectionUtil.isNotEmpty(skus)){
            skuMap = skus.stream().collect(Collectors.groupingBy(GoodsSku::getItemId));
        }
        Map<Long,List<Long>> cateMap = Maps.newHashMap();

        Map<Long,GoodsUnit> unitMap = Maps.newHashMap();
        List<GoodsUnit> unitList = goodsUnitMapper.selectBatchIds(itemList.stream().map(GoodsItem::getUnit).distinct().collect(Collectors.toList()));
        if (CollectionUtil.isNotEmpty(unitList)) {
            unitMap = unitList.stream().collect(Collectors.toMap(GoodsUnit::getId, Function.identity()));
        }

        List<GoodsItemEs> esList = Lists.newArrayList();
        for (GoodsItem item : itemList) {
            GoodsItemEs es = new GoodsItemEs();
            es.setId(item.getId());
            es.setVendorId(item.getVendorId());
            es.setVendorName(item.getVendorName());
            es.setCategoryId(item.getCategoryId());
            es.setItemCode(item.getItemCode());
            es.setBrandId(item.getBrandId());
            es.setBrandName(item.getBrandName());
            es.setItemName(item.getItemName());
            es.setUniversalName(item.getUniversalName());
            es.setTitleName(item.getTitleName());
            es.setMainImage(item.getMainImage());
            es.setStatus(item.getStatus());
            if(item.getDeleted() == 1){
                es.setStatus("DELETE");
            }
            es.setItemType(item.getItemType());
            es.setCanGroup(item.getCanGroup());
            es.setCreateBy(item.getCreateBy());
            es.setCreateName(item.getCreateName());
            es.setCreateTime(item.getCreateTime() != null ?DateUtil.format(item.getCreateTime(), DatePattern.NORM_DATETIME_PATTERN) + "" : null);
            es.setUpdateBy(item.getUpdateBy());
            es.setUpdateName(item.getUpdateName());
            es.setUpdateTime(item.getUpdateTime() != null ? DateUtil.format(item.getUpdateTime(), DatePattern.NORM_DATETIME_PATTERN) + "" : null);
            es.setKeyword(item.getKeyword());
            List<Long> ids = cateMap.get(item.getCategoryId());
            if(CollectionUtil.isEmpty(ids)){
                ids = goodsBackCategoryService.queryFullParentById(item.getCategoryId());
                cateMap.put(item.getCategoryId(),ids);
            }
            if(CollectionUtil.isNotEmpty(ids)){
                Collections.reverse(ids);
                es.setCategoryIds(ids);
            }
            es.setUnit(item.getUnit());
            if(StringUtils.isNotBlank(item.getUnit()) && unitMap.containsKey(Long.parseLong(item.getUnit()))){
                es.setUnitName(unitMap.get(Long.parseLong(item.getUnit())).getUnitName());
            }
            es.setTaxCode(item.getTaxCode());
            es.setTaxName(item.getTaxName());
            es.setVatrate(item.getVatrate());
            es.setDataType(item.getDataType());
            es.setItemColor(item.getItemColor());
            es.setPushStatus(item.getPushStatus());
            es.setSupportReturn(item.getSupportReturn());
            es.setVideoUrl(item.getVideoUrl());
            es.setItemTag(item.getItemTag());
            List<GoodsSku> goodsSkus = skuMap.get(item.getId());
            if (CollectionUtil.isNotEmpty(goodsSkus)) {
                GoodsSku goodsSku = goodsSkus.get(0);
                es.setSkuId(goodsSku.getId());
                es.setMinQuantity(goodsSku.getMinQuantity());
                es.setSalesMultiple(goodsSku.getSalesMultiple());
                es.setSkuCode(goodsSku.getSkuCode());
                es.setBarcode(goodsSku.getBarcode());
                es.setSpecification(goodsSku.getSpecification());
                es.setCommunityModel(goodsSku.getCommunityModel());
                es.setSkuJson(goodsSku.getSkuJson());
                es.setLowSalePrice(Collections.min(goodsSkus, Comparator.comparing(GoodsSku::getSalePrice)).getSalePrice());
                es.setHighSalePrice(Collections.max(goodsSkus, Comparator.comparing(GoodsSku::getSalePrice)).getSalePrice());
//            es.setLowBasePrice(Collections.min(goodsSkus, Comparator.comparing(GoodsSku::getBasePrice)).getBasePrice());
//            es.setHighBasePrice(Collections.max(goodsSkus, Comparator.comparing(GoodsSku::getBasePrice)).getBasePrice());
                es.setLowMarketPrice(Collections.min(goodsSkus, Comparator.comparing(GoodsSku::getMarketPrice)).getMarketPrice());
                es.setHighMarketPrice(Collections.max(goodsSkus, Comparator.comparing(GoodsSku::getMarketPrice)).getMarketPrice());
            }
            esList.add(es);
        }
        goodsItemEsMapper.insertBatch(esList);
    }
}
