package com.oms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.oms.domain.OutAddressesMapping;
import com.oms.domain.vo.OutAddressesMappingVo;
import com.oms.domain.bo.OutAddressesMappingBo;
import com.oms.mapper.OutAddressesMappingMapper;

import com.oms.common.core.page.TableDataInfo;
import com.oms.common.core.domain.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 外部地址映射Service接口
 *
 * <AUTHOR>
 * @date 2024-10-13
 */
public interface IOutAddressesMappingService extends IService<OutAddressesMapping>{

    /**
     * 查询外部地址映射
     */
    OutAddressesMappingVo queryById(Long id);

    /**
     * 查询外部地址映射列表
     */
    TableDataInfo<OutAddressesMappingVo> queryPageList(OutAddressesMappingBo bo, PageQuery pageQuery);

    /**
     * 查询外部地址映射列表
     */
    List<OutAddressesMappingVo> queryList(OutAddressesMappingBo bo);

    /**
     * 新增外部地址映射
     */
    Boolean insertByBo(OutAddressesMappingBo bo);

    /**
     * 修改外部地址映射
     */
    Boolean updateByBo(OutAddressesMappingBo bo);

    /**
     * 校验并批量删除外部地址映射信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
