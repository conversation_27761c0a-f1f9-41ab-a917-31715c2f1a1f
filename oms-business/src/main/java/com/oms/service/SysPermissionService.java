package com.oms.service;

import com.oms.common.core.domain.entity.SysUser;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.Set;

/**
 * 用户权限处理
 *
 */
@RequiredArgsConstructor
@Service
public class SysPermissionService {

    private final ISysRoleService roleService;
    private final ISysMenuService menuService;

    /**
     * 获取角色数据权限
     *
     * @return 角色权限信息
     */
    public Set<String> getRolePermission(Long userId, Boolean isAdmin, String sysApp) {
        Set<String> roles = new HashSet<>();
        // 管理员拥有所有权限
        if (isAdmin) {
            roles.add("admin");
        } else {
            roles.add("admin");
            roles.addAll(roleService.selectRolePermissionByUserId(userId, sysApp));
        }
        return roles;
    }

    /**
     * 获取菜单数据权限
     *
     * @return 菜单权限信息
     */
    public Set<String> getMenuPermission(Long userId, Boolean isAdmin) {
        Set<String> perms = new HashSet<>();
        // 管理员拥有所有权限
        if (isAdmin) {
            perms.add("*:*:*");
        } else {
            perms.add("*:*:*");
//            perms.addAll(menuService.selectMenuPermsByUserId(userId));
        }
        return perms;
    }
}
