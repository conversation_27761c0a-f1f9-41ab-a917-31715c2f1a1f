package com.oms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.oms.domain.VendorContact;
import com.oms.domain.vo.VendorContactVo;
import com.oms.domain.bo.VendorContactBo;
import com.oms.mapper.VendorContactMapper;

import com.oms.common.core.page.TableDataInfo;
import com.oms.common.core.domain.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 供应商联系人Service接口
 *
 * <AUTHOR>
 * @date 2025-01-02
 */
public interface IVendorContactService extends IService<VendorContact>{

    /**
     * 查询供应商联系人
     */
    VendorContactVo queryById(Long id);

    /**
     * 查询供应商联系人列表
     */
    TableDataInfo<VendorContactVo> queryPageList(VendorContactBo bo);

    /**
     * 查询供应商联系人列表
     */
    List<VendorContactVo> queryList(VendorContactBo bo);

    /**
     * 新增供应商联系人
     */
    Boolean insertByBo(VendorContactBo bo);

    /**
     * 修改供应商联系人
     */
    Boolean updateByBo(VendorContactBo bo);

    /**
     * 校验并批量删除供应商联系人信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
