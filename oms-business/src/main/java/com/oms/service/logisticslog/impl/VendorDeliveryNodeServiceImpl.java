package com.oms.service.logisticslog.impl;

import com.oms.common.enums.LogisticsNodeEnum;
import com.oms.common.enums.LogisticsReplaceEnum;
import com.oms.common.enums.OrderOperationLogEnum;
import com.oms.common.exception.ServiceException;
import com.oms.common.utils.RandomUtils;
import com.oms.domain.LogisticsLogInfo;
import com.oms.domain.LogisticsRuleConfig;
import com.oms.domain.Order;
import com.oms.mapper.LogisticsLogInfoMapper;
import com.oms.mapper.OrderMapper;
import com.oms.service.ILogisticsLogInfoService;
import com.oms.service.IMessagePoolService;
import com.oms.service.logisticslog.ILogisticsLogCreateService;
import com.oms.service.logisticslog.LogisticslogContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2024/12/17
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class VendorDeliveryNodeServiceImpl implements ILogisticsLogCreateService {


    private final ILogisticsLogInfoService logisticsLogInfoService;

    private final IMessagePoolService messagePoolService;

    private final LogisticsLogInfoMapper logisticsLogInfoMapper;

    private final OrderMapper orderMapper;

    @Override
    public String getType() {
        return LogisticsNodeEnum.VENDOR_DELIVERY.name();
    }

    @Override
    public void execute(LogisticslogContext context) {
        //虚拟物流节点配置
        LogisticsRuleConfig logisticsRuleConfig = context.getLogisticsRuleConfig();
        //销售订单
        Order order = orderMapper.selectById(context.getBusinessId());
        if(Objects.isNull(logisticsRuleConfig) || Objects.isNull(order)){
            log.error("物流节点配置或采购单信息为空");
            throw new ServiceException("生成物流时发生异常");
        }
        LogisticsLogInfo logisticsLogInfo = new LogisticsLogInfo();
        logisticsLogInfo.setPrjId(order.getPrjId());
        logisticsLogInfo.setPrjName(order.getPrjName());
        logisticsLogInfo.setTenantId(order.getTenantId());
        logisticsLogInfo.setRuleType(1L);
        logisticsLogInfo.setOutId(order.getOutId());
        logisticsLogInfo.setPackageOrderId(order.getVirtualShipCode());
        logisticsLogInfo.setTrackingNum(order.getVirtualShipCode());
        logisticsLogInfo.setOrderId(order.getId());
        logisticsLogInfo.setVirtualShipCode(order.getVirtualShipCode());
        logisticsLogInfo.setReceiveMobile(order.getReceiveMobile());
        logisticsLogInfo.setReceiveName(order.getReceiveUserName());
        String content = logisticsRuleConfig.getContent();
        LogisticsReplaceEnum.replaceContent(content,logisticsLogInfoService.fillLogisticsReplaceInfo(order.getProvinceId()
                ,order.getCityName(),order.getReceiveUserName(),order.getVirtualShipCode()));
        logisticsLogInfo.setLogisticeContent(content);
        logisticsLogInfo.setLogisticeNodeNo(LogisticsNodeEnum.VENDOR_DELIVERY.getName());
        logisticsLogInfo.setLogisticeNodeName(LogisticsNodeEnum.VENDOR_DELIVERY.getDesc());
        //获取间隔时间
        String randomTimeRange = logisticsRuleConfig.getRandomTimeRang();
        if(!randomTimeRange.contains("-")){
            throw new ServiceException("获取轨迹随机时间异常");
        }
        String[] randomTimeSplit = randomTimeRange.split("-");
        if(randomTimeSplit.length == 2){
            LocalDateTime shippingAt = order.getShippingAt().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
            LocalDateTime localDateTime = shippingAt.plusSeconds(RandomUtils.getRandomInt2(Integer.parseInt(randomTimeSplit[0]), Integer.parseInt(randomTimeSplit[1])));
            logisticsLogInfo.setFTime(localDateTime);
        }
        //插入轨迹
        logisticsLogInfoMapper.insert(logisticsLogInfo);
        //轨迹时间连续判定
        logisticsLogInfoService.adjustFtime(logisticsLogInfo);
        //插入消息log
        messagePoolService.addMessage(messagePoolService.getMessagePoolBo(OrderOperationLogEnum.ADD_LOGISTICS_TRAJECTORY.getCode(),logisticsLogInfo));
    }
}
