package com.oms.service.logisticslog.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.oms.common.enums.LogisticsNodeEnum;
import com.oms.common.enums.LogisticsReplaceEnum;
import com.oms.common.enums.OrderOperationLogEnum;
import com.oms.common.exception.ServiceException;
import com.oms.common.utils.RandomUtils;
import com.oms.domain.LogisticsLogInfo;
import com.oms.domain.LogisticsRuleConfig;
import com.oms.domain.PreSellerOrder;
import com.oms.domain.SellerOrder;
import com.oms.domain.bo.MessagePoolBo;
import com.oms.mapper.LogisticsLogInfoMapper;
import com.oms.mapper.PreSellerOrderMapper;
import com.oms.service.ILogisticsLogInfoService;
import com.oms.service.IMessagePoolService;
import com.oms.service.logisticslog.ILogisticsLogCreateService;
import com.oms.service.logisticslog.LogisticslogContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2024/12/17
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PreSellerOrderAuditNodeServiceImpl implements ILogisticsLogCreateService {


    private final PreSellerOrderMapper preSellerOrderMapper;

    private final ILogisticsLogInfoService logisticsLogInfoService;

    private final LogisticsLogInfoMapper logisticsLogInfoMapper;

    private final IMessagePoolService messagePoolService;

    @Override
    public String getType() {
        return LogisticsNodeEnum.PRE_SELLER_ORDER_AUDIT.name();
    }


    /**
     * 生成预占单审核物流轨迹(幂等????)
     * @param context
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void execute(LogisticslogContext context) {
        //虚拟物流节点配置
        LogisticsRuleConfig logisticsRuleConfig = context.getLogisticsRuleConfig();
        //查询预占单
        PreSellerOrder preSellerOrder = preSellerOrderMapper.selectById(context.getBusinessId());
        if(Objects.isNull(logisticsRuleConfig) || Objects.isNull(preSellerOrder)){
            log.error("物流节点配置或销售单信息为空");
            throw new ServiceException("生成物流时发生异常");
        }
        //通过项目 + 外部订单号  + 节点名称查询轨迹是否已经存在
        Long checkCount = logisticsLogInfoMapper.selectCount(new LambdaQueryWrapper<LogisticsLogInfo>()
                .eq(LogisticsLogInfo::getLogisticeNodeNo,LogisticsNodeEnum.PRE_SELLER_ORDER_AUDIT.getName())
                .eq(LogisticsLogInfo::getOutId,preSellerOrder.getOutId())
                .eq(LogisticsLogInfo::getPrjId,preSellerOrder.getPrjId()));
        //如果存在 则不用再处理
        if(checkCount >0){
            return;
        }
        LogisticsLogInfo logisticsLogInfo = new LogisticsLogInfo();
        logisticsLogInfo.setPrjId(preSellerOrder.getPrjId());
        logisticsLogInfo.setPrjName(preSellerOrder.getPrjName());
        logisticsLogInfo.setTenantId(preSellerOrder.getTenantId());
        logisticsLogInfo.setRuleType(1L);
        logisticsLogInfo.setOutId(preSellerOrder.getOutId());
        logisticsLogInfo.setOrderId(preSellerOrder.getId());
        logisticsLogInfo.setPackageOrderId(preSellerOrder.getVirtualShipCode());
        //logisticsLogInfo.setOrderId();
        logisticsLogInfo.setVirtualShipCode(preSellerOrder.getVirtualShipCode());
        logisticsLogInfo.setTrackingNum(preSellerOrder.getVirtualShipCode());
        logisticsLogInfo.setReceiveMobile(preSellerOrder.getReceiveMobile());
        logisticsLogInfo.setReceiveName(preSellerOrder.getReceiveUserName());
        String content = logisticsRuleConfig.getContent();
        LogisticsReplaceEnum.replaceContent(content,logisticsLogInfoService.fillLogisticsReplaceInfo(preSellerOrder.getProvinceId()
                ,preSellerOrder.getCityName(),preSellerOrder.getReceiveUserName(),preSellerOrder.getVirtualShipCode()));
        logisticsLogInfo.setLogisticeContent(content);
        logisticsLogInfo.setLogisticeNodeNo(LogisticsNodeEnum.PRE_SELLER_ORDER_AUDIT.getName());
        logisticsLogInfo.setLogisticeNodeName(LogisticsNodeEnum.PRE_SELLER_ORDER_AUDIT.getDesc());
        //获取间隔时间
        String randomTimeRange = logisticsRuleConfig.getRandomTimeRang();
        if(!randomTimeRange.contains("-")){
            throw new ServiceException("获取轨迹随机时间异常");
        }
        String[] randomTimeSplit = randomTimeRange.split("-");
        if(randomTimeSplit.length == 2){
            LocalDateTime auditAt = preSellerOrder.getCreateTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
            LocalDateTime localDateTime = auditAt.plusSeconds(RandomUtils.getRandomInt2(Integer.parseInt(randomTimeSplit[0]), Integer.parseInt(randomTimeSplit[1])));
            logisticsLogInfo.setFTime(localDateTime);
        }
        //插入轨迹
        logisticsLogInfoMapper.insert(logisticsLogInfo);
        //轨迹时间连续判定
        logisticsLogInfoService.adjustFtime(logisticsLogInfo);
        //插入轨迹LOG
        messagePoolService.addMessage(messagePoolService.getMessagePoolBo(OrderOperationLogEnum.ADD_LOGISTICS_TRAJECTORY.getCode(),logisticsLogInfo));
    }

}
