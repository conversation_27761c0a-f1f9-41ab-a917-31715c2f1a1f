package com.oms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.oms.domain.RuleDictionary;
import com.oms.domain.vo.RuleDictionaryVo;
import com.oms.domain.bo.RuleDictionaryBo;
import com.oms.mapper.RuleDictionaryMapper;

import com.oms.common.core.page.TableDataInfo;
import com.oms.common.core.domain.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 规则字典Service接口
 *
 * <AUTHOR>
 * @date 2024-11-09
 */
public interface IRuleDictionaryService extends IService<RuleDictionary>{

    /**
     * 查询规则字典
     */
    RuleDictionaryVo queryById(Long id);

    /**
     * 查询规则字典列表
     */
    TableDataInfo<RuleDictionaryVo> queryPageList(RuleDictionaryBo bo, PageQuery pageQuery);

    /**
     * 查询规则字典列表
     */
    List<RuleDictionaryVo> queryList(RuleDictionaryBo bo);

    /**
     * 新增规则字典
     */
    Boolean insertByBo(RuleDictionaryBo bo);

    /**
     * 修改规则字典
     */
    Boolean updateByBo(RuleDictionaryBo bo);

    /**
     * 校验并批量删除规则字典信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    List<RuleDictionaryVo> queryListCache();
}
