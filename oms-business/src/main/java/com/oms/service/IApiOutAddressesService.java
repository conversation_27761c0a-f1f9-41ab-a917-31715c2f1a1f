package com.oms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.oms.domain.ApiOutAddresses;
import com.oms.domain.vo.ApiOutAddressesVo;
import com.oms.domain.bo.ApiOutAddressesBo;
import com.oms.mapper.ApiOutAddressesMapper;

import com.oms.common.core.page.TableDataInfo;
import com.oms.common.core.domain.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 外部系统自有地址库Service接口
 *
 * <AUTHOR>
 * @date 2025-05-08
 */
public interface IApiOutAddressesService extends IService<ApiOutAddresses>{

    /**
     * 查询外部系统自有地址库
     */
    ApiOutAddressesVo queryById(Long id);

    /**
     * 查询外部系统自有地址库列表
     */
    TableDataInfo<ApiOutAddressesVo> queryPageList(ApiOutAddressesBo bo, PageQuery pageQuery);

    /**
     * 查询外部系统自有地址库列表
     */
    List<ApiOutAddressesVo> queryList(ApiOutAddressesBo bo);

    /**
     * 新增外部系统自有地址库
     */
    Boolean insertByBo(ApiOutAddressesBo bo);

    /**
     * 修改外部系统自有地址库
     */
    Boolean updateByBo(ApiOutAddressesBo bo);

    /**
     * 校验并批量删除外部系统自有地址库信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
