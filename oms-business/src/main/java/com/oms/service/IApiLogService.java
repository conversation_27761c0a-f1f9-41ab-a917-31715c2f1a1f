package com.oms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.oms.common.core.domain.PageQuery;
import com.oms.common.core.page.TableDataInfo;
import com.oms.domain.ApiLog;
import com.oms.domain.bo.ApiLogBo;
import com.oms.domain.vo.ApiLogVo;

import java.util.Collection;
import java.util.List;

/**
 * api日志Service接口
 *
 * <AUTHOR>
 * @date 2025-05-07
 */
public interface IApiLogService extends IService<ApiLog>{

    /**
     * 查询api日志
     */
    ApiLogVo queryById(Long id);

    /**
     * 查询api日志列表
     */
    TableDataInfo<ApiLogVo> queryPageList(ApiLogBo bo, PageQuery pageQuery);

    /**
     * 查询api日志列表
     */
    List<ApiLogVo> queryList(ApiLogBo bo);

    /**
     * 新增api日志
     */
    Boolean insertByBo(ApiLogBo bo);

    /**
     * 修改api日志
     */
    Boolean updateByBo(ApiLogBo bo);

    /**
     * 校验并批量删除api日志信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
