package com.oms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.oms.domain.VendorContract;
import com.oms.domain.bo.extend.vendor.VendorAuditBo;
import com.oms.domain.bo.extend.vendor.VendorContractDoubleStampBo;
import com.oms.domain.vo.VendorContractVo;
import com.oms.domain.bo.VendorContractBo;

import com.oms.common.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 供应商合同Service接口
 *
 * <AUTHOR>
 * @date 2024-09-01
 */
public interface IVendorContractService extends IService<VendorContract>{

    /**
     * 查询供应商合同
     */
    VendorContractVo queryById(Long id);

    /**
     * 查询供应商合同列表
     */
    TableDataInfo<VendorContractVo> queryPageList(VendorContractBo bo);

    /**
     * 查询供应商合同列表
     */
    List<VendorContractVo> queryList(VendorContractBo bo);

    /**
     * 新增供应商合同
     */
    Boolean insertByBo(VendorContractBo bo);

    /**
     * 修改供应商合同
     */
    Boolean updateByBo(VendorContractBo bo);

    /**
     * 校验并批量删除供应商合同信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * oms供应商合同提醒
     * @param id
     * @return
     */
    Boolean vendorWarnInfo(Long id);

    /**
     * oms收回合同
     * @param id
     * @return
     */
    Boolean alreadyTakeBackContract(Long id);

    /**
     * oms收到合同
     * @param id
     * @return
     */
    Boolean receiveStampContract(Long id);

    /**
     * oms审核盖章合同
     * @param bo
     * @return
     */
    Boolean auditStampContract(VendorAuditBo bo);

    /**
     * 邮寄盖章合同
     * @param bo
     * @return
     */
    Boolean postOffStampContract(VendorContractBo bo);

    /**
     * 上传盖章合同
     * @param bo
     * @return
     */
    Boolean uploadStampContract(VendorContractBo bo);

    /**
     * oms上传双章合同
     * @param bo
     * @return
     */
    Boolean uploadDoubleStampContract(VendorContractDoubleStampBo bo);
}
