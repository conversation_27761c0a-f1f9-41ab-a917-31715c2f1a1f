package com.oms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.oms.domain.GoodsPrjSkuExtends;
import com.oms.domain.vo.GoodsPrjSkuExtendsVo;
import com.oms.domain.bo.GoodsPrjSkuExtendsBo;
import com.oms.mapper.GoodsPrjSkuExtendsMapper;

import com.oms.common.core.page.TableDataInfo;
import com.oms.common.core.domain.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 项目sku信息拓展Service接口
 *
 * <AUTHOR>
 * @date 2025-01-05
 */
public interface IGoodsPrjSkuExtendsService extends IService<GoodsPrjSkuExtends>{

    /**
     * 查询项目sku信息拓展
     */
    GoodsPrjSkuExtendsVo queryById(Long id);

    /**
     * 根据商品id 项目id 查询项目sku信息拓展
     */
    GoodsPrjSkuExtendsVo queryByItemId(GoodsPrjSkuExtendsBo bo);

    /**
     * 查询项目sku信息拓展列表
     */
    TableDataInfo<GoodsPrjSkuExtendsVo> queryPageList(GoodsPrjSkuExtendsBo bo, PageQuery pageQuery);

    /**
     * 查询项目sku信息拓展列表
     */
    List<GoodsPrjSkuExtendsVo> queryList(GoodsPrjSkuExtendsBo bo);

    /**
     * 新增项目sku信息拓展
     */
    Boolean insertByBo(GoodsPrjSkuExtendsBo bo);

    /**
     * 修改项目sku信息拓展
     */
    Boolean updateByBo(GoodsPrjSkuExtendsBo bo);

    /**
     * 校验并批量删除项目sku信息拓展信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
