package com.oms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.oms.common.core.domain.PageQuery;
import com.oms.common.core.page.TableDataInfo;
import com.oms.domain.ApprovalConfigUser;
import com.oms.domain.bo.ApprovalConfigUserBo;
import com.oms.domain.vo.ApprovalConfigUserVo;

import java.util.Collection;
import java.util.List;

/**
 * 审批用户配置Service接口
 *
 * <AUTHOR>
 * @date 2024-10-22
 */
public interface IApprovalConfigUserService extends IService<ApprovalConfigUser>{

    /**
     * 查询审批用户配置
     */
    ApprovalConfigUserVo queryById(Long id);

    /**
     * 查询审批用户配置列表
     */
    TableDataInfo<ApprovalConfigUserVo> queryPageList(ApprovalConfigUserBo bo, PageQuery pageQuery);

    /**
     * 查询审批用户配置列表
     */
    List<ApprovalConfigUserVo> queryList(ApprovalConfigUserBo bo);

    /**
     * 新增审批用户配置
     */
    Boolean insertByBo(ApprovalConfigUserBo bo);

    /**
     * 修改审批用户配置
     */
    Boolean updateByBo(ApprovalConfigUserBo bo);

    /**
     * 校验并批量删除审批用户配置信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
