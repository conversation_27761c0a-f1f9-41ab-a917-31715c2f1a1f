package com.oms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.oms.domain.VendorUpdateRecord;
import com.oms.domain.bo.extend.vendor.VendorAuditBo;
import com.oms.domain.vo.VendorUpdateRecordVo;
import com.oms.domain.bo.VendorUpdateRecordBo;

import com.oms.common.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 供应商修改记录Service接口
 *
 * <AUTHOR>
 * @date 2024-12-10
 */
public interface IVendorUpdateRecordService extends IService<VendorUpdateRecord>{

    /**
     * 查询供应商修改记录
     */
    VendorUpdateRecordVo queryById(Long id);

    /**
     * 查询供应商修改记录列表
     */
    TableDataInfo<VendorUpdateRecordVo> queryPageList(VendorUpdateRecordBo bo);

    /**
     * 查询供应商修改记录列表
     */
    List<VendorUpdateRecordVo> queryList(VendorUpdateRecordBo bo);

    /**
     * 新增供应商修改记录
     */
    Boolean insertByBo(VendorUpdateRecordBo bo);

    /**
     * 修改供应商修改记录
     */
    Boolean updateByBo(VendorUpdateRecordBo bo);

    /**
     * 校验并批量删除供应商修改记录信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 审核
     * @param bo
     * @return
     */
    Boolean audit(VendorAuditBo bo);

    /**
     * 获取审核信息
     */
    VendorUpdateRecordVo getCurentLastInfo();
}
