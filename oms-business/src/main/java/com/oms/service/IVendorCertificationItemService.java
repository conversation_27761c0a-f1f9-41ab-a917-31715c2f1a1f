package com.oms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.oms.domain.VendorCertificationItem;
import com.oms.domain.vo.VendorCertificationItemVo;
import com.oms.domain.bo.VendorCertificationItemBo;
import com.oms.mapper.VendorCertificationItemMapper;

import com.oms.common.core.page.TableDataInfo;
import com.oms.common.core.domain.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 供应商资质详情Service接口
 *
 * <AUTHOR>
 * @date 2024-10-13
 */
public interface IVendorCertificationItemService extends IService<VendorCertificationItem>{

    /**
     * 查询供应商资质详情
     */
    VendorCertificationItemVo queryById(Long id);

    /**
     * 查询供应商资质详情列表
     */
    TableDataInfo<VendorCertificationItemVo> queryPageList(VendorCertificationItemBo bo, PageQuery pageQuery);

    /**
     * 查询供应商资质详情列表
     */
    List<VendorCertificationItemVo> queryList(VendorCertificationItemBo bo);

    /**
     * 新增供应商资质详情
     */
    Boolean insertByBo(VendorCertificationItemBo bo);

    /**
     * 修改供应商资质详情
     */
    Boolean updateByBo(VendorCertificationItemBo bo);

    /**
     * 校验并批量删除供应商资质详情信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
