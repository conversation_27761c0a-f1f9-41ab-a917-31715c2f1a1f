package com.oms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.oms.domain.VendorApplyCooperation;
import com.oms.domain.vo.VendorApplyCooperationVo;
import com.oms.domain.bo.VendorApplyCooperationBo;
import com.oms.mapper.VendorApplyCooperationMapper;

import com.oms.common.core.page.TableDataInfo;
import com.oms.common.core.domain.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 供应商申请合作Service接口
 *
 * <AUTHOR>
 * @date 2024-10-11
 */
public interface IVendorApplyCooperationService extends IService<VendorApplyCooperation>{

    /**
     * 查询供应商申请合作
     */
    VendorApplyCooperationVo queryById(Long id);

    /**
     * 查询供应商申请合作列表
     */
    TableDataInfo<VendorApplyCooperationVo> queryPageList(VendorApplyCooperationBo bo);

    /**
     * 查询供应商申请合作列表
     */
    List<VendorApplyCooperationVo> queryList(VendorApplyCooperationBo bo);

    /**
     * 新增供应商申请合作
     */
    Boolean insertByBo(VendorApplyCooperationBo bo);

    /**
     * 修改供应商申请合作
     */
    Boolean updateByBo(VendorApplyCooperationBo bo);

    /**
     * 校验并批量删除供应商申请合作信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 修改供应商申请合作状态
     * @param id
     * @return
     */
    Boolean updateStatus(Long id, Integer status);
}
