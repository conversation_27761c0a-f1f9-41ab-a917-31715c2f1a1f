package com.oms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.oms.domain.SysApp;
import com.oms.domain.vo.SysAppVo;
import com.oms.domain.bo.SysAppBo;
import com.oms.mapper.SysAppMapper;

import com.oms.common.core.page.TableDataInfo;
import com.oms.common.core.domain.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 系统应用程序Service接口
 *
 * <AUTHOR>
 * @date 2024-09-05
 */
public interface ISysAppService extends IService<SysApp>{

    /**
     * 查询系统应用程序
     */
    SysAppVo queryById(Long id);

    /**
     * 查询系统应用程序列表
     */
    TableDataInfo<SysAppVo> queryPageList(SysAppBo bo, PageQuery pageQuery);

    /**
     * 查询系统应用程序列表
     */
    List<SysAppVo> queryList(SysAppBo bo);

    /**
     * 新增系统应用程序
     */
    Boolean insertByBo(SysAppBo bo);

    /**
     * 修改系统应用程序
     */
    Boolean updateByBo(SysAppBo bo);

    /**
     * 校验并批量删除系统应用程序信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
