package com.oms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.oms.domain.VendorContractTemplate;
import com.oms.domain.vo.VendorContractTemplateVo;
import com.oms.domain.bo.VendorContractTemplateBo;
import com.oms.mapper.VendorContractTemplateMapper;

import com.oms.common.core.page.TableDataInfo;
import com.oms.common.core.domain.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 供应商合同模板Service接口
 *
 * <AUTHOR>
 * @date 2024-09-01
 */
public interface IVendorContractTemplateService extends IService<VendorContractTemplate>{

    /**
     * 查询供应商合同模板
     */
    VendorContractTemplateVo queryById(String id);

    /**
     * 查询供应商合同模板列表
     */
    TableDataInfo<VendorContractTemplateVo> queryPageList(VendorContractTemplateBo bo);

    /**
     * 查询供应商合同模板列表
     */
    List<VendorContractTemplateVo> queryList(VendorContractTemplateBo bo);

    /**
     * 新增供应商合同模板
     */
    Boolean insertByBo(VendorContractTemplateBo bo);

    /**
     * 修改供应商合同模板
     */
    Boolean updateByBo(VendorContractTemplateBo bo);

    /**
     * 删除合同模版
     */
    Boolean deleteById(Long id);

    /**
     * 查询有效合同模板列表
     * @param bo
     * @return
     */
    List<VendorContractTemplateVo> usableList(VendorContractTemplateBo bo);

    /**
     * 供应商合同调整状态
     * @param id
     * @param status
     * @return
     */
    Boolean changeStatus(Long id, Integer status);
}
