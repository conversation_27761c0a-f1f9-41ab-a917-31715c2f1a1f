package com.oms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.oms.common.core.domain.PageQuery;
import com.oms.common.core.page.TableDataInfo;
import com.oms.domain.ApiUrl;
import com.oms.domain.bo.ApiUrlBo;
import com.oms.domain.vo.ApiUrlVo;

import java.util.Collection;
import java.util.List;

/**
 * api接口Service接口
 *
 * <AUTHOR>
 * @date 2025-05-07
 */
public interface IApiUrlService extends IService<ApiUrl>{

    /**
     * 查询api接口
     */
    ApiUrlVo queryById(Long id);

    /**
     * 查询api接口列表
     */
    TableDataInfo<ApiUrlVo> queryPageList(ApiUrlBo bo, PageQuery pageQuery);

    /**
     * 查询api接口列表
     */
    List<ApiUrlVo> queryList(ApiUrlBo bo);

    /**
     * 新增api接口
     */
    Boolean insertByBo(ApiUrlBo bo);

    /**
     * 修改api接口
     */
    Boolean updateByBo(ApiUrlBo bo);

    /**
     * 校验并批量删除api接口信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
