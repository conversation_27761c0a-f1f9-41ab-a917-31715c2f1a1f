package com.oms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.oms.common.core.page.AsyncTableDataInfo;
import com.oms.domain.SysAsyncImport;
import com.oms.domain.vo.SysAsyncImportVo;
import com.oms.domain.bo.SysAsyncImportBo;
import com.oms.mapper.SysAsyncImportMapper;

import com.oms.common.core.page.TableDataInfo;
import com.oms.common.core.domain.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 导入任务Service接口
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
public interface ISysAsyncImportService extends IService<SysAsyncImport>{

    /**
     * 查询导入任务
     */
    SysAsyncImportVo queryById(Long id);

    /**
     * 查询导入任务列表
     */
    AsyncTableDataInfo<SysAsyncImportVo> queryPageList(SysAsyncImportBo bo, PageQuery pageQuery);

    /**
     * 查询导入任务列表
     */
    List<SysAsyncImportVo> queryList(SysAsyncImportBo bo);

    /**
     * 新增导入任务
     */
    Boolean insertByBo(SysAsyncImportBo bo);

    /**
     * 修改导入任务
     */
    Boolean updateByBo(SysAsyncImportBo bo);

    /**
     * 校验并批量删除导入任务信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
