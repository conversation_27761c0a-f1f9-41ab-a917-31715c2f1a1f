package com.oms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.oms.common.core.domain.PageQuery;
import com.oms.common.core.page.TableDataInfo;
import com.oms.domain.RuleLine;
import com.oms.domain.bo.RuleLineBo;
import com.oms.domain.bo.RuleLineCreateBo;
import com.oms.domain.bo.RuleMatchingBo;
import com.oms.domain.vo.RuleDictionaryVo;
import com.oms.domain.vo.RuleDimensionVo;
import com.oms.domain.vo.RuleLineVo;
import com.oms.domain.vo.RuleOrderMatchVo;

import java.util.Collection;
import java.util.List;

/**
 * 规则行Service接口
 *
 * <AUTHOR>
 * @date 2024-11-09
 */
public interface IRuleLineService extends IService<RuleLine>{

    /**
     * 查询规则行
     */
    RuleLineVo queryById(Long id);

    /**
     * 查询规则行列表
     */
    TableDataInfo<RuleLineVo> queryPageList(RuleLineBo bo, PageQuery pageQuery);

    /**
     * 查询规则行列表
     */
    List<RuleLineVo> queryList(RuleLineBo bo);

    /**
     * 新增规则行
     */
    Boolean insertByBo(RuleLineBo bo);

    /**
     * 修改规则行
     */
    Boolean updateByBo(RuleLineBo bo);

    /**
     * 校验并批量删除规则行信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 获取规则创还能基础信息 (新增规则的时候调用)
     * @param ruleTypeCode 规则类型编码
     * @return /
     */
    RuleLineVo queryRuleCreate(String ruleTypeCode);

    /**
     * 获取规则类型的匹配条件
     * @param ruleTypeCode 规则code
     * @param overall 是否全局
     * @return /
     */
    List<RuleDimensionVo> queryMatchingCondition(String ruleTypeCode, Integer overall);

    /**
     * 保存规则行
     * @param bo /
     * @return /
     */
    Boolean saveRule(RuleLineCreateBo bo);

    /**
     * 获取规则类型的匹配条件-下拉值
     * @param dimValue 字典值
     * @return /
     */
    List<RuleDictionaryVo> queryMatchingConditionDictionary(String dimValue);

    /**
     * 批量禁用
     * @param ids 规则ID列表
     */
    Boolean disabled(Long[] ids);

    /**
     * 规则模拟匹配
     */
    List<RuleOrderMatchVo> queryRuleMatchingImitate(RuleMatchingBo bo);
}
