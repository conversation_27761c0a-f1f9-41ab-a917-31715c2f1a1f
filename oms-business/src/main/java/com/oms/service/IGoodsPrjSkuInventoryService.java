package com.oms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.oms.domain.GoodsPrjSkuInventory;
import com.oms.domain.vo.GoodsPrjSkuInventoryVo;
import com.oms.domain.bo.GoodsPrjSkuInventoryBo;
import com.oms.mapper.GoodsPrjSkuInventoryMapper;

import com.oms.common.core.page.TableDataInfo;
import com.oms.common.core.domain.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 项目商品sku库存Service接口
 *
 * <AUTHOR>
 * @date 2025-03-20
 */
public interface IGoodsPrjSkuInventoryService extends IService<GoodsPrjSkuInventory>{

    /**
     * 查询项目商品sku库存
     */
    GoodsPrjSkuInventoryVo queryById(Long id);

    /**
     * 查询项目商品sku库存列表
     */
    TableDataInfo<GoodsPrjSkuInventoryVo> queryPageList(GoodsPrjSkuInventoryBo bo, PageQuery pageQuery);

    /**
     * 查询项目商品sku库存列表
     */
    List<GoodsPrjSkuInventoryVo> queryList(GoodsPrjSkuInventoryBo bo);

    /**
     * 新增项目商品sku库存
     */
    Boolean insertByBo(GoodsPrjSkuInventoryBo bo);

    /**
     * 修改项目商品sku库存
     */
    Boolean updateByBo(GoodsPrjSkuInventoryBo bo);

    /**
     * 保存项目商品sku库存
     */
    Boolean addOrUpdate(GoodsPrjSkuInventoryBo bo);

    /**
     * 校验并批量删除项目商品sku库存信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
