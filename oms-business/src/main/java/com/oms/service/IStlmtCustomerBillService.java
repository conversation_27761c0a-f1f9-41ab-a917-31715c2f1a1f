package com.oms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.oms.domain.StlmtCustomerBill;
import com.oms.domain.vo.StlmtCustomerBillVo;
import com.oms.domain.bo.StlmtCustomerBillBo;
import com.oms.mapper.StlmtCustomerBillMapper;

import com.oms.common.core.page.TableDataInfo;
import com.oms.common.core.domain.PageQuery;

import javax.validation.constraints.NotNull;
import java.util.Collection;
import java.util.List;

/**
 * 客户对账单Service接口
 *
 * <AUTHOR>
 * @date 2025-02-04
 */
public interface IStlmtCustomerBillService extends IService<StlmtCustomerBill>{

    /**
     * 查询客户对账单
     */
    StlmtCustomerBillVo queryById(Long id);

    /**
     * 查询客户对账单列表
     */
    TableDataInfo<StlmtCustomerBillVo> queryPageList(StlmtCustomerBillBo bo);

    /**
     * 查询客户对账单列表
     */
    List<StlmtCustomerBillVo> queryList(StlmtCustomerBillBo bo);

    /**
     * 新增客户对账单
     */
    Boolean insertByBo(StlmtCustomerBillBo bo);

    /**
     * 修改客户对账单
     */
    Boolean updateByBo(StlmtCustomerBillBo bo);

    /**
     * 校验并批量删除客户对账单信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 提交客户对账单
     * @param ids
     * @return
     */
    Boolean submit(List<Long> ids);

    /**
     * 审核客户对账单
     * @param id
     * @param auditStatus
     * @param auditMsg
     * @return
     */
    Boolean audit(Long id, Integer auditStatus, String auditMsg);
}
