package com.oms.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.oms.common.core.domain.BaseEntity;
import com.oms.common.core.page.TableDataInfo;
import com.oms.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.oms.common.helper.LoginHelper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.oms.domain.bo.VendorInfoExtendBo;
import com.oms.domain.vo.VendorInfoExtendVo;
import com.oms.domain.VendorInfoExtend;
import com.oms.mapper.VendorInfoExtendMapper;
import com.oms.service.IVendorInfoExtendService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 供应商扩展信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-01
 */
@RequiredArgsConstructor
@Service
public class VendorInfoExtendServiceImpl extends ServiceImpl<VendorInfoExtendMapper, VendorInfoExtend> implements IVendorInfoExtendService {

    private final VendorInfoExtendMapper baseMapper;

    /**
     * 查询供应商扩展信息
     */
    @Override
    public VendorInfoExtendVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询供应商扩展信息列表
     */
    @Override
    public TableDataInfo<VendorInfoExtendVo> queryPageList(VendorInfoExtendBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<VendorInfoExtend> lqw = buildQueryWrapper(bo);
        Page<VendorInfoExtendVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询供应商扩展信息列表
     */
    @Override
    public List<VendorInfoExtendVo> queryList(VendorInfoExtendBo bo) {
        LambdaQueryWrapper<VendorInfoExtend> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<VendorInfoExtend> buildQueryWrapper(VendorInfoExtendBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<VendorInfoExtend> lqw = Wrappers.lambdaQuery();
        lqw.eq(BaseEntity::getTenantId, LoginHelper.getTenantId());
        lqw.eq(StrUtil.isNotBlank(bo.getLogo()), VendorInfoExtend::getLogo, bo.getLogo());
        lqw.eq(StrUtil.isNotBlank(bo.getBusinessLicenseUrl()), VendorInfoExtend::getBusinessLicenseUrl, bo.getBusinessLicenseUrl());
        lqw.eq(bo.getBusinessLicenseStartDate() != null, VendorInfoExtend::getBusinessLicenseStartDate, bo.getBusinessLicenseStartDate());
        lqw.eq(bo.getBusinessLicenseEndDate() != null, VendorInfoExtend::getBusinessLicenseEndDate, bo.getBusinessLicenseEndDate());
        lqw.eq(bo.getBusinessLicenseFlag() != null, VendorInfoExtend::getBusinessLicenseFlag, bo.getBusinessLicenseFlag());
        lqw.eq(StrUtil.isNotBlank(bo.getAccountOpeningLicenseUrl()), VendorInfoExtend::getAccountOpeningLicenseUrl, bo.getAccountOpeningLicenseUrl());
        lqw.eq(StrUtil.isNotBlank(bo.getLegalPerson()), VendorInfoExtend::getLegalPerson, bo.getLegalPerson());
        lqw.like(StrUtil.isNotBlank(bo.getInvoiceCompanyName()), VendorInfoExtend::getInvoiceCompanyName, bo.getInvoiceCompanyName());
        lqw.eq(bo.getTaxpayerType() != null, VendorInfoExtend::getTaxpayerType, bo.getTaxpayerType());
        lqw.eq(StrUtil.isNotBlank(bo.getInvoiceCompanyAddress()), VendorInfoExtend::getInvoiceCompanyAddress, bo.getInvoiceCompanyAddress());
        lqw.eq(StrUtil.isNotBlank(bo.getInvoiceTelephone()), VendorInfoExtend::getInvoiceTelephone, bo.getInvoiceTelephone());
        lqw.eq(StrUtil.isNotBlank(bo.getInvoiceBank()), VendorInfoExtend::getInvoiceBank, bo.getInvoiceBank());
        lqw.eq(StrUtil.isNotBlank(bo.getInvoiceAccno()), VendorInfoExtend::getInvoiceAccno, bo.getInvoiceAccno());
        lqw.eq(StrUtil.isNotBlank(bo.getInvoiceInfoCertUrl()), VendorInfoExtend::getInvoiceInfoCertUrl, bo.getInvoiceInfoCertUrl());
        lqw.like(StrUtil.isNotBlank(bo.getBankName()), VendorInfoExtend::getBankName, bo.getBankName());
        lqw.like(StrUtil.isNotBlank(bo.getBankSubBranchName()), VendorInfoExtend::getBankSubBranchName, bo.getBankSubBranchName());
        lqw.eq(StrUtil.isNotBlank(bo.getBankAccount()), VendorInfoExtend::getBankAccount, bo.getBankAccount());
        lqw.like(StrUtil.isNotBlank(bo.getBankAccountName()), VendorInfoExtend::getBankAccountName, bo.getBankAccountName());
        lqw.eq(StrUtil.isNotBlank(bo.getAssessFileUrl()), VendorInfoExtend::getAssessFileUrl, bo.getAssessFileUrl());
        lqw.eq(bo.getDefaultReturnProvinceId() != null, VendorInfoExtend::getDefaultReturnProvinceId, bo.getDefaultReturnProvinceId());
        lqw.eq(bo.getDefaultReturnCityId() != null, VendorInfoExtend::getDefaultReturnCityId, bo.getDefaultReturnCityId());
        lqw.eq(bo.getDefaultReturnRegionId() != null, VendorInfoExtend::getDefaultReturnRegionId, bo.getDefaultReturnRegionId());
        lqw.eq(bo.getDefaultReturnStreetId() != null, VendorInfoExtend::getDefaultReturnStreetId, bo.getDefaultReturnStreetId());
        lqw.like(StrUtil.isNotBlank(bo.getDefaultReturnProvinceName()), VendorInfoExtend::getDefaultReturnProvinceName, bo.getDefaultReturnProvinceName());
        lqw.like(StrUtil.isNotBlank(bo.getDefaultReturnCityName()), VendorInfoExtend::getDefaultReturnCityName, bo.getDefaultReturnCityName());
        lqw.like(StrUtil.isNotBlank(bo.getDefaultReturnRegionName()), VendorInfoExtend::getDefaultReturnRegionName, bo.getDefaultReturnRegionName());
        lqw.like(StrUtil.isNotBlank(bo.getDefaultReturnStreetName()), VendorInfoExtend::getDefaultReturnStreetName, bo.getDefaultReturnStreetName());
        lqw.eq(StrUtil.isNotBlank(bo.getDefaultReturnAddress()), VendorInfoExtend::getDefaultReturnAddress, bo.getDefaultReturnAddress());
        lqw.eq(StrUtil.isNotBlank(bo.getDefaultReturnContacts()), VendorInfoExtend::getDefaultReturnContacts, bo.getDefaultReturnContacts());
        lqw.eq(StrUtil.isNotBlank(bo.getDefaultReturnContactsMobile()), VendorInfoExtend::getDefaultReturnContactsMobile, bo.getDefaultReturnContactsMobile());
        lqw.like(StrUtil.isNotBlank(bo.getCreateName()), VendorInfoExtend::getCreateName, bo.getCreateName());
        lqw.like(StrUtil.isNotBlank(bo.getUpdateName()), VendorInfoExtend::getUpdateName, bo.getUpdateName());
        lqw.eq(bo.getDeleted() != null, VendorInfoExtend::getDeleted, bo.getDeleted());
        lqw.eq(bo.getDeleteTime() != null, VendorInfoExtend::getDeleteTime, bo.getDeleteTime());
        lqw.eq(bo.getDeleteBy() != null, VendorInfoExtend::getDeleteBy, bo.getDeleteBy());
        lqw.like(StrUtil.isNotBlank(bo.getDeleteName()), VendorInfoExtend::getDeleteName, bo.getDeleteName());
        return lqw;
    }

    /**
     * 新增供应商扩展信息
     */
    @Override
    public Boolean insertByBo(VendorInfoExtendBo bo) {
        VendorInfoExtend add = BeanUtil.toBean(bo, VendorInfoExtend.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改供应商扩展信息
     */
    @Override
    public Boolean updateByBo(VendorInfoExtendBo bo) {
        VendorInfoExtend update = BeanUtil.toBean(bo, VendorInfoExtend.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(VendorInfoExtend entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除供应商扩展信息
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
