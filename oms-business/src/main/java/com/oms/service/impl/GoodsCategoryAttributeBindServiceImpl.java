package com.oms.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.api.client.util.Lists;
import com.google.api.client.util.Maps;
import com.oms.common.core.domain.BaseEntity;
import com.oms.common.core.domain.PageQuery;
import com.oms.common.core.page.TableDataInfo;
import com.oms.common.enums.DictStatusEnums;
import com.oms.common.enums.DomainDeletedType;
import com.oms.common.exception.ServiceException;
import com.oms.common.helper.LoginHelper;
import com.oms.domain.GoodsAttributeValue;
import com.oms.domain.GoodsCategoryAttributeBind;
import com.oms.domain.bo.GoodsCategoryAttributeBindBo;
import com.oms.domain.vo.GoodsAttributeValueVo;
import com.oms.domain.vo.GoodsCategoryAttributeBindVo;
import com.oms.mapper.GoodsAttributeValueMapper;
import com.oms.mapper.GoodsCategoryAttributeBindMapper;
import com.oms.service.IGoodsBackCategoryService;
import com.oms.service.IGoodsCategoryAttributeBindService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 商品类目属性绑定Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-19
 */
@RequiredArgsConstructor
@Service
public class GoodsCategoryAttributeBindServiceImpl extends ServiceImpl<GoodsCategoryAttributeBindMapper, GoodsCategoryAttributeBind> implements IGoodsCategoryAttributeBindService {

    private final GoodsCategoryAttributeBindMapper baseMapper;
    private final IGoodsBackCategoryService iGoodsBackCategoryService;
    private final GoodsAttributeValueMapper goodsAttributeValueMapper;

    /**
     * 查询商品类目属性绑定
     * @return
     */
    @Override
    public GoodsCategoryAttributeBindVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询商品类目属性绑定列表
     */
    @Override
    public TableDataInfo<GoodsCategoryAttributeBindVo> queryPageList(GoodsCategoryAttributeBindBo bo) {
        PageQuery pageQuery = JSONUtil.toBean(JSONUtil.toJsonStr(bo), PageQuery.class);
        LambdaQueryWrapper<GoodsCategoryAttributeBind> lqw = buildQueryWrapper(bo);
        Page<GoodsCategoryAttributeBindVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    @Override
    public List<GoodsCategoryAttributeBindVo> queryByCateIdToItem(GoodsCategoryAttributeBindBo bo){
        bo.setStatus(DictStatusEnums.NORMAL.code());
        List<GoodsCategoryAttributeBindVo> list = queryList(bo);
        if(CollectionUtil.isNotEmpty(list)){
            List<Long> attrIds = list.stream().map(GoodsCategoryAttributeBindVo::getAttributeId).collect(Collectors.toList());
            if(CollectionUtil.isNotEmpty(attrIds)){
                LambdaQueryWrapper<GoodsAttributeValue> lqw = Wrappers.lambdaQuery(GoodsAttributeValue.class)
                        .in(GoodsAttributeValue::getAttributeId,attrIds)
                        .eq(GoodsAttributeValue::getDeleted, DomainDeletedType.NORMAL.code());
                List<GoodsAttributeValue> goodsAttributeValues = goodsAttributeValueMapper.selectList(lqw);
                Map<Long, List<GoodsAttributeValue>> valueMap = Maps.newHashMap();
                if(CollectionUtil.isNotEmpty(goodsAttributeValues)){
                    valueMap = goodsAttributeValues.stream().collect(Collectors.groupingBy(GoodsAttributeValue::getAttributeId));
                }
                for (GoodsCategoryAttributeBindVo vo : list) {
                    vo.setGoodsAttributeValueVoList(BeanUtil.copyToList(valueMap.get(vo.getAttributeId()), GoodsAttributeValueVo.class));
                }
            }
        }
        return list;
    }

    /**
     * 查询商品类目属性绑定列表
     */
    @Override
    public List<GoodsCategoryAttributeBindVo> queryList(GoodsCategoryAttributeBindBo bo) {
        //根据分类 获取当前分类的所有父级数据
        List<Long> categoryIds = iGoodsBackCategoryService.queryFullParentById(bo.getCategoryId());
        if(CollectionUtil.isEmpty(categoryIds)){
            return CollectionUtil.newArrayList();
        }
        List<GoodsCategoryAttributeBindVo> result = Lists.newArrayList();
        //根据当前类目 获取类目以及类目的所有父类绑定的属性
        List<GoodsCategoryAttributeBindVo> list = baseMapper.queryAttributeIdByCategoryId(bo.getTenantId(), categoryIds);
        if(CollectionUtil.isNotEmpty(list)){
            //根据属性id分组  如果当前类目 与父类都存在相同属性 优先取当前类目自己绑定的属性
            Map<Long, List<GoodsCategoryAttributeBindVo>> map = list.stream().collect(Collectors.groupingBy(GoodsCategoryAttributeBindVo::getAttributeId));
            for (Long attributeId : map.keySet()) {
                List<GoodsCategoryAttributeBindVo> bindVos = map.get(attributeId);
                if(bindVos.size() == 1){
                    GoodsCategoryAttributeBindVo vo = bindVos.get(0);
                    if(vo.getCategoryId().equals(bo.getCategoryId())){
                        vo.setHasExtends("N");
                    }else{
                        vo.setHasExtends("Y");
                    }
                    result.add(vo);
                }else{
                    //如果当前属性有多个 则取当前类目的属性 如果当前类目没绑定该属性 直接取第一个
                    List<GoodsCategoryAttributeBindVo> collect = bindVos.stream().filter(f -> f.getCategoryId().equals(bo.getCategoryId())).collect(Collectors.toList());
                    if(CollectionUtil.isNotEmpty(collect)){
                        GoodsCategoryAttributeBindVo vo = collect.get(0);
                        vo.setHasExtends("N");
                        result.add(vo);
                    }else{
                        GoodsCategoryAttributeBindVo vo = bindVos.get(0);
                        vo.setHasExtends("Y");
                        result.add(vo);
                    }
                }
            }
//            for (GoodsCategoryAttributeBindVo bindVo : result) {
//                //如果当前类目绑定的属性是继承与父类  标记为使用中
//                if("Y".equals(bindVo.getHasExtends())){
//                    bindVo.setStatus(DictStatusEnums.USE.code());
//                }
//
//            }
        }
        return result.stream().sorted(Comparator.comparing(GoodsCategoryAttributeBindVo::getOrderNum)).collect(Collectors.toList());
    }

    private LambdaQueryWrapper<GoodsCategoryAttributeBind> buildQueryWrapper(GoodsCategoryAttributeBindBo bo) {
        LambdaQueryWrapper<GoodsCategoryAttributeBind> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getCategoryId() != null, GoodsCategoryAttributeBind::getCategoryId, bo.getCategoryId());
        lqw.eq(bo.getAttributeId() != null, GoodsCategoryAttributeBind::getAttributeId, bo.getAttributeId());
        lqw.eq(bo.getOrderNum() != null, GoodsCategoryAttributeBind::getOrderNum, bo.getOrderNum());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), GoodsCategoryAttributeBind::getStatus, bo.getStatus());
        lqw.eq(BaseEntity::getTenantId, LoginHelper.getTenantId());
		lqw.eq(GoodsCategoryAttributeBind::getDeleted, DomainDeletedType.NORMAL.code());
        lqw.orderByDesc(BaseEntity::getCreateTime);
        return lqw;
    }

    /**
     * 新增商品类目属性绑定
     */
    @Override
    public Boolean insertByBo(GoodsCategoryAttributeBindBo bo) {
        GoodsCategoryAttributeBind add = BeanUtil.toBean(bo, GoodsCategoryAttributeBind.class);
        validEntityBeforeSave(add);
        add.setOrderNum(1L);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改商品类目属性绑定
     */
    @Override
    public Boolean updateByBo(GoodsCategoryAttributeBindBo bo) {
        GoodsCategoryAttributeBind update = BeanUtil.toBean(bo, GoodsCategoryAttributeBind.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(GoodsCategoryAttributeBind entity){
        if(entity.getCategoryId() == null){
            throw new ServiceException("分类id不能为空");
        }
        if(entity.getAttributeId() == null){
            throw new ServiceException("属性不能为空");
        }
        List<Long> categoryIds = iGoodsBackCategoryService.queryFullParentById(entity.getCategoryId());
        if(CollectionUtil.isNotEmpty(categoryIds)){
            List<GoodsCategoryAttributeBindVo> list = baseMapper.queryAttributeIdByCategoryId(entity.getTenantId(), categoryIds);
            if(CollectionUtil.isNotEmpty(list) &&
                    CollectionUtil.isNotEmpty(list.stream().filter(f -> f.getAttributeId().equals(entity.getAttributeId())).collect(Collectors.toList()))){
                throw new ServiceException("该属性已存在");
            }
        }
    }

    /**
     * 批量删除商品类目属性绑定
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        
        if(ids == null || ids.size() == 0) {
    		return Boolean.TRUE;
    	}
    	
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        
        //不允许做物理删除
		//return baseMapper.deleteBatchIds(ids) > 0;
        baseMapper.deleteBatchIds(ids);
        
        return Boolean.TRUE;
    }
}
