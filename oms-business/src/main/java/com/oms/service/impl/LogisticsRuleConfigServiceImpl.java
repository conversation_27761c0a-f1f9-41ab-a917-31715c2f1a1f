package com.oms.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.oms.common.core.domain.BaseEntity;
import com.oms.common.core.page.TableDataInfo;
import com.oms.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.oms.common.enums.LogisticsNodeEnum;
import com.oms.common.enums.LogisticsReplaceEnum;
import com.oms.common.helper.LoginHelper;
import com.oms.domain.vo.extend.order.LogisticsRuleNodeVo;
import com.oms.domain.vo.extend.order.LogisticsRuleVariableVo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.oms.domain.bo.LogisticsRuleConfigBo;
import com.oms.domain.vo.LogisticsRuleConfigVo;
import com.oms.domain.LogisticsRuleConfig;
import com.oms.mapper.LogisticsRuleConfigMapper;
import com.oms.service.ILogisticsRuleConfigService;

import java.util.*;

/**
 * 物流规则配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-28
 */
@RequiredArgsConstructor
@Service
public class LogisticsRuleConfigServiceImpl extends ServiceImpl<LogisticsRuleConfigMapper, LogisticsRuleConfig>  implements ILogisticsRuleConfigService {

    private final LogisticsRuleConfigMapper baseMapper;

    /**
     * 查询物流规则配置
     */
    @Override
    public LogisticsRuleConfigVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询物流规则配置列表
     */
    @Override
    public TableDataInfo<LogisticsRuleConfigVo> queryPageList(LogisticsRuleConfigBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<LogisticsRuleConfig> lqw = buildQueryWrapper(bo);
        lqw.orderByDesc(LogisticsRuleConfig::getCreateTime);
        Page<LogisticsRuleConfigVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        dataHandle(result.getRecords());
        return TableDataInfo.build(result);
    }

    private void dataHandle(List<LogisticsRuleConfigVo> records) {
        if(CollectionUtils.isEmpty(records)){
            return;
        }
        records.stream().forEach(record->{
            record.setNodeName(LogisticsNodeEnum.valueOf(record.getNodeNo()).getDesc());
        });
    }

    /**
     * 查询物流规则配置列表
     */
    @Override
    public List<LogisticsRuleConfigVo> queryList(LogisticsRuleConfigBo bo) {
        LambdaQueryWrapper<LogisticsRuleConfig> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<LogisticsRuleConfig> buildQueryWrapper(LogisticsRuleConfigBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<LogisticsRuleConfig> lqw = Wrappers.lambdaQuery();
        lqw.eq(BaseEntity::getTenantId, LoginHelper.getTenantId());
        lqw.eq(bo.getPrjId() != null, LogisticsRuleConfig::getPrjId, bo.getPrjId());
        lqw.like(StringUtils.isNotBlank(bo.getPrjName()), LogisticsRuleConfig::getPrjName, bo.getPrjName());
        lqw.eq(StringUtils.isNotBlank(bo.getNodeNo()), LogisticsRuleConfig::getNodeNo, bo.getNodeNo());
        lqw.eq(StringUtils.isNotBlank(bo.getContent()), LogisticsRuleConfig::getContent, bo.getContent());
        lqw.eq(StringUtils.isNotBlank(bo.getRandomTimeRang()), LogisticsRuleConfig::getRandomTimeRang, bo.getRandomTimeRang());
        lqw.like(StringUtils.isNotBlank(bo.getUpdateName()), LogisticsRuleConfig::getUpdateName, bo.getUpdateName());
        lqw.like(StringUtils.isNotBlank(bo.getCreateName()), LogisticsRuleConfig::getCreateName, bo.getCreateName());
        lqw.eq(bo.getDeleted() != null, LogisticsRuleConfig::getDeleted, bo.getDeleted());
        lqw.eq(bo.getDeleteTime() != null, LogisticsRuleConfig::getDeleteTime, bo.getDeleteTime());
        lqw.eq(bo.getDeleteBy() != null, LogisticsRuleConfig::getDeleteBy, bo.getDeleteBy());
        lqw.like(StringUtils.isNotBlank(bo.getDeleteName()), LogisticsRuleConfig::getDeleteName, bo.getDeleteName());
        return lqw;
    }

    /**
     * 新增物流规则配置
     */
    @Override
    public Boolean insertByBo(LogisticsRuleConfigBo bo) {
        LogisticsRuleConfig add = BeanUtil.toBean(bo, LogisticsRuleConfig.class);
        validEntityBeforeSave(add);
        add.setTenantId(1L);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改物流规则配置
     */
    @Override
    public Boolean updateByBo(LogisticsRuleConfigBo bo) {
        LogisticsRuleConfig update = BeanUtil.toBean(bo, LogisticsRuleConfig.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(LogisticsRuleConfig entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除物流规则配置
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }


    @Override
    public List<LogisticsRuleVariableVo> queryLogisticsRuleVariableList() {
        List<LogisticsRuleVariableVo> logisticsRuleVariableVoList = new ArrayList<>();
        for(LogisticsReplaceEnum logisticsReplaceEnum : LogisticsReplaceEnum.values()){
            LogisticsRuleVariableVo logisticsRuleVariableVo = new LogisticsRuleVariableVo();
            logisticsRuleVariableVo.setVName(logisticsReplaceEnum.getReplaceStr());
            logisticsRuleVariableVo.setVDesc(logisticsReplaceEnum.getDesc());
            logisticsRuleVariableVoList.add(logisticsRuleVariableVo);
        }
        return logisticsRuleVariableVoList;
    }

    @Override
    public List<LogisticsRuleNodeVo> queryLogisticsRuleNodeList() {
        List<LogisticsRuleNodeVo> logisticsRuleNodeVoList = new ArrayList<>();
        for(LogisticsNodeEnum logisticsNodeEnum : LogisticsNodeEnum.values()){
            LogisticsRuleNodeVo logisticsRuleNodeVo = new LogisticsRuleNodeVo();
            logisticsRuleNodeVo.setNodeKey(logisticsNodeEnum.getName());
            logisticsRuleNodeVo.setNodeName(logisticsNodeEnum.getDesc());
            logisticsRuleNodeVoList.add(logisticsRuleNodeVo);
        }
        return logisticsRuleNodeVoList;
    }
}
