package com.oms.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import com.oms.common.core.domain.PageQuery;
import com.oms.common.core.domain.entity.SysDictData;
import com.oms.common.core.page.TableDataInfo;
import com.oms.common.enums.*;
import com.oms.common.exception.ServiceException;
import com.oms.common.helper.LoginHelper;
import com.oms.common.utils.StreamUtils;

import com.oms.common.utils.bean.BeanValidators;
import com.oms.domain.*;
import com.oms.domain.bo.TendersInfoBo;
import com.oms.domain.bo.TendersLoseBo;
import com.oms.domain.bo.TendersWinBo;
import com.oms.domain.bo.extend.tenders.TendersChangeStatusBo;
import com.oms.domain.excel.*;
import com.oms.domain.vo.TendersDtlVo;
import com.oms.domain.vo.TendersInfoVo;
import com.oms.domain.vo.TendersLoseVo;
import com.oms.domain.vo.TendersWinVo;
import com.oms.mapper.*;
import com.oms.service.ISysDictTypeService;
import com.oms.service.ITendersInfoService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.compress.utils.Lists;
import org.elasticsearch.common.collect.ImmutableOpenMap;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.Validator;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 投标单Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-10-15
 */
@RequiredArgsConstructor
@Service
public class TendersInfoServiceImpl extends ServiceImpl<TendersInfoMapper, TendersInfo>  implements ITendersInfoService {

    private final TendersInfoMapper baseMapper;
    private final TendersContractMapper tendersContractMapper;
    private final TendersApproveMapper tendersApproveMapper;
    private final ApprovalConfigMapper approvalConfigMapper;
    private final ApprovalConfigUserMapper approvalConfigUserMapper;
    private final ApprovalConfigVariableMapper approvalConfigVariableMapper;
    private final TendersDtlMapper tendersDtlMapper;
    private final ISysDictTypeService dictTypeService;
    private final RegionMapper regionMapper;
    private final Validator validator;


    /**
     * 查询投标单
     */
    @Override
    public TendersInfoVo queryById(Long id){
        TendersInfo entity = baseMapper.selectById(id);
        if (Objects.isNull(entity)) {
            throw new ServiceException("数据不存在");
        }
        TendersInfoVo vo = BeanUtil.toBean(entity, TendersInfoVo.class);
        // 解析extraData
        analysisExtraData(entity.getExtraData(), vo);
        List<TendersDtlVo> dtlList = tendersDtlMapper.selectVoList(Wrappers.<TendersDtl>lambdaQuery().eq(TendersDtl::getTendersId, id));
        vo.setDtlList(dtlList);
        return vo;
    }

    private void  analysisExtraData(String extraData, TendersInfoVo vo) {
        if(StrUtil.isEmpty(extraData)){
            return;
        }
        Map<String, Object> extraMap = JSON.parseObject(extraData, new TypeReference<Map<String, Object>>() {});
        if (ObjectUtil.isEmpty(extraMap)){
            return ;
        }
        // 代理机构
        if(extraMap.containsKey(TendersConstantEnum.AGENT_UNIT.getKey())){
            vo.setAgentUnit(MapUtil.getStr(extraMap, TendersConstantEnum.AGENT_UNIT.getKey()));
        }
        // 代理机构联系人
        if(extraMap.containsKey(TendersConstantEnum.AGENT_RELATION_NAME.getKey())){
            vo.setAgentRelationName(MapUtil.getStr(extraMap, TendersConstantEnum.AGENT_RELATION_NAME.getKey()));
        }
        // 代理机构联系方式
        if(extraMap.containsKey(TendersConstantEnum.AGENT_RELATION_WAY.getKey())){
            vo.setAgentRelationWay(MapUtil.getStr(extraMap, TendersConstantEnum.AGENT_RELATION_WAY.getKey()));
        }
        // 招标网站地址
        if(extraMap.containsKey(TendersConstantEnum.WEBSITE_URL.getKey())){
            vo.setWebsiteUrl(MapUtil.getStr(extraMap, TendersConstantEnum.WEBSITE_URL.getKey()));
        }
        // 项目背景
        if(extraMap.containsKey(TendersConstantEnum.PROJECT_BACKDROP.getKey())){
            vo.setProjectBackdrop(MapUtil.getStr(extraMap, TendersConstantEnum.PROJECT_BACKDROP.getKey()));
        }
        // 服务期限
        if(extraMap.containsKey(TendersConstantEnum.SERVICE_PERIOD.getKey())){
            vo.setServicePeriod(MapUtil.getStr(extraMap, TendersConstantEnum.SERVICE_PERIOD.getKey()));
        }
        // 前中标人
        if(extraMap.containsKey(TendersConstantEnum.PREVIOUS_WINNING_BIDDER.getKey())){
            vo.setPreviousWinningBidder(MapUtil.getStr(extraMap, TendersConstantEnum.PREVIOUS_WINNING_BIDDER.getKey()));
        }
        // 支撑内容
        if(extraMap.containsKey(TendersConstantEnum.SUPPORTING_CONTENT.getKey())){
            vo.setSupportingContent(MapUtil.getStr(extraMap, TendersConstantEnum.SUPPORTING_CONTENT.getKey()));
        }
        // 扣分分析
        if(extraMap.containsKey(TendersConstantEnum.DEDUCTION_ANALYSIS.getKey())){
            vo.setDeductionAnalysis(MapUtil.getStr(extraMap, TendersConstantEnum.DEDUCTION_ANALYSIS.getKey()));
        }
        // 客户意向单位
        if(extraMap.containsKey(TendersConstantEnum.CUST_INTENDED_UNIT.getKey())){
            vo.setCustIntendedUnit(MapUtil.getStr(extraMap, TendersConstantEnum.CUST_INTENDED_UNIT.getKey()));
        }
        // 中标概率
        if(extraMap.containsKey(TendersConstantEnum.PROBABILITY.getKey())){
            vo.setProbability(MapUtil.getStr(extraMap, TendersConstantEnum.PROBABILITY.getKey()));
        }
        // 投标结果分析
        if(extraMap.containsKey(TendersConstantEnum.BID_CAUSE_ANALYSIS.getKey())){
            vo.setBidCauseAnalysis(MapUtil.getStr(extraMap, TendersConstantEnum.BID_CAUSE_ANALYSIS.getKey()));
        }
        // 销售名称
        if(extraMap.containsKey(TendersConstantEnum.SALES_NAME.getKey())){
            vo.setSalesName(MapUtil.getStr(extraMap, TendersConstantEnum.SALES_NAME.getKey()));
        }
        // 结果公示链接/中标链接
        if(extraMap.containsKey(TendersConstantEnum.BID_WIN_URL.getKey())){
            vo.setBidWinUrl(MapUtil.getStr(extraMap, TendersConstantEnum.BID_WIN_URL.getKey()));
        }
        // 中标信息
        if(extraMap.containsKey(TendersConstantEnum.WIN_BIDDING_LIST.getKey())){
            vo.setWinBiddingList(JSON.parseObject(MapUtil.getStr(extraMap, TendersConstantEnum.WIN_BIDDING_LIST.getKey()), new TypeReference<List<Map<String, Object>>>() {}));
        }
        // 附件List附件
        if (extraMap.containsKey(TendersConstantEnum.FILE_LIST.getKey())){
            vo.setFileList(JSON.parseObject(MapUtil.getStr(extraMap, TendersConstantEnum.FILE_LIST.getKey()), new TypeReference<List<Map<String, Object>>>() {}));
        }
        // 履约品类
        if(extraMap.containsKey(TendersConstantEnum.PERFORMANCE_CATEGORY.getKey())){
            vo.setPerformanceCategory(MapUtil.getStr(extraMap, TendersConstantEnum.PERFORMANCE_CATEGORY.getKey()));
        }
    }

    /**
     * 查询投标单列表
     */
    @Override
    public TableDataInfo<TendersInfoVo> queryPageList(TendersInfoBo bo) {
        LambdaQueryWrapper<TendersInfo> lqw = buildQueryWrapper(bo);
        lqw.orderByDesc(TendersInfo::getCreateTime);
        PageQuery pageQuery = JSONUtil.toBean(JSONUtil.toJsonStr(bo), PageQuery.class);
        Page<TendersInfoVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询投标单列表
     */
    @Override
    public List<TendersInfoVo> queryList(TendersInfoBo bo) {
        LambdaQueryWrapper<TendersInfo> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<TendersInfo> buildQueryWrapper(TendersInfoBo bo) {
        LambdaQueryWrapper<TendersInfo> lqw = Wrappers.lambdaQuery();
        /**
         * TODO 增加区域权限
         */
//        lqw.in(CollUtil.isNotEmpty(LoginHelper.getRegion()), TendersInfo::getProvinceId, LoginHelper.getRegion());
        lqw.eq(StringUtils.isNotBlank(bo.getCursorMark()), TendersInfo::getCursorMark, bo.getCursorMark());
        lqw.eq(StringUtils.isNotBlank(bo.getInfoId()), TendersInfo::getInfoId, bo.getInfoId());
        lqw.like(StringUtils.isNotBlank(bo.getInfoTitle()), TendersInfo::getInfoTitle, bo.getInfoTitle());
        lqw.eq(bo.getStatus() != null, TendersInfo::getStatus, bo.getStatus());
        lqw.in(CollUtil.isNotEmpty(bo.getStatusList()), TendersInfo::getStatus, bo.getStatusList());
        lqw.eq(bo.getInfoType() != null, TendersInfo::getInfoType, bo.getInfoType());
        lqw.eq(bo.getInfoTypeSegment() != null, TendersInfo::getInfoTypeSegment, bo.getInfoTypeSegment());

        lqw.ge(bo.getStartInfoPublishTime() != null, TendersInfo::getInfoPublishTime, bo.getStartInfoPublishTime());
        lqw.le(bo.getEndInfoPublishTime() != null, TendersInfo::getInfoPublishTime, bo.getEndInfoPublishTime());

        lqw.ge(bo.getStartTenderBeginTime() != null, TendersInfo::getTenderBeginTime, bo.getStartTenderBeginTime());
        lqw.le(bo.getEndTenderBeginTime() != null, TendersInfo::getTenderBeginTime, bo.getEndTenderBeginTime());

        lqw.ge(bo.getStartCreateTime() != null, TendersInfo::getCreateTime, bo.getStartCreateTime());
        lqw.le(bo.getEndCreateTime() != null, TendersInfo::getCreateTime, bo.getEndCreateTime());

        // 标书获取开始时间
        lqw.ge(bo.getStartBidingAcquireTime() != null, TendersInfo::getBidingAcquireTime, bo.getStartBidingAcquireTime());
        lqw.le(bo.getEndBidingAcquireTime() != null, TendersInfo::getBidingAcquireTime, bo.getEndBidingAcquireTime());

        // 标书截止时间
        lqw.ge(bo.getStartBidingEndTime() != null, TendersInfo::getBidingEndTime, bo.getStartBidingEndTime());
        lqw.le(bo.getEndBidingEndTime() != null, TendersInfo::getBidingEndTime, bo.getEndBidingEndTime());

        // 投标截止时间
        lqw.ge(bo.getStartTenderEndTime() != null, TendersInfo::getTenderEndTime, bo.getStartTenderEndTime());
        lqw.le(bo.getEndTenderEndTime() != null, TendersInfo::getTenderEndTime, bo.getEndTenderEndTime());

        // 开标时间
        lqw.ge(bo.getStartOpenBidingTime() != null, TendersInfo::getOpenBidingTime, bo.getStartOpenBidingTime());
        lqw.le(bo.getEndOpenBidingTime() != null, TendersInfo::getOpenBidingTime, bo.getEndOpenBidingTime());

        // 修改时间
        lqw.ge(bo.getStartUpdateTime() != null, TendersInfo::getUpdateTime, bo.getStartUpdateTime());
        lqw.le(bo.getEndUpdateTime() != null, TendersInfo::getUpdateTime, bo.getEndUpdateTime());

        // 结果公示日期
        lqw.ge(bo.getResultShowStartDate() != null, TendersInfo::getResultShowDate, bo.getResultShowStartDate());
        lqw.le(bo.getResultShowEndDate() != null, TendersInfo::getResultShowDate, bo.getResultShowEndDate());

        lqw.eq(bo.getProvinceId() != null, TendersInfo::getProvinceId, bo.getProvinceId());
        lqw.like(StrUtil.isNotBlank(bo.getProvinceName()), TendersInfo::getProvinceName, bo.getProvinceName());
        lqw.like(StrUtil.isNotBlank(bo.getCityName()), TendersInfo::getCityName, bo.getCityName());
        lqw.like(StrUtil.isNotBlank(bo.getDistrictName()), TendersInfo::getDistrictName, bo.getDistrictName());
        lqw.eq(StringUtils.isNotBlank(bo.getTendersNumber()), TendersInfo::getTendersNumber, bo.getTendersNumber());
        lqw.eq(bo.getBiddingType() != null, TendersInfo::getBiddingType, bo.getBiddingType());
        lqw.eq(bo.getIsElectronic() != null, TendersInfo::getIsElectronic, bo.getIsElectronic());
        lqw.eq(StringUtils.isNotBlank(bo.getTenderCompany()), TendersInfo::getTenderCompany, bo.getTenderCompany());
        lqw.like(StringUtils.isNotBlank(bo.getTenderRelationName()), TendersInfo::getTenderRelationName, bo.getTenderRelationName());
        lqw.eq(StringUtils.isNotBlank(bo.getTenderRelationWay()), TendersInfo::getTenderRelationWay, bo.getTenderRelationWay());
        lqw.eq(StringUtils.isNotBlank(bo.getWinBiddingUnit()), TendersInfo::getWinBiddingUnit, bo.getWinBiddingUnit());
        lqw.like(StringUtils.isNotBlank(bo.getWinBiddingRelationName()), TendersInfo::getWinBiddingRelationName, bo.getWinBiddingRelationName());
        lqw.eq(StringUtils.isNotBlank(bo.getWinBiddingRelationWay()), TendersInfo::getWinBiddingRelationWay, bo.getWinBiddingRelationWay());
        lqw.eq(StringUtils.isNotBlank(bo.getBidWinnerAmount()), TendersInfo::getBidWinnerAmount, bo.getBidWinnerAmount());
        lqw.eq(StringUtils.isNotBlank(bo.getBidBudget()), TendersInfo::getBidBudget, bo.getBidBudget());
        lqw.eq(StringUtils.isNotBlank(bo.getFundsSource()), TendersInfo::getFundsSource, bo.getFundsSource());
        lqw.eq(StringUtils.isNotBlank(bo.getBidEvaluationExpert()), TendersInfo::getBidEvaluationExpert, bo.getBidEvaluationExpert());
        lqw.eq(StringUtils.isNotBlank(bo.getBelongingArea()), TendersInfo::getBelongingArea, bo.getBelongingArea());
        lqw.eq(StringUtils.isNotBlank(bo.getMaterialCategory()), TendersInfo::getMaterialCategory, bo.getMaterialCategory());
        lqw.eq(StrUtil.isNotEmpty(bo.getIndustry()), TendersInfo::getIndustry, bo.getIndustry());
        lqw.eq(StringUtils.isNotBlank(bo.getSupportDepartment()), TendersInfo::getSupportDepartment, bo.getSupportDepartment());
        lqw.eq(bo.getWinBidStatus() != null, TendersInfo::getWinBidStatus, bo.getWinBidStatus());
        lqw.eq(bo.getProjectLevel() != null, TendersInfo::getProjectLevel, bo.getProjectLevel());
        return lqw;
    }

    /**
     * 新增投标单
     */
    @Override
    @Transactional
    public Boolean insertByBo(TendersInfoBo bo) {
        TendersInfo add = BeanUtil.toBean(bo, TendersInfo.class);
        // 扩展字段转换
        convertExtraData(add, bo);
        validEntityBeforeSave(add);
        add.setSourceType(TendersSourceTypeEnum.BID.getValue());
        add.setStatus(TendersStatusEnum.NEW.getValue());
        add.created();
        baseMapper.insert(add);
        if(CollUtil.isEmpty(bo.getDtlList())){
            return true;
        }
        // 录入明细
        List<TendersDtl> tendersDtlList = BeanUtil.copyToList(bo.getDtlList(), TendersDtl.class);
        tendersDtlList.forEach(item -> {
            item.setTendersId(add.getId());
            item.created();
        });
        tendersDtlMapper.insertBatch(tendersDtlList);
        return true;
    }

    private void convertExtraData(TendersInfo add, TendersInfoBo bo) {
        Map<String, Object> extraMap = (Map<String, Object>) Optional.ofNullable(
                JSON.parseObject(add.getExtraData(), new TypeReference<Map<String, Object>>(){}))
                .orElse(new HashMap<>());
        // 代理机构
        extraMap.put(TendersConstantEnum.AGENT_UNIT.getKey(), bo.getAgentUnit());
        // 代理机构联系人
        extraMap.put(TendersConstantEnum.AGENT_RELATION_NAME.getKey(), bo.getAgentRelationName());
        // 代理机构联系方式
        extraMap.put(TendersConstantEnum.AGENT_RELATION_WAY.getKey(), bo.getAgentRelationWay());
        // 招标网站地址
        extraMap.put(TendersConstantEnum.WEBSITE_URL.getKey(), bo.getWebsiteUrl());
        // 项目背景
        extraMap.put(TendersConstantEnum.PROJECT_BACKDROP.getKey(), bo.getProjectBackdrop());
        // 前中标人
        extraMap.put(TendersConstantEnum.PREVIOUS_WINNING_BIDDER.getKey(), bo.getPreviousWinningBidder());
        // 支撑内容
        extraMap.put(TendersConstantEnum.SUPPORTING_CONTENT.getKey(), bo.getSupportingContent());
        // 中标概率
        extraMap.put(TendersConstantEnum.PROBABILITY.getKey(), bo.getProbability());
        // 客户意向单位
        extraMap.put(TendersConstantEnum.CUST_INTENDED_UNIT.getKey(), bo.getCustIntendedUnit());
        // 服务期限
        extraMap.put(TendersConstantEnum.SERVICE_PERIOD.getKey(), bo.getServicePeriod());
        // 扣分分析
        extraMap.put(TendersConstantEnum.DEDUCTION_ANALYSIS.getKey(), bo.getDeductionAnalysis());
        // 中标信息List json 数据存储 [{"winBiddingUnit":"中标单位", "winBiddingAmount":"中标金额"}]
        extraMap.put(TendersConstantEnum.WIN_BIDDING_LIST.getKey(), bo.getWinBiddingList());
        // 附件
        extraMap.put(TendersConstantEnum.FILE_LIST.getKey(), bo.getFileList());
        add.setExtraData(JSON.toJSONString(extraMap));
    }

    /**
     * 修改投标单
     */
    @Override
    @Transactional
    public Boolean updateByBo(TendersInfoBo bo) {
        TendersInfo update = BeanUtil.toBean(bo, TendersInfo.class);
        validEntityBeforeSave(update);
        //
        convertExtraData(update, bo);
        update.updated();
        baseMapper.updateById(update);
        if(CollUtil.isEmpty(bo.getDtlList())){
            tendersDtlMapper.update(new LambdaUpdateWrapper<>(TendersDtl.class)
                    .set(TendersDtl::getDeleted, true)
                    .set(TendersDtl::getDeleteBy, LoginHelper.getUserId())
                    .set(TendersDtl::getDeleteTime, new Date())
                    .set(TendersDtl::getDeleteName, LoginHelper.getUsername())
                    .eq(TendersDtl::getTendersId, bo.getId())
            );
            return true;
        }
        handleUpdateDtl(bo);
        return true;
    }

    private void handleUpdateDtl(TendersInfoBo bo) {
        // 处理明细. 根据id 判断新增，删除, 修改的数据
        List<TendersDtl> tendersDtlList = BeanUtil.copyToList(bo.getDtlList(), TendersDtl.class);
        List<Long> dtlIds = StreamUtils.toList(tendersDtlList, TendersDtl::getId);
        List<TendersDtl> oldList = tendersDtlMapper.selectList(new LambdaQueryWrapper<TendersDtl>().in(TendersDtl::getTendersId, bo.getId()));
        List<Long> oldIds = oldList.stream().map(TendersDtl::getId).collect(Collectors.toList());
        // 新增
        List<TendersDtl> addList = StreamUtils.filter(tendersDtlList, item -> item.getId() == null);
        // 修改
        List<TendersDtl> updateList = StreamUtils.filter(tendersDtlList, item -> oldIds.contains(item.getId()));
        // 删除
        List<TendersDtl> deleteList = StreamUtils.filter(oldList, item -> !dtlIds.contains(item.getId()));
//        List<TendersDtl> deleteList = StreamUtils.filter(updateList, item -> !oldIds.contains(item.getId()));

        if(CollUtil.isNotEmpty(addList)){
            addList.forEach(item -> {
                item.setTendersId(bo.getId());
                item.created();
            });
            tendersDtlMapper.insertBatch(addList);
        }
        if(CollUtil.isNotEmpty(updateList)){
            updateList.forEach(item -> item.updated());
            tendersDtlMapper.updateBatchById(updateList);
        }
        if(CollUtil.isNotEmpty(deleteList)){
            tendersDtlMapper.update(new LambdaUpdateWrapper<>(TendersDtl.class)
                .set(TendersDtl::getDeleted, true)
                .set(TendersDtl::getDeleteBy, LoginHelper.getUserId())
                .set(TendersDtl::getDeleteTime, new Date())
            .   set(TendersDtl::getDeleteName, LoginHelper.getUsername())
                .in(TendersDtl::getId, StreamUtils.toList(deleteList, TendersDtl::getId))
            );
        }
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(TendersInfo entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除投标单
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }


    @Override
    @Transactional
    public Boolean submit(Long id, String remark) {
        TendersInfo tendersInfo = this.getById(id);

        validateSubmit(tendersInfo);

        List<ApprovalConfig> approvalConfigs = getApprovalConfigs();
        if(CollUtil.isEmpty(approvalConfigs)){
            return updateToStatus(id, remark, TendersStatusEnum.AUDIT_SUCCESS.getValue());
        }

        Map<Long, List<ApprovalConfigVariable>> variableMap = getVariableMap(approvalConfigs);
        Long matchedApprovalConfigId = findMatchedApprovalConfigId(variableMap, tendersInfo);

        // 无匹配的审批配置
        if(matchedApprovalConfigId == null){
            return updateToStatus(id, remark, TendersStatusEnum.AUDIT_SUCCESS.getValue());
        }

        List<ApprovalConfigUser> configUserList = approvalConfigUserMapper.selectList(new LambdaQueryWrapper<ApprovalConfigUser>()
                .eq(ApprovalConfigUser::getApprovalId, matchedApprovalConfigId).orderByAsc(ApprovalConfigUser::getLevel));
        if (CollUtil.isEmpty(configUserList)){
            throw new ServiceException("审批用户不存在");
        }
        List<TendersApprove> approveList = createApproveList(tendersInfo.getId(), configUserList);
        tendersApproveMapper.insertBatch(approveList);
        return this.lambdaUpdate().set(TendersInfo::getStatus, TendersStatusEnum.WAIT_AUDIT.getValue())
                .set(TendersInfo::getIsNeedApprove, YesNoEnum.YES.getValue())
                .set(TendersInfo::getNextApproveUserId, approveList.get(0).getApproveId())
                .set(TendersInfo::getRemark, remark)
                .set(TendersInfo::getUpdateBy, LoginHelper.getUserId())
                .set(TendersInfo::getUpdateTime, new Date())
                .set(TendersInfo::getUpdateName, LoginHelper.getUsername())
                .eq(TendersInfo::getId, id).update();
    }

    private List<TendersApprove> createApproveList(Long id, List<ApprovalConfigUser> configUserList) {
        return IntStream.range(0, configUserList.size())
                .mapToObj(index -> {
                    ApprovalConfigUser configUser = configUserList.get(index);
                    TendersApprove approve = new TendersApprove();
                    approve.setInfoId(id);
                    approve.setApproveId(configUser.getApproverId());
                    approve.setApproveName(configUser.getApproverName());
                    approve.setAuditStatus(AuditStatusEnum.WAIT_FOR_VIEW.getValue());
                    approve.created();
                    approve.setSort(index + 1); // 如果需要从1开始自增，则使用 index + 1
                    return approve;
                }).collect(Collectors.toList());
    }

    private Long findMatchedApprovalConfigId(Map<Long, List<ApprovalConfigVariable>> variableMap, TendersInfo tendersInfo) {
        for (Map.Entry<Long, List<ApprovalConfigVariable>> entry : variableMap.entrySet()) {            // 匹配变量类型
            Boolean projectLevelFlag = false ;
            Boolean provinceFlag =  false ;

            for (ApprovalConfigVariable variable : entry.getValue()) {
                TendersVariableTypeEnum variableTypeEnum = TendersVariableTypeEnum.fromValue(variable.getVariableType());
                switch (variableTypeEnum) {
                    case PROVINCE:
                        provinceFlag = matchingProvince(variable, tendersInfo.getProvinceId());
                        break;
                    case PROJECT_LEVEL:
                        projectLevelFlag = matchingProjectLevel(variable, tendersInfo.getProjectLevel());
                        break;
                    default:
                        throw new ServiceException("暂不支持该类型审批");
                }
            }
            if(projectLevelFlag && provinceFlag){
                return entry.getKey();
            }
        }
        return null;
    }

    private Map<Long, List<ApprovalConfigVariable>> getVariableMap(List<ApprovalConfig> approvalConfigs) {
        List<Long> configIds = StreamUtils.toList(approvalConfigs, ApprovalConfig::getId);
        List<ApprovalConfigVariable> variableList = approvalConfigVariableMapper.selectList(new LambdaQueryWrapper<ApprovalConfigVariable>()
                .in(ApprovalConfigVariable::getApprovalId, configIds)
        );
        Map<Long, List<ApprovalConfigVariable>> variableMap = StreamUtils.group(variableList, ApprovalConfigVariable::getApprovalId);
        return variableMap;
    }

    private Boolean updateToStatus(Long id, String remark, Integer status) {
        return this.lambdaUpdate().set(TendersInfo::getStatus, status)
                .set(TendersInfo::getIsNeedApprove, YesNoEnum.NO.getValue())
                .set(StrUtil.isNotEmpty(remark), TendersInfo::getRemark, remark)
                .set(TendersInfo::getUpdateBy, LoginHelper.getUserId())
                .set(TendersInfo::getUpdateTime, new Date())
                .set(TendersInfo::getUpdateName, LoginHelper.getUsername())
                .eq(TendersInfo::getId, id).update();
    }

    private List<ApprovalConfig> getApprovalConfigs() {
        return approvalConfigMapper.selectList(new LambdaQueryWrapper<ApprovalConfig>()
                .eq(ApprovalConfig::getStatus, StatusEnum.NOR.getValue())
                .orderByDesc(ApprovalConfig::getId));
    }

    private static void validateSubmit(TendersInfo tendersInfo) {
        if (ObjectUtil.isNull(tendersInfo)) {
            throw new ServiceException("数据不存在");
        }
        if(!TendersStatusEnum.NEW.getValue().equals(tendersInfo.getStatus())){
            throw new ServiceException("非新建状态不能提交");
        }
        if(tendersInfo.getProjectLevel() == null){
            throw new ServiceException("项目等级不存在");
        }
        if(tendersInfo.getProvinceId() == null){
            throw new ServiceException("省份不存在");
        }
    }

    private Boolean matchingProvince(ApprovalConfigVariable variable, Long provinceId) {
        TendersRequirementTypeEnum requirementTypeEnum = TendersRequirementTypeEnum.fromValue(variable.getRequirementType());
        switch (requirementTypeEnum) {
            case EQ:
                return variable.getValue().contains(provinceId.toString());
        }
        return false ;
    }

    private Boolean matchingRequirementType(ApprovalConfigVariable variable, BigDecimal bidBudget) {
        TendersRequirementTypeEnum requirementTypeEnum = TendersRequirementTypeEnum.fromValue(variable.getRequirementType());
        switch (requirementTypeEnum) {
            case EQ:
                return bidBudget.compareTo(new BigDecimal(variable.getValue())) == 0;
            case GT:
                return bidBudget.compareTo(new BigDecimal(variable.getValue())) > 0;
            case GE:
                return bidBudget.compareTo(new BigDecimal(variable.getValue()))>= 0;
            case LE:
                return bidBudget.compareTo(new BigDecimal(variable.getValue())) <= 0;
        }
        return false;
    }

    private Boolean matchingProjectLevel(ApprovalConfigVariable variable, Integer projectLevel) {
        TendersRequirementTypeEnum requirementTypeEnum = TendersRequirementTypeEnum.fromValue(variable.getRequirementType());
        switch (requirementTypeEnum) {
            case EQ:
                return projectLevel == Integer.parseInt(variable.getValue());
            case GT:
                return projectLevel > Integer.parseInt(variable.getValue());
            case GE:
                return projectLevel >= Integer.parseInt(variable.getValue());
            case LE:
                return projectLevel <= Integer.parseInt(variable.getValue());
        }
        return false;
    }

    @Override
    public Boolean changeStatus(TendersChangeStatusBo bo) {
        // 数据不存在
        TendersInfo tendersInfo = getById(bo.getId());
        if (ObjectUtil.isNull(tendersInfo)) {
            throw new ServiceException("数据不存在");
        }
        checkStatusChange(tendersInfo.getStatus(), bo.getStatus());
        Map<String, Object> extraMap = (Map<String, Object>) Optional.ofNullable(JSON.parseObject(tendersInfo.getExtraData(), new TypeReference<Map<String, Object>>() {}))
                .orElse(Maps.newHashMap());
        extraMap.put(TendersConstantEnum.BID_WIN_URL.getKey(), bo.getBidWinUrl());
        extraMap.put(TendersConstantEnum.BID_CAUSE_ANALYSIS.getKey(), bo.getBidCauseAnalysis());
        return this.lambdaUpdate()
                .set(TendersInfo::getExtraData, JSON.toJSONString(extraMap))
                .set(TendersInfo::getStatus, bo.getStatus())
                .set(TendersInfo::getResultShowDate, bo.getResultShowDate())
                .set(TendersInfo::getRemark, bo.getRemark())
                .set(TendersInfo::getUpdateBy, LoginHelper.getUserId())
                .set(TendersInfo::getUpdateTime, new Date())
                .set(TendersInfo::getUpdateName, LoginHelper.getUsername())
                .eq(TendersInfo::getId, bo.getId())
                .update();
    }

    /**
     * 新建(允许修改为 未参与;已提报，待审批;已提报，审批同意;已提报，审批驳回;正在参与;结果未出;中标;落标;弃标;流标)
     * 未参与(不允许更改)
     * 已提报，待审批(允许修改为已提报，审批同意;已提报，审批驳回)
     * 已提报，审批同意(允许修改为未参与正在参与,结果未出,中标,落标,弃标,流标)
     * 已提报，审批驳回(允许更改为未参与)
     * 正在参与(允许修改为结果未出,中标,落标,弃标,流标)
     * 结果未出 (允许修改为中标,落标,弃标,流标)
     * 中标(不允许更改)
     * 落标(不允许更改)
     * 弃标(不允许更改)
     * 流标(不允许更改)
     * @param dbStatus
     * @param changeStatus
     */
    private void checkStatusChange(Integer dbStatus, Integer changeStatus) {
        TendersStatusEnum dbStatusEnum = TendersStatusEnum.fromValue(dbStatus);
        switch (dbStatusEnum) {
            case NEW:
                throw new ServiceException("新建状态不允许更改");
            case NOT_TAKE_PART:
                throw new ServiceException("未参与状态不允许更改");
            case WAIT_AUDIT:
                if (!TendersStatusEnum.AUDIT_SUCCESS.getValue().equals(changeStatus) && !TendersStatusEnum.AUDIT_REJECT.getValue().equals(changeStatus)) {
                    throw new ServiceException("已提报，待审批状态只能修改为已提报，审批同意或已提报，审批驳回");
                }
                return ;
            case AUDIT_SUCCESS:
                if (!TendersStatusEnum.NOT_TAKE_PART.getValue().equals(changeStatus) && !TendersStatusEnum.TAKE_PART.getValue().equals(changeStatus)
                        && !TendersStatusEnum.NOT_RESULTS.getValue().equals(changeStatus) && !TendersStatusEnum.WINNING_BID.getValue().equals(changeStatus)
                        && !TendersStatusEnum.LOSING_BID.getValue().equals(changeStatus) && !TendersStatusEnum.FAILED_BID.getValue().equals(changeStatus)
                        && !TendersStatusEnum.ABANDONING_BID.getValue().equals(changeStatus)) {
                    throw new ServiceException("已提报，审批同意状态只能修改为未参与,正在参与,结果未出,中标,落标,弃标,流标");
                }
                return ;
            case AUDIT_REJECT:
                if (!TendersStatusEnum.NOT_TAKE_PART.getValue().equals(changeStatus)) {
                    throw new ServiceException("已提报，审批驳回状态只能修改为未参与");
                }
                return ;
            case TAKE_PART:
                if (!TendersStatusEnum.NOT_RESULTS.getValue().equals(changeStatus) && !TendersStatusEnum.WINNING_BID.getValue().equals(changeStatus)
                        && !TendersStatusEnum.LOSING_BID.getValue().equals(changeStatus) && !TendersStatusEnum.FAILED_BID.getValue().equals(changeStatus)) {
                    throw new ServiceException("正在参与状态只能修改为结果未出,中标,落标,弃标,流标");
                }
                return ;
            case NOT_RESULTS:
                if (!TendersStatusEnum.WINNING_BID.getValue().equals(changeStatus) && !TendersStatusEnum.LOSING_BID.getValue().equals(changeStatus)
                        && !TendersStatusEnum.FAILED_BID.getValue().equals(changeStatus) && !TendersStatusEnum.ABANDONING_BID.getValue().equals(changeStatus)) {
                    throw new ServiceException("结果未出状态只能修改为中标,落标,弃标,流标");
                }
                return ;
            case WINNING_BID:
            case LOSING_BID:
            case FAILED_BID:
            case ABANDONING_BID:
                throw new ServiceException("该状态不允许更改");
            default:
                throw new ServiceException("暂不支持该状态");
        }
    }

    @Override
    @Transactional
    public Boolean audit(Long id, Integer status, String remark) {
        // 数据不存在
        TendersInfo tendersInfo = this.getById(id);
        if (ObjectUtil.isNull(tendersInfo)) {
            throw new ServiceException("数据不存在");
        }
        if (!TendersStatusEnum.WAIT_AUDIT.getValue().equals(tendersInfo.getStatus())) {
            throw new ServiceException("不属于审批中无法操作, 请刷新后再试");
        }
        // 获取用户审批流
        List<TendersApprove> approveList = tendersApproveMapper.selectList(new LambdaQueryWrapper<TendersApprove>().eq(TendersApprove::getInfoId, id));
        if (CollUtil.isEmpty(approveList)) {
            throw new ServiceException("流程异常, 审批数据不存在");
        }
        TendersApprove tendersApprove = approveList.stream().filter(approve -> approve.getApproveId().equals(LoginHelper.getUserId())).findFirst().orElse(null);
        if (ObjectUtil.isNull(tendersApprove)){
            throw new ServiceException("您不是当前审批人, 无法操作");
        }
        // 审批驳回
        if (TendersStatusEnum.AUDIT_REJECT.getValue().equals(status)) {
            updateTendersApprove(AuditStatusEnum.NOT_PASS.getValue(), tendersApprove.getId(), remark);
            return updateTendersInfo(id, status, remark);
        }

        // 下级审批人
        TendersApprove nextApprove = approveList.stream().filter(approve -> approve.getSort() == tendersApprove.getSort()+1).findFirst().orElse(null);
        if (ObjectUtil.isNull(nextApprove)){
            updateTendersApprove(AuditStatusEnum.PASS.getValue(), tendersApprove.getId(), remark);
            return updateTendersInfo(id, status, remark);
        }
        // 存在下级审批人
        updateTendersApprove(AuditStatusEnum.PASS.getValue(), tendersApprove.getId(), remark);
        /**
         * TODO 发送邮件
         */
        return this.lambdaUpdate()
                .set(TendersInfo::getNextApproveUserId, nextApprove.getApproveId())
                .set(TendersInfo::getUpdateBy, LoginHelper.getUserId())
                .set(TendersInfo::getUpdateName, LoginHelper.getUsername())
                .set(TendersInfo::getUpdateTime, new Date())
                .eq(TendersInfo::getId, id)
                .update();
    }

    private void updateTendersApprove(Integer auditStatus, Long id, String remark) {
        tendersApproveMapper.update(new LambdaUpdateWrapper<>(TendersApprove.class)
                .set(TendersApprove::getAuditMsg, remark)
                .set(TendersApprove::getAuditStatus, auditStatus)
                .set(TendersApprove::getUpdateBy, LoginHelper.getUserId())
                .set(TendersApprove::getUpdateTime, new Date()) // 使用LocalDateTime
                .eq(TendersApprove::getId, id));
    }

    private boolean updateTendersInfo(Long id, Integer status, String remark) {
        return this.lambdaUpdate()
                .set(TendersInfo::getStatus, status)
                .set(StrUtil.isNotEmpty(remark), TendersInfo::getRemark, remark)
                .set(TendersInfo::getUpdateBy, LoginHelper.getUserId())
                .set(TendersInfo::getUpdateName, LoginHelper.getUsername())
                .set(TendersInfo::getUpdateTime, new Date()) // 使用LocalDateTime
                .eq(TendersInfo::getId, id)
                .update();
    }

    @Override
    public Boolean addWin(TendersWinBo bo) {
        TendersInfo add = BeanUtil.toBean(bo, TendersInfo.class);
        Map<String, Object> extraMap = ImmutableMap.of(
                TendersConstantEnum.PERFORMANCE_CATEGORY.getKey(), bo.getPerformanceCategory(),
                TendersConstantEnum.SERVICE_PERIOD.getKey(), bo.getServicePeriod(),
                TendersConstantEnum.BID_WIN_URL.getKey(), bo.getBidWinUrl());
        add.setExtraData(JSON.toJSONString(extraMap));
        add.setStatus(TendersStatusEnum.WINNING_BID.getValue());
        add.setSourceType(TendersSourceTypeEnum.OFFLINE.getValue());
        add.created();
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    @Override
    public Boolean editWin(TendersWinBo bo) {
        TendersInfo tenders = baseMapper.selectById(bo.getId());
        if (ObjectUtil.isNull(tenders)) {
            throw new ServiceException("数据不存在");
        }
        TendersInfo update = BeanUtil.toBean(bo, TendersInfo.class);
        Map<String, Object> extraMap = JSON.parseObject(tenders.getExtraData(), new TypeReference<Map<String, Object>>() {});
        extraMap.put(TendersConstantEnum.PERFORMANCE_CATEGORY.getKey(), bo.getPerformanceCategory());
        extraMap.put(TendersConstantEnum.SERVICE_PERIOD.getKey(), bo.getServicePeriod());
        extraMap.put(TendersConstantEnum.BID_WIN_URL.getKey(), bo.getBidWinUrl());
        update.setExtraData(JSON.toJSONString(extraMap));
        update.updated();
        return baseMapper.updateById(update) > 0;
    }

    @Override
    public TendersWinVo getWinBidById(Long id) {
        TendersInfo tendersVo = baseMapper.selectById(id);
        if (Objects.isNull(tendersVo)){
            throw new ServiceException("数据不存在");
        }
        TendersWinVo tendersWinVo = BeanUtil.copyProperties(tendersVo, TendersWinVo.class);
        if (ObjectUtil.isNotNull(tendersVo.getExtraData())){
            Map<String, Object> extraMap = JSON.parseObject(tendersVo.getExtraData(), Map.class);
            tendersWinVo.setPerformanceCategory(MapUtil.getStr(extraMap, TendersConstantEnum.PERFORMANCE_CATEGORY.getKey()));
            tendersWinVo.setServicePeriod(MapUtil.getStr(extraMap, TendersConstantEnum.SERVICE_PERIOD.getKey()));
            tendersWinVo.setBidWinUrl(MapUtil.getStr(extraMap, TendersConstantEnum.BID_WIN_URL.getKey()));
        }
        return tendersWinVo;
    }

    @Override
    public Boolean deleteByIds(List<Long> ids, boolean b) {
        return this.lambdaUpdate().set(TendersInfo::getDeleted, YesNoEnum.YES.getValue())
                .set(TendersInfo::getUpdateBy, LoginHelper.getUserId())
                .set(TendersInfo::getUpdateTime, new Date())
                .set(TendersInfo::getUpdateName, LoginHelper.getUsername())
                .in(TendersInfo::getId, ids).update();
    }

    @Override
    public Boolean addLose(TendersLoseBo bo) {
        TendersInfo add = BeanUtil.toBean(bo, TendersInfo.class);
        Map<String, Object> extraMap = Maps.newHashMap();
        extraMap.put(TendersConstantEnum.SERVICE_PERIOD.getKey(), bo.getServicePeriod());
        extraMap.put(TendersConstantEnum.BID_WIN_URL.getKey(), bo.getBidWinUrl());
        extraMap.put(TendersConstantEnum.LOSE_BID_FILE_LIST.getKey(), bo.getLoseBidFileList());
        extraMap.put(TendersConstantEnum.LOSE_BID_REMARK.getKey(), bo.getLoseBidRemark());
        extraMap.put(TendersConstantEnum.BID_CAUSE_ANALYSIS.getKey(), bo.getBidCauseAnalysis());
        extraMap.put(TendersConstantEnum.SALES_NAME.getKey(), bo.getSalesName());
        extraMap.put(TendersConstantEnum.WEBSITE_URL.getKey(), bo.getWebsiteUrl());
        add.setExtraData(JSON.toJSONString(extraMap));
        add.setStatus(TendersStatusEnum.LOSING_BID.getValue());
        add.setSourceType(TendersSourceTypeEnum.OFFLINE.getValue());
        add.created();
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    @Override
    public Boolean editLose(TendersLoseBo bo) {
        TendersInfo tenders = baseMapper.selectById(bo.getId());
        if (ObjectUtil.isNull(tenders)) {
            throw new ServiceException("数据不存在");
        }
        TendersInfo update = BeanUtil.toBean(bo, TendersInfo.class);
        Map<String, Object> extraMap = JSON.parseObject(tenders.getExtraData(), new TypeReference<Map<String, Object>>() {});
        extraMap.put(TendersConstantEnum.SERVICE_PERIOD.getKey(), bo.getServicePeriod());
        extraMap.put(TendersConstantEnum.BID_WIN_URL.getKey(), bo.getBidWinUrl());
        extraMap.put(TendersConstantEnum.LOSE_BID_FILE_LIST.getKey(), bo.getLoseBidFileList());
        extraMap.put(TendersConstantEnum.LOSE_BID_REMARK.getKey(), bo.getLoseBidRemark());
        extraMap.put(TendersConstantEnum.BID_CAUSE_ANALYSIS.getKey(), bo.getBidCauseAnalysis());
        extraMap.put(TendersConstantEnum.SALES_NAME.getKey(), bo.getSalesName());
        extraMap.put(TendersConstantEnum.WEBSITE_URL.getKey(), bo.getWebsiteUrl());
        update.setExtraData(JSON.toJSONString(extraMap));
        update.updated();
        return baseMapper.updateById(update) > 0;
    }

    @Override
    public TendersLoseVo getLoseBidById(Long id) {
        TendersInfo tendersVo = baseMapper.selectById(id);
        if (Objects.isNull(tendersVo)){
            throw new ServiceException("数据不存在");
        }
        TendersLoseVo tendersLoseVo = BeanUtil.copyProperties(tendersVo, TendersLoseVo.class);
        if (ObjectUtil.isNotNull(tendersVo.getExtraData())){
            Map<String, Object> extraMap = JSON.parseObject(tendersVo.getExtraData(), Map.class);
            tendersLoseVo.setServicePeriod(MapUtil.getStr(extraMap, TendersConstantEnum.SERVICE_PERIOD.getKey()));
            tendersLoseVo.setBidWinUrl(MapUtil.getStr(extraMap, TendersConstantEnum.BID_WIN_URL.getKey()));
            tendersLoseVo.setLoseBidFileList(JSON.parseObject(
                    MapUtil.getStr(extraMap, TendersConstantEnum.LOSE_BID_FILE_LIST.getKey()),
                    new TypeReference<List<Map<String, Object>>>() {}));
            tendersLoseVo.setLoseBidRemark(MapUtil.getStr(extraMap, TendersConstantEnum.LOSE_BID_REMARK.getKey()));
            tendersLoseVo.setBidCauseAnalysis(MapUtil.getStr(extraMap, TendersConstantEnum.BID_CAUSE_ANALYSIS.getKey()));
            tendersLoseVo.setSalesName(MapUtil.getStr(extraMap, TendersConstantEnum.SALES_NAME.getKey()));
            tendersLoseVo.setWebsiteUrl(MapUtil.getStr(extraMap, TendersConstantEnum.WEBSITE_URL.getKey()));
        }
        return tendersLoseVo;
    }

    @Override
    public List<TendersInfoExport> headerExport(TendersInfoBo bo) {
        LambdaQueryWrapper<TendersInfo> lqw = buildQueryWrapper(bo);
        List<TendersInfo> list = baseMapper.selectList(lqw);
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<TendersInfoExport> exportList = Lists.newArrayList();
        for (TendersInfo item : list) {
            TendersInfoExport export = BeanUtil.copyProperties(item, TendersInfoExport.class);
            export.setBelongingArea(dictTypeConvert(item.getBelongingArea(), DictTypeEnum.TENDERS_BELONGING_AREA.getValue()));
            export.setProjectLevel(dictTypeConvert(String.valueOf(item.getProjectLevel()), DictTypeEnum.TENDERS_PROJECT_LEVEL.getValue()));
            export.setIndustry(dictTypeConvert(item.getIndustry(), DictTypeEnum.TENDERS_INDUSTRY.getValue()));
            export.setMaterialCategory(dictTypeConvert(item.getMaterialCategory(), DictTypeEnum.TENDERS_IMATERIAL_CATEGORY.getValue()));
            export.setBiddingType(dictTypeConvert(String.valueOf(item.getBiddingType()), DictTypeEnum.BIDDING_TYPE.getValue()));
            export.setInfoType(dictTypeConvert(String.valueOf(item.getInfoType()), DictTypeEnum.TENDERS_INFO_TYPE.getValue()));
            export.setInfoTypeSegment(dictTypeConvert(String.valueOf(item.getInfoTypeSegment()), DictTypeEnum.TENDERS_INFO_TYPE_SEGMENT.getValue()));
            export.setStatus(dictTypeConvert(String.valueOf(item.getStatus()), DictTypeEnum.TENDERS_STATUS.getValue()));
            //
            String extraData = item.getExtraData();
            if(StrUtil.isEmpty(extraData)){
                continue;
            }
            Map<String, Object> extraMap = JSON.parseObject(extraData, new TypeReference<Map<String, Object>>() {});
            if (ObjectUtil.isEmpty(extraMap)){
                continue ;
            }
            // 代理机构
            if(extraMap.containsKey(TendersConstantEnum.AGENT_UNIT.getKey())){
                export.setAgentUnit(MapUtil.getStr(extraMap, TendersConstantEnum.AGENT_UNIT.getKey()));
            }
            // 代理机构联系人
            if(extraMap.containsKey(TendersConstantEnum.AGENT_RELATION_NAME.getKey())){
                export.setAgentRelationName(MapUtil.getStr(extraMap, TendersConstantEnum.AGENT_RELATION_NAME.getKey()));
            }
            // 代理机构联系方式
            if(extraMap.containsKey(TendersConstantEnum.AGENT_RELATION_WAY.getKey())){
                export.setAgentRelationWay(MapUtil.getStr(extraMap, TendersConstantEnum.AGENT_RELATION_WAY.getKey()));
            }
            // 招标网站地址
            if(extraMap.containsKey(TendersConstantEnum.WEBSITE_URL.getKey())){
                export.setWebsiteUrl(MapUtil.getStr(extraMap, TendersConstantEnum.WEBSITE_URL.getKey()));
            }
            // 项目背景
            if(extraMap.containsKey(TendersConstantEnum.PROJECT_BACKDROP.getKey())){
                export.setProjectBackdrop(MapUtil.getStr(extraMap, TendersConstantEnum.PROJECT_BACKDROP.getKey()));
            }
            // 前中标人
            if(extraMap.containsKey(TendersConstantEnum.PREVIOUS_WINNING_BIDDER.getKey())){
                export.setPreviousWinningBidder(MapUtil.getStr(extraMap, TendersConstantEnum.PREVIOUS_WINNING_BIDDER.getKey()));
            }
            // 支撑内容
            if(extraMap.containsKey(TendersConstantEnum.SUPPORTING_CONTENT.getKey())){
                export.setSupportingContent(MapUtil.getStr(extraMap, TendersConstantEnum.SUPPORTING_CONTENT.getKey()));
            }
            // 扣分分析
            if(extraMap.containsKey(TendersConstantEnum.DEDUCTION_ANALYSIS.getKey())){
                export.setDeductionAnalysis(MapUtil.getStr(extraMap, TendersConstantEnum.DEDUCTION_ANALYSIS.getKey()));
            }
            // 客户意向单位
            if(extraMap.containsKey(TendersConstantEnum.CUST_INTENDED_UNIT.getKey())){
                export.setCustIntendedUnit(MapUtil.getStr(extraMap, TendersConstantEnum.CUST_INTENDED_UNIT.getKey()));
            }
            // 中标概率
            if(extraMap.containsKey(TendersConstantEnum.PROBABILITY.getKey())){
                export.setProbability(MapUtil.getStr(extraMap, TendersConstantEnum.PROBABILITY.getKey()));
            }
            // 中标信息
            if(extraMap.containsKey(TendersConstantEnum.WIN_BIDDING_LIST.getKey())){
                export.setWinBiddingList(MapUtil.getStr(extraMap, TendersConstantEnum.WIN_BIDDING_LIST.getKey()));
            }
            // 中标数量
            if(extraMap.containsKey(TendersConstantEnum.COMPANY_NUMBER.getKey())){
                export.setCompanyNumber(MapUtil.getStr(extraMap, TendersConstantEnum.COMPANY_NUMBER.getKey()));
            }
            // 服务期限
            if(extraMap.containsKey(TendersConstantEnum.SERVICE_PERIOD.getKey())){
                export.setServicePeriod(MapUtil.getStr(extraMap, TendersConstantEnum.SERVICE_PERIOD.getKey()));
            }


            exportList.add(export);
        }
        return exportList;
    }

    private String dictTypeConvert(String value, String dictType) {
        if(StrUtil.isEmpty(value)){
            return null ;
        }
        List<SysDictData> dictList = dictTypeService.selectDictDataByType(dictType);
        if(CollUtil.isEmpty(dictList)){
            return value ;
        }
        String dictLabel = null ;
        for (SysDictData SysDictData : dictList) {
            if(value.equals(SysDictData.getDictValue())){
                dictLabel = SysDictData.getDictLabel();
            }
        }
        dictList.clear();
        return dictLabel ;
    }

    @Override
    public List<TendersDtlExport> detailExport(TendersInfoBo bo) {
        List<TendersInfoVo> list = queryList(bo);
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<Long> tendersIds = StreamUtils.toList(list, TendersInfoVo::getId);
        List<TendersDtl> dtlList = tendersDtlMapper.selectList(new LambdaQueryWrapper<TendersDtl>().in(TendersDtl::getTendersId, tendersIds));
        if(CollUtil.isEmpty(dtlList)){
            return Collections.emptyList();
        }
        Map<Long, TendersInfoVo> infoMap = StreamUtils.toMap(list, TendersInfoVo::getId, item -> item);
        List<TendersDtlExport> exportList = dtlList.stream().map(item -> {
            TendersDtlExport export =  BeanUtil.copyProperties(item, TendersDtlExport.class);
            if(infoMap.containsKey(item.getTendersId())){
                export.setInfoTitle(infoMap.get(item.getTendersId()).getInfoTitle());
                export.setBelongingArea(dictTypeConvert(infoMap.get(item.getTendersId()).getBelongingArea(), DictTypeEnum.TENDERS_BELONGING_AREA.getValue()));
                export.setProjectLevel(dictTypeConvert(String.valueOf(infoMap.get(item.getTendersId()).getProjectLevel()), DictTypeEnum.TENDERS_PROJECT_LEVEL.getValue()));
            }
            return export;
        }).collect(Collectors.toList());
        return exportList;
    }

    @Override
    public String titleImport(List<TendersInfoImportTemplate> list) {
        int i = 1;
        List<String> errorList = Lists.newArrayList();
        List<TendersInfo> infoList = Lists.newArrayList();
        for (TendersInfoImportTemplate item : list) {
            try
            {
                BeanValidators.validateObject(validator, item);
                TendersInfo add = BeanUtil.copyProperties(item, TendersInfo.class);
                // 招标标题
                checkInfoTitle(item.getInfoTitle());
                Region province = getRegion(item.getProvinceName());
                add.setProvinceId(province.getId());
                // 所属区域
                add.setBelongingArea(convertDictValue(item.getBelongingAreaExt(), DictTypeEnum.TENDERS_BELONGING_AREA));
                // 项目等级
                add.setProjectLevel(Integer.parseInt(convertDictValue(item.getProjectLevelExt(), DictTypeEnum.TENDERS_PROJECT_LEVEL)));
                // 行业
                add.setIndustry(convertDictValue(item.getIndustryExt(), DictTypeEnum.TENDERS_INDUSTRY));
                // 物资分类
                add.setMaterialCategory(convertDictValue(item.getMaterialCategoryExt(), DictTypeEnum.TENDERS_IMATERIAL_CATEGORY));
                // 招标方式
                add.setBiddingType(Integer.parseInt(convertDictValue(item.getBiddingTypeExt(), DictTypeEnum.BIDDING_TYPE)));
                // 信息类型
                add.setInfoType(Integer.parseInt(convertDictValue(item.getInfoTypeExt(), DictTypeEnum.TENDERS_INFO_TYPE)));
                // 二级信息类型细分
                add.setInfoTypeSegment(Integer.parseInt(convertDictValue(item.getInfoTypeSegmentExt(), DictTypeEnum.TENDERS_INFO_TYPE_SEGMENT)));
                add.setStatus(TendersStatusEnum.NEW.getValue());

                Map<String, Object> extraMap = (Map<String, Object>) Optional.ofNullable(
                                JSON.parseObject(add.getExtraData(), new TypeReference<Map<String, Object>>(){}))
                        .orElse(new HashMap<>());
                // 代理机构
                extraMap.put(TendersConstantEnum.AGENT_UNIT.getKey(), item.getAgentUnit());
                // 代理机构联系人
                extraMap.put(TendersConstantEnum.AGENT_RELATION_NAME.getKey(), item.getAgentRelationName());
                // 代理机构联系方式
                extraMap.put(TendersConstantEnum.AGENT_RELATION_WAY.getKey(), item.getAgentRelationWay());
                // 招标网站地址
                extraMap.put(TendersConstantEnum.WEBSITE_URL.getKey(), item.getWebsiteUrl());
                // 项目背景
                extraMap.put(TendersConstantEnum.PROJECT_BACKDROP.getKey(), item.getProjectBackdrop());
                // 前中标人
                extraMap.put(TendersConstantEnum.PREVIOUS_WINNING_BIDDER.getKey(), item.getPreviousWinningBidder());
                // 支撑内容
                extraMap.put(TendersConstantEnum.SUPPORTING_CONTENT.getKey(), item.getSupportingContent());
                // 中标概率
                extraMap.put(TendersConstantEnum.PROBABILITY.getKey(), item.getProbability());
                // 客户意向单位
                extraMap.put(TendersConstantEnum.CUST_INTENDED_UNIT.getKey(), item.getCustIntendedUnit());
                // 扣分分析
                extraMap.put(TendersConstantEnum.DEDUCTION_ANALYSIS.getKey(), item.getDeductionAnalysis());
                // 服务期限
                extraMap.put(TendersConstantEnum.SERVICE_PERIOD.getKey(), item.getServicePeriod());
                add.setExtraData(JSON.toJSONString(extraMap));
                add.setSourceType(TendersSourceTypeEnum.BID.getValue());
                add.created();
                infoList.add(add);
            } catch (Exception e) {
                errorList.add(String.format("第%s条数据:%s", i, e.getMessage()));
            }
            i++ ;
        }
        if (CollUtil.isNotEmpty(errorList)) {
            return String.join("<br/>", errorList);
        }
        this.saveBatch(infoList);
        return "导入成功";
    }

    private Region getRegion(String regionName) {
        Region province = regionMapper.selectOne(new LambdaQueryWrapper<Region>().like(Region::getRegionName, regionName.substring(0, 2)).eq(Region::getRegionLevel, 2));
        if(Objects.isNull(province)){
            throw new ServiceException("省份不存在");
        }
        return province;
    }


    @Override
    public String detailImport(List<TendersDtlImportTemplate> list) {
        int i = 1;
        List<String> errorList = Lists.newArrayList();
        List<TendersDtl> addList = Lists.newArrayList();
        for (TendersDtlImportTemplate item : list)
        {
            try
            {
                BeanValidators.validateObject(validator, item);
                TendersDtl add = BeanUtil.copyProperties(item, TendersDtl.class);
                // 招标标题
                TendersInfo exits = this.lambdaQuery().eq(TendersInfo::getInfoTitle, item.getInfoTitle().trim()).one();
                if(Objects.isNull(exits)){
                    throw new ServiceException("招标标题不存在");
                }
                add.setTendersId(exits.getId());
                add.created();
                addList.add(add);
            } catch (Exception e) {
                errorList.add(String.format("第%s条数据:%s", i, e.getMessage()));
            }
            i++ ;
        }
        if (CollUtil.isNotEmpty(errorList)) {
            return String.join("<br/>", errorList);
        }
        tendersDtlMapper.insertBatch(addList);
        return "导入成功";
    }

    @Override
    public String winDataImport(List<TendersWinDataImportTemplate> list) {
        int i = 1;
        List<String> errorList = Lists.newArrayList();

        for (TendersWinDataImportTemplate item : list)
        {
            try
            {
                BeanValidators.validateObject(validator, item);
            } catch (Exception e) {
                errorList.add(String.format("第%s条数据:%s", i, e.getMessage()));
            }
            i++ ;
        }
        if (CollUtil.isNotEmpty(errorList)) {
            return String.join("<br/>", errorList);
        }

        List<TendersInfo> updateList = Lists.newArrayList();
        List<String> titleList = StreamUtils.toList(list, TendersWinDataImportTemplate::getInfoTitle);
        List<TendersInfo> infoList = this.lambdaQuery().in(TendersInfo::getInfoTitle, titleList).list();
        Map<String, TendersInfo> infoMap = infoList.stream().collect(Collectors.toMap(TendersInfo::getInfoTitle, item -> item));

        Map<String, List<TendersWinDataImportTemplate>> mapList = StreamUtils.group(list, TendersWinDataImportTemplate::getInfoTitle);
        for (Map.Entry<String, List<TendersWinDataImportTemplate>> entry : mapList.entrySet())
        {
            TendersInfo exits = infoMap.get(entry.getKey());
            if(Objects.isNull(exits)){
                errorList.add(String.format("招标标题%s不存在", entry.getKey()));
                continue;
            }
            Map<String, Object> extraMap = (Map<String, Object>) Optional.ofNullable(
                            JSON.parseObject(exits.getExtraData(), new TypeReference<Map<String, Object>>(){}))
                    .orElse(new HashMap<>());

            // 中标信息List json 数据存储 [{"winBiddingUnit":"中标单位", "winBiddingAmount":"中标金额"}]
            extraMap.put(TendersConstantEnum.WIN_BIDDING_LIST.getKey(), entry.getValue());
            exits.setExtraData(JSON.toJSONString(extraMap));
            updateList.add(exits);
        }
        if (CollUtil.isNotEmpty(errorList)) {
            return String.join("<br/>", errorList);
        }
        this.updateBatchById(updateList);
        return "导入成功";
    }

    @Override
    public String winBidImport(List<TendersWinImportTemplate> list) {
        int i = 1;
        List<String> errorList = Lists.newArrayList();
        List<TendersInfo> infoList = Lists.newArrayList();
        for (TendersWinImportTemplate item : list) {
            try
            {
                BeanValidators.validateObject(validator, item);
                TendersInfo add = BeanUtil.copyProperties(item, TendersInfo.class);
                // 招标标题
                checkInfoTitle(item.getInfoTitle());
                Region province = getRegion(item.getProvinceName());
                add.setProvinceId(province.getId());
                // 所属区域
                add.setBelongingArea(convertDictValue(item.getBelongingAreaExt(), DictTypeEnum.TENDERS_BELONGING_AREA));
                // 项目等级
                add.setProjectLevel(Integer.parseInt(convertDictValue(item.getProjectLevelExt(), DictTypeEnum.TENDERS_PROJECT_LEVEL)));
                // 行业
                add.setIndustry(convertDictValue(item.getIndustryExt(), DictTypeEnum.TENDERS_INDUSTRY));
                // 物资分类
                add.setMaterialCategory(convertDictValue(item.getMaterialCategoryExt(), DictTypeEnum.TENDERS_IMATERIAL_CATEGORY));
                // 操作模式
                add.setOperationModel(convertDictValue(item.getOperationModel(), DictTypeEnum.OPERATION_MODEL));
                Map<String, Object> extraMap = ImmutableMap.of(
                        TendersConstantEnum.PERFORMANCE_CATEGORY.getKey(), item.getPerformanceCategory(),
                        TendersConstantEnum.BID_WIN_URL.getKey(), item.getBidWinUrl(),
                        TendersConstantEnum.SERVICE_PERIOD.getKey(), item.getServicePeriod());
                add.setExtraData(JSON.toJSONString(extraMap));
                add.setStatus(TendersStatusEnum.WINNING_BID.getValue());
                add.setSourceType(TendersSourceTypeEnum.OFFLINE.getValue());
                add.created();
                infoList.add(add);
            } catch (Exception e) {
                errorList.add(String.format("第%s条数据:%s", i, e.getMessage()));
            }
            i++ ;
        }
        if (CollUtil.isNotEmpty(errorList)) {
            return String.join("<br/>", errorList);
        }
        this.saveBatch(infoList);
        return "导入成功";
    }

    @Override
    public String loseBidImport(List<TendersLoseImportTemplate> list) {
        int i = 1;
        List<String> errorList = Lists.newArrayList();
        List<TendersInfo> infoList = Lists.newArrayList();
        for (TendersLoseImportTemplate item : list) {
            try
            {
                BeanValidators.validateObject(validator, item);
                TendersInfo add = BeanUtil.copyProperties(item, TendersInfo.class);
                checkInfoTitle(item.getInfoTitle());
                Region province = getRegion(item.getProvinceName());
                add.setProvinceId(province.getId());
                // 所属区域
                add.setBelongingArea(convertDictValue(item.getBelongingAreaExt(), DictTypeEnum.TENDERS_BELONGING_AREA));
                // 项目等级
                add.setProjectLevel(Integer.parseInt(convertDictValue(item.getProjectLevelExt(), DictTypeEnum.TENDERS_PROJECT_LEVEL)));
                // 物资分类
                add.setMaterialCategory(convertDictValue(item.getMaterialCategoryExt(), DictTypeEnum.TENDERS_IMATERIAL_CATEGORY));
                // 行业
                add.setIndustry(convertDictValue(item.getIndustryExt(), DictTypeEnum.TENDERS_INDUSTRY));
                Map<String, Object> extraMap = Maps.newHashMap();
                extraMap.put(TendersConstantEnum.SERVICE_PERIOD.getKey(), item.getServicePeriod());
                extraMap.put(TendersConstantEnum.BID_WIN_URL.getKey(), item.getBidWinUrl());
                extraMap.put(TendersConstantEnum.LOSE_BID_FILE_LIST.getKey(), item.getLoseBidFileList());
                extraMap.put(TendersConstantEnum.LOSE_BID_REMARK.getKey(), item.getLoseBidRemark());
                extraMap.put(TendersConstantEnum.BID_CAUSE_ANALYSIS.getKey(), item.getBidCauseAnalysis());
                extraMap.put(TendersConstantEnum.SALES_NAME.getKey(), item.getSalesName());
                extraMap.put(TendersConstantEnum.WEBSITE_URL.getKey(), item.getWebsiteUrl());
                add.setExtraData(JSON.toJSONString(extraMap));
                add.setStatus(TendersStatusEnum.LOSING_BID.getValue());
                add.setSourceType(TendersSourceTypeEnum.OFFLINE.getValue());
                add.created();
                infoList.add(add);
            } catch (Exception e) {
                errorList.add(String.format("第%s条数据:%s", i, e.getMessage()));
            }
            i++ ;
        }
        if (CollUtil.isNotEmpty(errorList)) {
            return String.join("<br/>", errorList);
        }
        this.saveBatch(infoList);
        return "导入成功";
    }

    private void checkInfoTitle(String item) {
        // 招标标题
        TendersInfo exits = this.lambdaQuery().eq(TendersInfo::getInfoTitle, item.trim()).one();
        if (Objects.nonNull(exits)) {
            throw new ServiceException("招标标题已存在");
        }
    }

    @Override
    public TableDataInfo<TendersInfoVo> queryAnalysisExtraPageList(TendersInfoBo bo) {
        LambdaQueryWrapper<TendersInfo> lqw = buildQueryWrapper(bo);
        PageQuery pageQuery = JSONUtil.toBean(JSONUtil.toJsonStr(bo), PageQuery.class);
        Page<TendersInfo> pageResult = baseMapper.selectPage(pageQuery.build(), lqw);
        List<TendersInfoVo> list = pageResult.getRecords().stream().map(item -> {
            TendersInfoVo tendersInfoVo = BeanUtil.copyProperties(item, TendersInfoVo.class);
            analysisExtraData(item.getExtraData(), tendersInfoVo);
            return tendersInfoVo;
        }).collect(Collectors.toList());
        IPage<TendersInfoVo> voPage = new Page<>(pageResult.getCurrent(), pageResult.getSize(), pageResult.getTotal());
        voPage.setRecords(list);
        return TableDataInfo.build(voPage);
    }

    @Override
    public List<TendersLoseExport> loseBid(TendersInfoBo bo) {
        LambdaQueryWrapper<TendersInfo> lqw = buildQueryWrapper(bo);
        List<TendersInfo> list = baseMapper.selectList(lqw);
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list.stream().map(this::buildLoseEntity).collect(Collectors.toList());

    }

    private TendersLoseExport buildLoseEntity(TendersInfo item) {
        TendersLoseExport export = BeanUtil.copyProperties(item, TendersLoseExport.class);
        export.setBelongingArea(dictTypeConvert(item.getBelongingArea(), DictTypeEnum.TENDERS_BELONGING_AREA.getValue()));
        export.setProjectLevel(dictTypeConvert(String.valueOf(item.getProjectLevel()), DictTypeEnum.TENDERS_PROJECT_LEVEL.getValue()));
        export.setMaterialCategory(dictTypeConvert(item.getMaterialCategory(), DictTypeEnum.TENDERS_IMATERIAL_CATEGORY.getValue()));
        export.setIndustry(dictTypeConvert(item.getIndustry(), DictTypeEnum.TENDERS_INDUSTRY.getValue()));
        if(StrUtil.isEmpty(item.getExtraData())){
            return export;
        }
        Map<String, Object> extraMap = JSON.parseObject(item.getExtraData(), new TypeReference<Map<String, Object>>() {});
        if (ObjectUtil.isEmpty(extraMap)){
            return export;
        }
        // 服务期限
        if(extraMap.containsKey(TendersConstantEnum.SERVICE_PERIOD.getKey())){
            export.setServicePeriod(MapUtil.getStr(extraMap, TendersConstantEnum.SERVICE_PERIOD.getKey()));
        }
        // 招标网站地址
        if(extraMap.containsKey(TendersConstantEnum.WEBSITE_URL.getKey())){
            export.setWebsiteUrl(MapUtil.getStr(extraMap, TendersConstantEnum.WEBSITE_URL.getKey()));
        }
        // 投标结果分析
        if(extraMap.containsKey(TendersConstantEnum.BID_CAUSE_ANALYSIS.getKey())){
            export.setBidCauseAnalysis(MapUtil.getStr(extraMap, TendersConstantEnum.BID_CAUSE_ANALYSIS.getKey()));
        }
        // 销售名称
        if(extraMap.containsKey(TendersConstantEnum.SALES_NAME.getKey())){
            export.setSalesName(MapUtil.getStr(extraMap, TendersConstantEnum.SALES_NAME.getKey()));
        }
        // 结果公示链接/中标链接
        if(extraMap.containsKey(TendersConstantEnum.BID_WIN_URL.getKey())){
            export.setBidWinUrl(MapUtil.getStr(extraMap, TendersConstantEnum.BID_WIN_URL.getKey()));
        }
        // 未中标原因
        if(extraMap.containsKey(TendersConstantEnum.LOSE_BID_FILE_LIST.getKey())){
            export.setLoseBidFileList(MapUtil.getStr(extraMap, TendersConstantEnum.LOSE_BID_FILE_LIST.getKey()));
        }
        if(extraMap.containsKey(TendersConstantEnum.LOSE_BID_REMARK.getKey())){
            export.setLoseBidRemark(MapUtil.getStr(extraMap, TendersConstantEnum.LOSE_BID_REMARK.getKey()));
        }
        return export;
    }

    @Override
    public List<TendersWinExport> winBidExport(TendersInfoBo bo) {
        LambdaQueryWrapper<TendersInfo> lqw = buildQueryWrapper(bo);
        List<TendersInfo> list = baseMapper.selectList(lqw);
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list.stream().map(this::buildWinEntity).collect(Collectors.toList());
    }

    private TendersWinExport buildWinEntity(TendersInfo item) {
        TendersWinExport export = BeanUtil.copyProperties(item, TendersWinExport.class);
        export.setBelongingArea(dictTypeConvert(item.getBelongingArea(), DictTypeEnum.TENDERS_BELONGING_AREA.getValue()));
        export.setProjectLevel(dictTypeConvert(String.valueOf(item.getProjectLevel()), DictTypeEnum.TENDERS_PROJECT_LEVEL.getValue()));
        export.setMaterialCategory(dictTypeConvert(item.getMaterialCategory(), DictTypeEnum.TENDERS_IMATERIAL_CATEGORY.getValue()));
        export.setIndustry(dictTypeConvert(item.getIndustry(), DictTypeEnum.TENDERS_INDUSTRY.getValue()));
        if(StrUtil.isEmpty(item.getExtraData())){
            return export;
        }
        Map<String, Object> extraMap = JSON.parseObject(item.getExtraData(), new TypeReference<Map<String, Object>>() {});
        if (ObjectUtil.isEmpty(extraMap)){
            return export;
        }
        // 服务期限
        if(extraMap.containsKey(TendersConstantEnum.SERVICE_PERIOD.getKey())){
            export.setServicePeriod(MapUtil.getStr(extraMap, TendersConstantEnum.SERVICE_PERIOD.getKey()));
        }
        // 履约品类
        if(extraMap.containsKey(TendersConstantEnum.PERFORMANCE_CATEGORY.getKey())){
            export.setPerformanceCategory(MapUtil.getStr(extraMap, TendersConstantEnum.PERFORMANCE_CATEGORY.getKey()));
        }
        // 结果公示链接/中标链接
        if(extraMap.containsKey(TendersConstantEnum.BID_WIN_URL.getKey())){
            export.setBidWinUrl(MapUtil.getStr(extraMap, TendersConstantEnum.BID_WIN_URL.getKey()));
        }
        return export;
    }

    private String convertDictValue(String dictLablel, DictTypeEnum dictTypeEnum) {
        String value = null;
        List<SysDictData> dictList = dictTypeService.selectDictDataByType(dictTypeEnum.getValue());
        if(CollUtil.isEmpty(dictList)){
            throw new ServiceException(String.format("%s数据不存在", dictTypeEnum.getDescription()));
        }
        for (SysDictData SysDictData : dictList) {
            if(dictLablel.equals(SysDictData.getDictLabel())){
                return SysDictData.getDictValue();

            }
        }
        if(value == null){
            throw new ServiceException(String.format("%s值错误", dictTypeEnum.getDescription()));
        }
        return null;
    }

}
