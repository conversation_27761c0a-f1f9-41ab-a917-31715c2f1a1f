package com.oms.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.oms.common.core.domain.BaseEntity;
import com.oms.common.core.page.TableDataInfo;
import com.oms.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.oms.common.enums.DomainDeletedType;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.oms.domain.bo.ApiUserBo;
import com.oms.domain.vo.ApiUserVo;
import com.oms.domain.ApiUser;
import com.oms.mapper.ApiUserMapper;
import com.oms.service.IApiUserService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * api用户Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-06
 */
@RequiredArgsConstructor
@Service
public class ApiUserServiceImpl extends ServiceImpl<ApiUserMapper, ApiUser>  implements IApiUserService {

    private final ApiUserMapper baseMapper;

    /**
     * 查询api用户
     */
    @Override
    public ApiUserVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    @Override
    public ApiUserVo queryByClientId(String appKey){
        return baseMapper.selectVoOne(Wrappers.lambdaQuery(ApiUser.class).eq(ApiUser::getApiKey, appKey).eq(BaseEntity::getDeleted, DomainDeletedType.NORMAL.code()));
    }

    /**
     * 查询api用户列表
     */
    @Override
    public TableDataInfo<ApiUserVo> queryPageList(ApiUserBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ApiUser> lqw = buildQueryWrapper(bo);
        Page<ApiUserVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询api用户列表
     */
    @Override
    public List<ApiUserVo> queryList(ApiUserBo bo) {
        LambdaQueryWrapper<ApiUser> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ApiUser> buildQueryWrapper(ApiUserBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ApiUser> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getTenantId() != null, ApiUser::getTenantId, bo.getTenantId());
        lqw.eq(bo.getPrjId() != null, ApiUser::getPrjId, bo.getPrjId());
        lqw.like(StringUtils.isNotBlank(bo.getPrjName()), ApiUser::getPrjName, bo.getPrjName());
        lqw.like(StringUtils.isNotBlank(bo.getApiUserName()), ApiUser::getApiUserName, bo.getApiUserName());
        lqw.eq(StringUtils.isNotBlank(bo.getApiUserEmain()), ApiUser::getApiUserEmain, bo.getApiUserEmain());
        lqw.eq(StringUtils.isNotBlank(bo.getApiDomian()), ApiUser::getApiDomian, bo.getApiDomian());
        lqw.eq(StringUtils.isNotBlank(bo.getApiKey()), ApiUser::getApiKey, bo.getApiKey());
        lqw.eq(StringUtils.isNotBlank(bo.getApiSecret()), ApiUser::getApiSecret, bo.getApiSecret());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), ApiUser::getStatus, bo.getStatus());
        lqw.like(StringUtils.isNotBlank(bo.getUpdateName()), ApiUser::getUpdateName, bo.getUpdateName());
        lqw.like(StringUtils.isNotBlank(bo.getCreateName()), ApiUser::getCreateName, bo.getCreateName());
        lqw.eq(bo.getDeleted() != null, ApiUser::getDeleted, bo.getDeleted());
        return lqw;
    }

    /**
     * 新增api用户
     */
    @Override
    public Boolean insertByBo(ApiUserBo bo) {
        ApiUser add = BeanUtil.toBean(bo, ApiUser.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改api用户
     */
    @Override
    public Boolean updateByBo(ApiUserBo bo) {
        ApiUser update = BeanUtil.toBean(bo, ApiUser.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ApiUser entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除api用户
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
