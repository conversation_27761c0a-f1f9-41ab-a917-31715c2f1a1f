package com.oms.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.oms.common.core.domain.BaseEntity;
import com.oms.common.core.page.TableDataInfo;
import com.oms.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.oms.common.enums.DomainDeletedType;
import com.oms.common.exception.ServiceException;
import com.oms.common.helper.LoginHelper;
import com.oms.domain.vo.GoodsTaxTypeVo;
import com.oms.mapper.GoodsTaxTypeMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.oms.domain.bo.GoodsCategoryTaxBindBo;
import com.oms.domain.vo.GoodsCategoryTaxBindVo;
import com.oms.domain.GoodsCategoryTaxBind;
import com.oms.mapper.GoodsCategoryTaxBindMapper;
import com.oms.service.IGoodsCategoryTaxBindService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 类目税收分类Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-10-06
 */
@RequiredArgsConstructor
@Service
public class GoodsCategoryTaxBindServiceImpl extends ServiceImpl<GoodsCategoryTaxBindMapper, GoodsCategoryTaxBind>  implements IGoodsCategoryTaxBindService {

    private final GoodsCategoryTaxBindMapper baseMapper;
    private final GoodsTaxTypeMapper goodsTaxTypeMapper;

    /**
     * 查询类目税收分类
     */
    @Override
    public GoodsCategoryTaxBindVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询类目税收分类列表
     */
    @Override
    public TableDataInfo<GoodsCategoryTaxBindVo> queryPageList(GoodsCategoryTaxBindBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<GoodsCategoryTaxBind> lqw = buildQueryWrapper(bo);
        Page<GoodsCategoryTaxBindVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询类目税收分类列表
     */
    @Override
    public List<GoodsCategoryTaxBindVo> queryList(GoodsCategoryTaxBindBo bo) {
        LambdaQueryWrapper<GoodsCategoryTaxBind> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<GoodsCategoryTaxBind> buildQueryWrapper(GoodsCategoryTaxBindBo bo) {
        if(bo.getCategoryId() == null){
            throw new ServiceException("类目id不能为空");
        }
        LambdaQueryWrapper<GoodsCategoryTaxBind> lqw = Wrappers.lambdaQuery();
        lqw.eq(BaseEntity::getTenantId, LoginHelper.getTenantId());
        lqw.eq(GoodsCategoryTaxBind::getCategoryId, bo.getCategoryId());
        lqw.eq(StringUtils.isNotBlank(bo.getTaxCode()), GoodsCategoryTaxBind::getTaxCode, bo.getTaxCode());
        lqw.like(StringUtils.isNotBlank(bo.getTaxName()), GoodsCategoryTaxBind::getTaxName, bo.getTaxName());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), GoodsCategoryTaxBind::getStatus, bo.getStatus());
        lqw.like(StringUtils.isNotBlank(bo.getUpdateName()), GoodsCategoryTaxBind::getUpdateName, bo.getUpdateName());
        lqw.like(StringUtils.isNotBlank(bo.getCreateName()), GoodsCategoryTaxBind::getCreateName, bo.getCreateName());
        lqw.eq(GoodsCategoryTaxBind::getDeleted, DomainDeletedType.NORMAL.code());
        lqw.eq(bo.getDeleteTime() != null, GoodsCategoryTaxBind::getDeleteTime, bo.getDeleteTime());
        lqw.eq(bo.getDeleteBy() != null, GoodsCategoryTaxBind::getDeleteBy, bo.getDeleteBy());
        lqw.like(StringUtils.isNotBlank(bo.getDeleteName()), GoodsCategoryTaxBind::getDeleteName, bo.getDeleteName());
        lqw.orderByDesc(BaseEntity::getCreateTime);
        return lqw;
    }

    /**
     * 新增类目税收分类
     */
    @Override
    public Boolean insertByBo(GoodsCategoryTaxBindBo bo) {
        if(bo.getCategoryId() == null || bo.getTaxId() == null){
            throw new ServiceException("参数不全");
        }
        GoodsTaxTypeVo taxTypeVo = goodsTaxTypeMapper.selectVoById(bo.getTaxId());
        if(taxTypeVo == null){
            throw new ServiceException("税收分类不存在");
        }
        GoodsCategoryTaxBind one = this.lambdaQuery()
                .eq(BaseEntity::getTenantId, bo.getTenantId())
                .eq(GoodsCategoryTaxBind::getCategoryId, bo.getCategoryId())
                .eq(GoodsCategoryTaxBind::getTaxCode, taxTypeVo.getTaxTypeCode())
                .eq(GoodsCategoryTaxBind::getTaxName, taxTypeVo.getTaxTypeName())
                .eq(BaseEntity::getDeleted, DomainDeletedType.NORMAL.code()).one();
        if(one != null){
            return Boolean.TRUE;
        }
        bo.setTaxName(taxTypeVo.getTaxTypeName());
        bo.setTaxCode(taxTypeVo.getTaxTypeCode());
        GoodsCategoryTaxBind add = BeanUtil.toBean(bo, GoodsCategoryTaxBind.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改类目税收分类
     */
    @Override
    public Boolean updateByBo(GoodsCategoryTaxBindBo bo) {
        GoodsCategoryTaxBind update = BeanUtil.toBean(bo, GoodsCategoryTaxBind.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(GoodsCategoryTaxBind entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除类目税收分类
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
