package com.oms.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.oms.common.core.domain.entity.SysUser;
import com.oms.common.core.domain.model.LoginUser;
import com.oms.common.core.page.TableDataInfo;
import com.oms.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.oms.common.enums.*;
import com.oms.common.exception.ServiceException;
import com.oms.common.helper.LoginHelper;
import com.oms.domain.VendorInfo;
import com.oms.mapper.SysUserMapper;
import com.oms.mapper.VendorInfoMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.oms.domain.bo.VendorUserStatusBo;
import com.oms.domain.vo.VendorUserStatusVo;
import com.oms.domain.VendorUserStatus;
import com.oms.mapper.VendorUserStatusMapper;
import com.oms.service.IVendorUserStatusService;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

import static com.oms.common.helper.LoginHelper.getLoginUser;

/**
 * 供应商账号状态Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-10-21
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class VendorUserStatusServiceImpl extends ServiceImpl<VendorUserStatusMapper, VendorUserStatus>  implements IVendorUserStatusService {

    private final VendorUserStatusMapper baseMapper;
    private final VendorInfoMapper vendorInfoMapper;
    private final SysUserMapper sysUserMapper;

    /**
     * 查询供应商账号状态
     */
    @Override
    public VendorUserStatusVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询供应商账号状态列表
     */
    @Override
    public TableDataInfo<VendorUserStatusVo> queryPageList(VendorUserStatusBo bo) {
        LambdaQueryWrapper<VendorUserStatus> lqw = buildQueryWrapper(bo);
        PageQuery pageQuery = JSONUtil.toBean(JSONUtil.toJsonStr(bo), PageQuery.class);
        lqw.orderByDesc(VendorUserStatus::getCreateTime);
        Page<VendorUserStatusVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询供应商账号状态列表
     */
    @Override
    public List<VendorUserStatusVo> queryList(VendorUserStatusBo bo) {
        LambdaQueryWrapper<VendorUserStatus> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<VendorUserStatus> buildQueryWrapper(VendorUserStatusBo bo) {
        LambdaQueryWrapper<VendorUserStatus> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getVendorId() != null, VendorUserStatus::getVendorId, bo.getVendorId());
        lqw.eq(bo.getApplyStatus() != null, VendorUserStatus::getApplyStatus, bo.getApplyStatus());
        lqw.eq(bo.getApplyShutdownTime() != null, VendorUserStatus::getApplyShutdownTime, bo.getApplyShutdownTime());
        lqw.eq(StringUtils.isNotBlank(bo.getApplyShutdownReason()), VendorUserStatus::getApplyShutdownReason, bo.getApplyShutdownReason());
        lqw.eq(bo.getStatus() != null, VendorUserStatus::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增供应商账号状态
     */
    @Override
    public Boolean insertByBo(VendorUserStatusBo bo) {
        VendorUserStatus add = BeanUtil.toBean(bo, VendorUserStatus.class);
        validEntityBeforeSave(add);
        add.setStatus(VendorUserStatusEnum.AUDIT_WAIT_FOR.getCode());
        add.setHandleStatus(VendorUserHandleStatusEnum.WAIT.getCode());
        return save(add);
    }

    /**
     * 修改供应商账号状态
     */
    @Override
    public Boolean updateByBo(VendorUserStatusBo bo) {
        VendorUserStatus update = BeanUtil.toBean(bo, VendorUserStatus.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(VendorUserStatus add){
        VendorInfo vendorInfo = vendorInfoMapper.selectOne(Wrappers.lambdaQuery(VendorInfo.class).eq(VendorInfo::getId, add.getVendorId()));
        if(Objects.isNull(vendorInfo)){
            throw new ServiceException("供应商不存在");
        }
        add.setVendorName(vendorInfo.getName());
        add.setUnifiedSocialCreditCode(vendorInfo.getUnifiedSocialCreditCode());

        VendorUserStatus vendorUserStatus = baseMapper.selectOne(Wrappers.lambdaQuery(VendorUserStatus.class).eq(VendorUserStatus::getVendorId, add.getVendorId())
                .ne(VendorUserStatus::getHandleStatus, VendorUserHandleStatusEnum.COMPLETE.getCode())
        );
        if(Objects.isNull(vendorUserStatus)){
            return ;
        }
        throw new ServiceException("当前供应商存在未处理完毕的申请,请处理后再操作");
    }

    /**
     * 批量删除供应商账号状态
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean auditStatus(VendorUserStatusBo bo) {
        VendorUserStatus exits = baseMapper.selectById(bo.getId());
        if (exits == null) {
            throw new ServiceException("数据不存在");
        }
        if (VendorUserStatusEnum.AUDIT_WAIT_FOR.getCode() != exits.getStatus()) {
            throw new ServiceException("数据非待审核状态");
        }
        VendorUserStatus update = BeanUtil.toBean(bo, VendorUserStatus.class);
        update.setHandleStatus(VendorUserHandleStatusEnum.COMPLETE.getCode());
        update.setVendorId(exits.getVendorId());
        // 审核拒绝
        if (VendorUserStatusEnum.AUDIT_REJECT.getCode().equals(bo.getStatus())) {
            this.updateById(update);
            return true;
        }
        // 重启
        if (StatusEnum.NOR.getValue().equals(exits.getApplyStatus())) {
            return handlerStart(bo.getIsSignNewContract(), update);
        }
        // 关停, 如果关停时间小于当前时间, 立即关停.  大于当前时间, 定时器关停
        if (exits.getApplyShutdownTime().getTime() > new Date().getTime()) {
            update.setHandleStatus(VendorUserHandleStatusEnum.WAIT.getCode());
            return this.updateById(update);
        }
        // 完成处理并关停账号
        this.updateById(update);
        return updateVendor(exits.getVendorId(), StatusEnum.DIS.getValue());
    }

    private boolean handlerStart(Boolean isSignNewContract, VendorUserStatus update) {
        // 处理需要重签合同的情况
        if (Boolean.TRUE.equals(isSignNewContract)) {
            update.setStatus(VendorUserStatusEnum.NOT_STARTED_CONTRACT.getCode());
        }
        this.updateById(update);
        // 供应商重启
        return updateVendor(update.getVendorId(), StatusEnum.NOR.getValue());
    }

    public boolean updateVendor(Long vendorId, String status) {
        vendorInfoMapper.update(Wrappers.lambdaUpdate(VendorInfo.class).set(VendorInfo::getStatus, status)
                .set(VendorInfo::getUpdateBy, LoginHelper.getUserId())
                .set(VendorInfo::getUpdateName, LoginHelper.getUsername())
                .set(VendorInfo::getUpdateTime, new Date())
                .eq(VendorInfo::getId, vendorId));
        return sysUserMapper.update(Wrappers.lambdaUpdate(SysUser.class)
                .set(SysUser::getStatus, status)
                .set(SysUser::getUpdateBy, LoginHelper.getUserId())
                .set(SysUser::getUpdateName, LoginHelper.getUsername())
                .set(SysUser::getUpdateTime, new Date())
                .eq(SysUser::getVendorId, vendorId)) > 0;
    }

    @Override
    public VendorUserStatusVo getByVendorId(Long vendorId) {
        VendorUserStatus vendorUserStatus = baseMapper.selectOne(Wrappers.lambdaQuery(VendorUserStatus.class).eq(VendorUserStatus::getVendorId, vendorId)
                .orderByDesc(VendorUserStatus::getId)
                .orderByDesc(VendorUserStatus::getId)
                .last("limit 1"));
        if(vendorUserStatus != null){
            VendorUserStatusVo vo = BeanUtil.toBean(vendorUserStatus, VendorUserStatusVo.class);
            return vo;
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleExpireShutdown() {
        // 获取 关停时间 小于当前时间的数据
        List<VendorUserStatus> list = baseMapper.selectList(Wrappers.lambdaQuery(VendorUserStatus.class)
                .eq(VendorUserStatus::getHandleStatus, VendorUserHandleStatusEnum.WAIT.getCode())
                .eq(VendorUserStatus::getApplyStatus, StatusEnum.DIS.getValue())
                .lt(VendorUserStatus::getApplyShutdownTime, new Date()));
        if (CollUtil.isEmpty(list)){
            log.info("没有需要关停的供应商");
            return;
        }
        for (VendorUserStatus vendorUserStatus : list) {
            updateVendor(vendorUserStatus.getVendorId(), StatusEnum.DIS.getValue());
            vendorUserStatus.setHandleStatus(VendorUserHandleStatusEnum.COMPLETE.getCode());
            this.updateById(vendorUserStatus);
        }
    }
}
