package com.oms.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.oms.common.core.domain.BaseEntity;
import com.oms.common.core.domain.PageQuery;
import com.oms.common.core.page.TableDataInfo;
import com.oms.common.enums.DomainDeletedType;
import com.oms.common.exception.ServiceException;
import com.oms.common.helper.LoginHelper;
import com.oms.domain.GoodsItemAudit;
import com.oms.domain.GoodsPriceApply;
import com.oms.domain.GoodsPrjSku;
import com.oms.domain.bo.GoodsPriceApplyBo;
import com.oms.domain.vo.GoodsPriceApplyVo;
import com.oms.mapper.GoodsItemAuditMapper;
import com.oms.mapper.GoodsPriceApplyMapper;
import com.oms.service.IGoodsPriceApplyService;
import com.oms.service.IGoodsPrjSkuService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;

/**
 * 服务商价格审核申请Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-10-06
 */
@RequiredArgsConstructor
@Service
public class GoodsPriceApplyServiceImpl extends ServiceImpl<GoodsPriceApplyMapper, GoodsPriceApply>  implements IGoodsPriceApplyService {

    private final GoodsPriceApplyMapper baseMapper;
    private final GoodsItemAuditMapper goodsItemAuditMapper;
    private final IGoodsPrjSkuService goodsPrjSkuService;

    /**
     * 查询服务商价格审核申请
     */
    @Override
    public GoodsPriceApplyVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询服务商价格审核申请列表
     */
    @Override
    public TableDataInfo<GoodsPriceApplyVo> queryPageList(GoodsPriceApplyBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<GoodsPriceApply> lqw = buildQueryWrapper(bo);
        Page<GoodsPriceApplyVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询服务商价格审核申请列表
     */
    @Override
    public List<GoodsPriceApplyVo> queryList(GoodsPriceApplyBo bo) {
        LambdaQueryWrapper<GoodsPriceApply> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<GoodsPriceApply> buildQueryWrapper(GoodsPriceApplyBo bo) {
        LambdaQueryWrapper<GoodsPriceApply> lqw = Wrappers.lambdaQuery();
        lqw.eq(BaseEntity::getTenantId, LoginHelper.getTenantId());
        lqw.eq(bo.getItemId() != null, GoodsPriceApply::getItemId, bo.getItemId());
        lqw.eq(bo.getSkuId() != null, GoodsPriceApply::getSkuId, bo.getSkuId());
        lqw.eq(bo.getPrjId() != null, GoodsPriceApply::getPrjId, bo.getPrjId());
        lqw.like(StringUtils.isNotBlank(bo.getPrjName()), GoodsPriceApply::getPrjName, bo.getPrjName());
        lqw.eq(bo.getVendorId() != null, GoodsPriceApply::getVendorId, bo.getVendorId());
        lqw.like(StringUtils.isNotBlank(bo.getVendorName()), GoodsPriceApply::getVendorName, bo.getVendorName());
        lqw.eq(bo.getBeforeBasePrice() != null, GoodsPriceApply::getBeforeBasePrice, bo.getBeforeBasePrice());
        lqw.eq(bo.getAfterBasePrice() != null, GoodsPriceApply::getAfterBasePrice, bo.getAfterBasePrice());
        lqw.eq(bo.getBeforeSalePrice() != null, GoodsPriceApply::getBeforeSalePrice, bo.getBeforeSalePrice());
        lqw.eq(bo.getAfterSalePrice() != null, GoodsPriceApply::getAfterSalePrice, bo.getAfterSalePrice());
        lqw.eq(StringUtils.isNotBlank(bo.getBeforGroupBasePrice()), GoodsPriceApply::getBeforGroupBasePrice, bo.getBeforGroupBasePrice());
        lqw.eq(StringUtils.isNotBlank(bo.getAfterGroupBasePrice()), GoodsPriceApply::getAfterGroupBasePrice, bo.getAfterGroupBasePrice());
        lqw.eq(StringUtils.isNotBlank(bo.getBeforGroupSalePrice()), GoodsPriceApply::getBeforGroupSalePrice, bo.getBeforGroupSalePrice());
        lqw.eq(StringUtils.isNotBlank(bo.getAfterGroupSalePrice()), GoodsPriceApply::getAfterGroupSalePrice, bo.getAfterGroupSalePrice());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), GoodsPriceApply::getStatus, bo.getStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getReason()), GoodsPriceApply::getReason, bo.getReason());
        lqw.eq(bo.getAuditAt() != null, GoodsPriceApply::getAuditAt, bo.getAuditAt());
        lqw.eq(StringUtils.isNotBlank(bo.getAuditBy()), GoodsPriceApply::getAuditBy, bo.getAuditBy());
        lqw.like(StringUtils.isNotBlank(bo.getUpdateName()), GoodsPriceApply::getUpdateName, bo.getUpdateName());
        lqw.like(StringUtils.isNotBlank(bo.getCreateName()), GoodsPriceApply::getCreateName, bo.getCreateName());
        lqw.eq(bo.getDeleted() != null, GoodsPriceApply::getDeleted, bo.getDeleted());
        lqw.eq(bo.getDeleteTime() != null, GoodsPriceApply::getDeleteTime, bo.getDeleteTime());
        lqw.eq(bo.getDeleteBy() != null, GoodsPriceApply::getDeleteBy, bo.getDeleteBy());
        lqw.like(StringUtils.isNotBlank(bo.getDeleteName()), GoodsPriceApply::getDeleteName, bo.getDeleteName());
        lqw.orderByDesc(BaseEntity::getCreateTime);
        return lqw;
    }

    /**
     * 新增服务商价格审核申请
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(GoodsPriceApplyBo bo) {
        //查询是否有审核中的单据
        GoodsItemAudit audit = goodsItemAuditMapper.selectOne(new LambdaQueryWrapper<GoodsItemAudit>()
                .eq(GoodsItemAudit::getPrjId, bo.getPrjId())
                .eq(GoodsItemAudit::getItemId, bo.getItemId())
                .eq(GoodsItemAudit::getVendorId, bo.getVendorId())
                .eq(GoodsItemAudit::getOpeation, "applyPrice")
                .eq(GoodsItemAudit::getStatus, "1")
                .eq(BaseEntity::getDeleted, DomainDeletedType.NORMAL.code()));
        if (ObjectUtil.isNotNull(audit)) {
            throw new ServiceException("存在申请的调价审核，无法再次修改");
        }
        List<GoodsPrjSku> prjSkuList = goodsPrjSkuService.lambdaQuery()
                .eq(GoodsPrjSku::getPrjId, bo.getPrjId())
                .eq(GoodsPrjSku::getItemId, bo.getItemId())
                .eq(BaseEntity::getDeleted, DomainDeletedType.NORMAL.code()).list();
        if(CollectionUtil.isEmpty(prjSkuList)){
            throw new ServiceException("未找到项目商品，无法修改");
        }
        GoodsPrjSku prjSku = prjSkuList.get(0);
        audit = new GoodsItemAudit();
        audit.setTenantId(bo.getTenantId());
        audit.setOpeation("applyPrice");
        audit.setItemId(bo.getItemId());
        audit.setSkuId(prjSku.getSkuId());
        audit.setPrjId(bo.getPrjId());
        audit.setVendorId(bo.getVendorId());
        audit.setSalePrice(prjSku.getSalePrice());
        audit.setStatus("1");
        goodsItemAuditMapper.insert(audit);
        GoodsPriceApply add = BeanUtil.toBean(bo, GoodsPriceApply.class);
        add.setAuditId(audit.getId());
        add.setBeforeSalePrice(prjSku.getSalePrice());
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改服务商价格审核申请
     */
    @Override
    public Boolean updateByBo(GoodsPriceApplyBo bo) {
        GoodsPriceApply update = BeanUtil.toBean(bo, GoodsPriceApply.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(GoodsPriceApply entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除服务商价格审核申请
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
