package com.oms.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.api.client.util.Maps;
import com.oms.common.core.domain.model.LoginUser;
import com.oms.common.core.page.TableDataInfo;
import com.oms.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.oms.common.enums.AuditStatusEnum;
import com.oms.common.enums.VendorContractExtendJsonEnum;
import com.oms.common.enums.VendorContractSignTypeEnum;
import com.oms.common.enums.VendorContractStatusEnum;
import com.oms.common.exception.ServiceException;
import com.oms.common.helper.LoginHelper;
import com.oms.common.utils.AssertTool;
import com.oms.common.utils.email.MailUtils;
import com.oms.domain.VendorInfo;
import com.oms.domain.bo.extend.vendor.VendorAuditBo;
import com.oms.domain.bo.extend.vendor.VendorContractDoubleStampBo;
import com.oms.mapper.VendorInfoMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.stereotype.Service;
import com.oms.domain.bo.VendorContractBo;
import com.oms.domain.vo.VendorContractVo;
import com.oms.domain.VendorContract;
import com.oms.mapper.VendorContractMapper;
import com.oms.service.IVendorContractService;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 供应商合同Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-01
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class VendorContractServiceImpl extends ServiceImpl<VendorContractMapper, VendorContract> implements IVendorContractService {

    private final VendorContractMapper baseMapper;
    private final VendorInfoMapper vendorInfoMapper;

    /**
     * 查询供应商合同
     */
    @Override
    public VendorContractVo queryById(Long id){
        VendorContract entity = checkById(id, LoginHelper.getTenantId());
        VendorContractVo vo = BeanUtil.toBean(entity, VendorContractVo.class);
        Map<String, Object> extendJson = Optional.ofNullable(JSON.parseObject(entity.getExtendJson(), Map.class)).orElse(new HashMap<>());
        if (MapUtil.isEmpty(extendJson)) {
            return vo;
        }
        vo.setStampContractUrl(MapUtil.getStr(extendJson, VendorContractExtendJsonEnum.STAMP_CONTRACT_URL.getCode()));
        vo.setStampContractUrlName(MapUtil.getStr(extendJson, VendorContractExtendJsonEnum.STAMP_CONTRACT_URL_NAME.getCode()));
        vo.setStampContractLogisticsCompany(MapUtil.getStr(extendJson, VendorContractExtendJsonEnum.STAMP_CONTRACT_LOGISTICS_COMPANY.getCode()));
        vo.setStampContractLogisticsNumber(MapUtil.getStr(extendJson, VendorContractExtendJsonEnum.STAMP_CONTRACT_LOGISTICS_NUMBER.getCode()));
        vo.setDoubleStampContractUrl(MapUtil.getStr(extendJson, VendorContractExtendJsonEnum.DOUBLE_STAMP_CONTRACT_URL.getCode()));
        vo.setDoubleStampContractUrlName(MapUtil.getStr(extendJson, VendorContractExtendJsonEnum.DOUBLE_STAMP_CONTRACT_URL_NAME.getCode()));
        vo.setDoubleStampContractLogisticsCompany(MapUtil.getStr(extendJson, VendorContractExtendJsonEnum.DOUBLE_STAMP_CONTRACT_LOGISTICS_COMPANY.getCode()));
        vo.setDoubleStampContractLogisticsNumber(MapUtil.getStr(extendJson, VendorContractExtendJsonEnum.DOUBLE_STAMP_CONTRACT_LOGISTICS_NUMBER.getCode()));
        return vo;
    }

    /**
     * 查询供应商合同列表
     */
    @Override
    public TableDataInfo<VendorContractVo> queryPageList(VendorContractBo bo) {
        LambdaQueryWrapper<VendorContract> lqw = buildQueryWrapper(bo);
        lqw.orderByDesc(VendorContract::getUpdateTime);
        PageQuery pageQuery = JSONUtil.toBean(JSONUtil.toJsonStr(bo), PageQuery.class);
        Page<VendorContractVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询供应商合同列表
     */
    @Override
    public List<VendorContractVo> queryList(VendorContractBo bo) {
        LambdaQueryWrapper<VendorContract> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<VendorContract> buildQueryWrapper(VendorContractBo bo) {
        LambdaQueryWrapper<VendorContract> lqw = Wrappers.lambdaQuery();
        lqw.eq(VendorContract::getTenantId, LoginHelper.getTenantId());
        lqw.eq(bo.getVendorId() != null, VendorContract::getVendorId, bo.getVendorId());
        lqw.eq(bo.getProjectId() != null, VendorContract::getProjectId, bo.getProjectId());
        lqw.eq(bo.getTemplateId() != null, VendorContract::getTemplateId, bo.getTemplateId());
        lqw.eq(StrUtil.isNotBlank(bo.getCode()), VendorContract::getCode, bo.getCode());
        lqw.like(StrUtil.isNotBlank(bo.getName()), VendorContract::getName, bo.getName());
        lqw.eq(StrUtil.isNotBlank(bo.getConent()), VendorContract::getConent, bo.getConent());

        lqw.ge(bo.getTakeEffectStartDate() != null, VendorContract::getValidityStartDate, bo.getTakeEffectStartDate());
        lqw.le(bo.getTakeEffectEndDate() != null, VendorContract::getValidityStartDate, bo.getTakeEffectEndDate());

        lqw.ge(bo.getInvalidityStartDate() != null, VendorContract::getValidityEndDate, bo.getInvalidityStartDate());
        lqw.le(bo.getInvalidityEndDate() != null, VendorContract::getValidityEndDate, bo.getInvalidityEndDate());

        lqw.eq(bo.getSignDate() != null, VendorContract::getSignDate, bo.getSignDate());
        lqw.eq(bo.getContractStatus() != null, VendorContract::getContractStatus, bo.getContractStatus());
        lqw.eq(bo.getSignType() != null, VendorContract::getSignType, bo.getSignType());
        lqw.eq(bo.getSourceType() != null, VendorContract::getSourceType, bo.getSourceType());
        return lqw;
    }

    /**
     * 新增供应商合同
     */
    @Override
    public Boolean insertByBo(VendorContractBo bo) {
        VendorContract add = BeanUtil.toBean(bo, VendorContract.class);
        // 当前时间 年月日时分秒毫秒
        add.setCode(DateFormatUtils.format(new Date(), "yyyyMMddHHmmssSSS"));
        add.setSignType(VendorContractSignTypeEnum.WAITING_SIGNATURE.getCode());
        add.created();
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改供应商合同
     */
    @Override
    public Boolean updateByBo(VendorContractBo bo) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        VendorContract vendorContract = checkById(bo.getId(), loginUser.getTenantId());
        if(vendorContract.getSignType() != VendorContractSignTypeEnum.WAITING_SIGNATURE.getCode()
                && vendorContract.getSignType() != VendorContractSignTypeEnum.ALREADY_TAKE_BACK.getCode()) {
            throw new ServiceException("非[待签署, 已收回]状态, 无法修改");
        }
        VendorContract update = BeanUtil.toBean(bo, VendorContract.class);
        update.setSignType(VendorContractSignTypeEnum.WAITING_SIGNATURE.getCode());
        update.updated();
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(VendorContract entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除供应商合同
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean vendorWarnInfo(Long id) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        VendorContract vendorContract = checkById(id, loginUser.getTenantId());
        VendorInfo vendorInfo = vendorInfoMapper.selectOne(Wrappers.<VendorInfo>lambdaQuery().eq(VendorInfo::getId, vendorContract.getVendorId())
                .eq(VendorInfo::getTenantId, loginUser.getTenantId()));
        if(Objects.isNull(vendorInfo)){
            throw new ServiceException("供应商不存在");
        }
        // 1 判断是否配置联系邮箱
        // 2 判断是否是待签署  待供应商邮寄
        // 3 发送邮件的提醒是否已经当天超过5次
        // 4 待签署时服务商尽快寄出合同  待供应商邮寄时尽快上传盖章合同
        if(StrUtil.isEmpty(vendorInfo.getEmail())){
            throw new ServiceException("该供应商未配置联系邮箱，请完善信息后在操作");
        }
        String curDate =  DateFormatUtils.format (new Date(), "yyyy-MM-dd");
        Map<String, Object> extendJson = Optional.ofNullable(JSON.parseObject(vendorContract.getExtendJson(), Map.class)).orElse(new HashMap<>());
        String warnDate = MapUtil.getStr(extendJson, "WARNDATE");
        String warnCount = MapUtil.getStr(extendJson, "WARNCOUNT");
        if(warnDate == null || !warnDate.equals(curDate)){
            extendJson.put("WARNDATE",curDate);
            extendJson.put("WARNCOUNT",1);
        }else if(warnDate.equals(curDate) && warnCount !=null && Integer.valueOf(warnCount).intValue()<5){
            extendJson.put("WARNCOUNT",Integer.valueOf(warnCount).intValue()+1);
        }else{
            throw new ServiceException("供应商提醒当天已经超过5次");
        }

        if(vendorContract.getSignType() != VendorContractSignTypeEnum.WAITING_SIGNATURE.getCode()
                && vendorContract.getSignType() != VendorContractSignTypeEnum.WAITING_VENDOR_MAIL.getCode()){
            throw new ServiceException(" 合同状态不是待签署或者待供应商邮寄,不能提醒");
        }
        // 修改扩展字段
        this.lambdaUpdate().eq(VendorContract::getId, vendorContract.getId())
                .set(VendorContract::getUpdateBy, loginUser.getId())
                .set(VendorContract::getUpdateName, loginUser.getUsername())
                .set(VendorContract::getUpdateTime, new Date())
                .set(VendorContract::getExtendJson, JSONUtil.toJsonStr(extendJson)).update();
        //发送邮件
        sendEmail(vendorInfo, vendorContract);
        return true;
    }

    private static void sendEmail(VendorInfo vendorInfo, VendorContract vendorContract) {
        String dimEmail = vendorInfo.getEmail();
        StringBuffer contentSb  = new StringBuffer();
        contentSb.append(" 尊敬的合作伙伴：").append(vendorInfo.getName()).append(" 您好,");
        if(vendorContract.getSignType() == VendorContractSignTypeEnum.WAITING_SIGNATURE.getCode()){
            contentSb.append("请您尽快上传盖章合同.");
        }else if(vendorContract.getSignType() == VendorContractSignTypeEnum.WAITING_VENDOR_MAIL.getCode()){
            contentSb.append("请您尽快邮寄合同.");
        }
        log.info("供应商合同提醒:dimEmail {}", dimEmail);
        MailUtils.sendText(dimEmail, "供应商合同提醒", contentSb.toString());
    }

    @Override
    public Boolean alreadyTakeBackContract(Long id) {
        return updateSignType(id, VendorContractSignTypeEnum.WAITING_SIGNATURE, VendorContractSignTypeEnum.ALREADY_TAKE_BACK);
    }

    @Override
    public Boolean receiveStampContract(Long id) {
        return updateSignType(id, VendorContractSignTypeEnum.VENDOR_ALREADY_MAIL, VendorContractSignTypeEnum.WAITING_DOUBLE_STAMP);
    }

    @Override
    public Boolean auditStampContract(VendorAuditBo bo) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        VendorContract vendorContract = checkById(bo.getId(), loginUser.getTenantId());
        if(vendorContract.getSignType() != VendorContractSignTypeEnum.ALREADY_SIGNATURE.getCode()){
            throw new ServiceException("当前状态非[待确定], 无法审核!");
        }
        return this.lambdaUpdate().eq(VendorContract::getId, vendorContract.getId())
                .set(VendorContract::getUpdateBy, loginUser.getId())
                .set(VendorContract::getUpdateName, loginUser.getUsername())
                .set(VendorContract::getUpdateTime, new Date())
                .set(VendorContract::getSignType, AuditStatusEnum.NOT_PASS.getValue() == bo.getAuditStatus() ?
                        VendorContractSignTypeEnum.AUDIT_REJECT.getCode(): VendorContractSignTypeEnum.WAITING_VENDOR_MAIL.getCode())
                .set(VendorContract::getSignMsg, bo.getAuditMsg())
                .update();
    }

    @Override
    public Boolean postOffStampContract(VendorContractBo bo) {
        VendorContract vendorContract = checkById(bo.getId(), LoginHelper.getTenantId());
        if(vendorContract.getSignType() != VendorContractSignTypeEnum.WAITING_VENDOR_MAIL.getCode()){
            throw new ServiceException(String.format("状态错误, 当前非邮寄状态, 当前状态[%s]", VendorContractSignTypeEnum.getDesc(vendorContract.getSignType())));
        }
        Map<String, Object> extendJsonMap = Optional.ofNullable(JSON.parseObject(vendorContract.getExtendJson(), Map.class)).orElse(Maps.newHashMap());
        extendJsonMap.put(VendorContractExtendJsonEnum.STAMP_CONTRACT_LOGISTICS_COMPANY.getCode(), bo.getStampContractLogisticsCompany());
        extendJsonMap.put(VendorContractExtendJsonEnum.STAMP_CONTRACT_LOGISTICS_NUMBER.getCode(), bo.getStampContractLogisticsNumber());
        return this.lambdaUpdate()
                .set(VendorContract::getUpdateBy, LoginHelper.getUserId())
                .set(VendorContract::getUpdateName, LoginHelper.getUsername())
                .set(VendorContract::getUpdateTime, new Date())
                .set(VendorContract::getSignType, VendorContractSignTypeEnum.VENDOR_ALREADY_MAIL.getCode())
                .set(VendorContract::getExtendJson, JSONUtil.toJsonStr(extendJsonMap))
                .eq(VendorContract::getId, bo.getId()).update();
    }

    @Override
    public Boolean uploadStampContract(VendorContractBo bo) {
        VendorInfo vendorInfo = vendorInfoMapper.selectById(bo.getVendorId());
        AssertTool.nonNull(vendorInfo, "供应商不存在");
//        bo.setVendorName(vendorInfo.getName());
        VendorContract vendorContract = checkById(bo.getId(), LoginHelper.getTenantId());
        if(vendorContract.getSignType() != VendorContractSignTypeEnum.WAITING_SIGNATURE.getCode()
                && vendorContract.getSignType() != VendorContractSignTypeEnum.AUDIT_REJECT.getCode()){
            throw new ServiceException(String.format("状态错误, 当前非待签署或已驳回, 无法重新上传, 当前状态[%s]", VendorContractSignTypeEnum.getDesc(vendorContract.getSignType())));
        }
        if(vendorContract.getContractStatus() == VendorContractStatusEnum.PAUSE.getCode()){
            throw new ServiceException("状态错误, 当前合同已暂停, 无法操作");
        }
        Map<String, Object> extendJsonMap = Optional.ofNullable(JSON.parseObject(vendorContract.getExtendJson(), Map.class)).orElse(Maps.newHashMap());
        extendJsonMap.put(VendorContractExtendJsonEnum.STAMP_CONTRACT_URL.getCode(), bo.getStampContractUrl());
        extendJsonMap.put(VendorContractExtendJsonEnum.STAMP_CONTRACT_URL_NAME.getCode(), bo.getStampContractUrlName());
        return this.lambdaUpdate()
                .set(VendorContract::getUpdateBy, LoginHelper.getUserId())
                .set(VendorContract::getUpdateName, LoginHelper.getUsername())
                .set(VendorContract::getUpdateTime, new Date())
                .set(VendorContract::getSignType, VendorContractSignTypeEnum.ALREADY_SIGNATURE.getCode())
                .set(VendorContract::getExtendJson, JSONUtil.toJsonStr(extendJsonMap))
                .eq(VendorContract::getId, bo.getId()).update();
    }

    @Override
    public Boolean uploadDoubleStampContract(VendorContractDoubleStampBo bo) {
        VendorContract vendorContract = checkById(bo.getId(), LoginHelper.getTenantId());
        if(vendorContract.getSignType() != VendorContractSignTypeEnum.WAITING_DOUBLE_STAMP.getCode()){
            throw new ServiceException(String.format("当前状态非[%s], 无法操作!", VendorContractSignTypeEnum.getDesc(VendorContractSignTypeEnum.WAITING_DOUBLE_STAMP.getCode())));
        }
        VendorContract update = BeanUtil.toBean(bo, VendorContract.class);
        update.setSignType(VendorContractSignTypeEnum.COMPLETE_SIGNATURE.getCode());
        // 比较时间， 立即生效
        if(bo.getValidityStartDate() != null && bo.getValidityEndDate() != null){
            Long nowTime = new Date().getTime();
            if(nowTime < bo.getValidityStartDate().getTime() ){
                update.setContractStatus(VendorContractStatusEnum.WAITING_EFFECTIVE.getCode());
            }
            if(nowTime > bo.getValidityStartDate().getTime() && nowTime < bo.getValidityEndDate().getTime()){
                update.setContractStatus(VendorContractStatusEnum.EFFECTIVE.getCode());
            }
        }
        Map<String, Object> extendJsonMap = Optional.ofNullable(JSON.parseObject(vendorContract.getExtendJson(), Map.class)).orElse(Maps.newHashMap());
        extendJsonMap.put(VendorContractExtendJsonEnum.DOUBLE_STAMP_CONTRACT_URL.getCode(), bo.getDoubleStampContractUrl());
        extendJsonMap.put(VendorContractExtendJsonEnum.DOUBLE_STAMP_CONTRACT_URL_NAME.getCode(), bo.getDoubleStampContractUrlName());
        extendJsonMap.put(VendorContractExtendJsonEnum.DOUBLE_STAMP_CONTRACT_LOGISTICS_COMPANY.getCode(), bo.getDoubleStampContractLogisticsCompany());
        extendJsonMap.put(VendorContractExtendJsonEnum.DOUBLE_STAMP_CONTRACT_LOGISTICS_NUMBER.getCode(), bo.getDoubleStampContractLogisticsNumber());
        update.setExtendJson(JSONUtil.toJsonStr(extendJsonMap));
        update.setSignDate(new Date());
        return this.updateById(update);
    }

    private Boolean updateSignType(Long id, VendorContractSignTypeEnum checkSignTypeEnum, VendorContractSignTypeEnum changeSignTypeEnum) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        VendorContract vendorContract = checkById(id, loginUser.getTenantId());
        if (vendorContract.getSignType() != checkSignTypeEnum.getCode()) {
            throw new ServiceException(String.format("当前状态非[%s], 无法操作!", VendorContractSignTypeEnum.getDesc(checkSignTypeEnum.getCode())));
        }
        return this.lambdaUpdate().eq(VendorContract::getId, vendorContract.getId())
                .set(VendorContract::getUpdateBy, loginUser.getId())
                .set(VendorContract::getUpdateName, loginUser.getUsername())
                .set(VendorContract::getUpdateTime, new Date())
                .set(VendorContract::getSignType, changeSignTypeEnum.getCode()).update();
    }

    private VendorContract checkById(Long id, Long tenantId) {
        VendorContract vendorContract = this.lambdaQuery().eq(VendorContract::getId, id).eq(VendorContract::getTenantId, tenantId).one();
        if (Objects.isNull(vendorContract)) {
            throw new ServiceException("数据不存在");
        }
        return vendorContract;
    }
}
