package com.oms.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.oms.common.core.domain.BaseEntity;
import com.oms.common.core.page.TableDataInfo;
import com.oms.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.oms.common.exception.ServiceException;
import com.oms.common.helper.LoginHelper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.oms.domain.bo.VendorContactBo;
import com.oms.domain.vo.VendorContactVo;
import com.oms.domain.VendorContact;
import com.oms.mapper.VendorContactMapper;
import com.oms.service.IVendorContactService;

import java.util.*;

/**
 * 供应商联系人Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-02
 */
@RequiredArgsConstructor
@Service
public class VendorContactServiceImpl extends ServiceImpl<VendorContactMapper, VendorContact>  implements IVendorContactService {

    private final VendorContactMapper baseMapper;

    /**
     * 查询供应商联系人
     */
    @Override
    public VendorContactVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询供应商联系人列表
     */
    @Override
    public TableDataInfo<VendorContactVo> queryPageList(VendorContactBo bo) {
        LambdaQueryWrapper<VendorContact> lqw = buildQueryWrapper(bo);
        PageQuery pageQuery = JSONUtil.toBean(JSONUtil.toJsonStr(bo), PageQuery.class);
        Page<VendorContactVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询供应商联系人列表
     */
    @Override
    public List<VendorContactVo> queryList(VendorContactBo bo) {
        LambdaQueryWrapper<VendorContact> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<VendorContact> buildQueryWrapper(VendorContactBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<VendorContact> lqw = Wrappers.lambdaQuery();
        lqw.eq(BaseEntity::getTenantId, LoginHelper.getTenantId());
        lqw.like(StringUtils.isNotBlank(bo.getName()), VendorContact::getName, bo.getName());
        lqw.eq(StringUtils.isNotBlank(bo.getJobTitle()), VendorContact::getJobTitle, bo.getJobTitle());
        lqw.eq(StringUtils.isNotBlank(bo.getPhoneNumber()), VendorContact::getPhoneNumber, bo.getPhoneNumber());
        lqw.eq(StringUtils.isNotBlank(bo.getFixedLineTelephone()), VendorContact::getFixedLineTelephone, bo.getFixedLineTelephone());
        lqw.eq(StringUtils.isNotBlank(bo.getEmailAddress()), VendorContact::getEmailAddress, bo.getEmailAddress());
        lqw.eq(StringUtils.isNotBlank(bo.getProjectId()), VendorContact::getProjectId, bo.getProjectId());
        lqw.like(StringUtils.isNotBlank(bo.getProjectName()), VendorContact::getProjectName, bo.getProjectName());
        lqw.eq(StringUtils.isNotBlank(bo.getAddress()), VendorContact::getAddress, bo.getAddress());
        lqw.eq(bo.getVendorId() != null, VendorContact::getVendorId, bo.getVendorId());
        return lqw;
    }




    /**
     * 新增供应商联系人
     */
    @Override
    public Boolean insertByBo(VendorContactBo bo) {
        VendorContact add = BeanUtil.toBean(bo, VendorContact.class);
        validEntityBeforeSave(add);
        add.setCreateTime(new Date());
        add.setCreateBy(LoginHelper.getUserId());
        return this.save(add);
    }

    /**
     * 修改供应商联系人
     */
    @Override
    public Boolean updateByBo(VendorContactBo bo) {
        VendorContact update = BeanUtil.toBean(bo, VendorContact.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(VendorContact param){
        //TODO 做一些数据校验,如唯一约束
        LambdaQueryWrapper<VendorContact> lqw = this.buildQueryWrapper(BeanUtil.toBean(VendorContact.builder()
                .vendorId(param.getVendorId())
                .projectId(param.getProjectId())
                .jobTitle(param.getJobTitle())
                .build(), VendorContactBo.class));
        List<VendorContact> exits = this.list(lqw);
        if (CollUtil.isNotEmpty(exits)) {
            throw new ServiceException(String.format("%s联系人已存在。", param.getJobTitle()));
        }
    }

    /**
     * 批量删除供应商联系人
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
