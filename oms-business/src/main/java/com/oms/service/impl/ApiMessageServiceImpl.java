package com.oms.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.oms.common.core.domain.PageQuery;
import com.oms.common.core.page.TableDataInfo;
import com.oms.domain.ApiMessage;
import com.oms.domain.MessagePool;
import com.oms.domain.bo.ApiMessageBo;
import com.oms.domain.bo.MessagePoolBo;
import com.oms.domain.vo.ApiMessageVo;
import com.oms.domain.vo.MessagePoolVo;
import com.oms.mapper.ApiMessageMapper;
import com.oms.mapper.MessagePoolMapper;
import com.oms.service.IApiMessageService;
import com.oms.service.IMessagePoolService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * api消息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-07
 */
@RequiredArgsConstructor
@Service
public class ApiMessageServiceImpl extends ServiceImpl<ApiMessageMapper, ApiMessage>  implements IApiMessageService {

    private final ApiMessageMapper baseMapper;

    private final MessagePoolMapper messagePoolMapper;

    private final IMessagePoolService messagePoolService;

    /**
     * 查询api消息
     */
    @Override
    public ApiMessageVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询api消息列表
     */
    @Override
    public TableDataInfo<ApiMessageVo> queryPageList(ApiMessageBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ApiMessage> lqw = buildQueryWrapper(bo);
        lqw.orderByDesc(ApiMessage::getCreateTime);
        Page<ApiMessageVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询api消息列表
     */
    @Override
    public List<ApiMessageVo> queryList(ApiMessageBo bo) {
        LambdaQueryWrapper<ApiMessage> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ApiMessage> buildQueryWrapper(ApiMessageBo bo) {
        LambdaQueryWrapper<ApiMessage> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getTenantId() != null, ApiMessage::getTenantId, bo.getTenantId());
        lqw.eq(bo.getPrjId() != null, ApiMessage::getPrjId, bo.getPrjId());
        lqw.like(StringUtils.isNotBlank(bo.getPrjName()), ApiMessage::getPrjName, bo.getPrjName());
        lqw.eq(StringUtils.isNotBlank(bo.getOmsMessageId()), ApiMessage::getOmsMessageId, bo.getOmsMessageId());
        lqw.eq(bo.getMessageType() != null, ApiMessage::getMessageType, bo.getMessageType());
        lqw.eq(StringUtils.isNotBlank(bo.getOperationType()), ApiMessage::getOperationType, bo.getOperationType());
        lqw.eq(bo.getStatus() != null, ApiMessage::getStatus, bo.getStatus());
        lqw.eq(null != bo.getClientStatus(), ApiMessage::getClientStatus, bo.getClientStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getContent()), ApiMessage::getContent, bo.getContent());
        lqw.eq(StringUtils.isNotBlank(bo.getReson()), ApiMessage::getReson, bo.getReson());
        lqw.like(StringUtils.isNotBlank(bo.getUpdateName()), ApiMessage::getUpdateName, bo.getUpdateName());
        lqw.like(StringUtils.isNotBlank(bo.getCreateName()), ApiMessage::getCreateName, bo.getCreateName());
        lqw.eq(bo.getDeleted() != null, ApiMessage::getDeleted, bo.getDeleted());
        lqw.eq(bo.getDeleteBy() != null, ApiMessage::getDeleteBy, bo.getDeleteBy());
        lqw.like(StringUtils.isNotBlank(bo.getDeleteName()), ApiMessage::getDeleteName, bo.getDeleteName());
        return lqw;
    }

    /**
     * 新增api消息
     */
    @Override
    public Boolean insertByBo(ApiMessageBo bo) {
        ApiMessage add = BeanUtil.toBean(bo, ApiMessage.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改api消息
     */
    @Override
    public Boolean updateByBo(ApiMessageBo bo) {
        ApiMessage update = BeanUtil.toBean(bo, ApiMessage.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ApiMessage entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除api消息
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean syncOmsMessage() {
        PageQuery pageQuery = new PageQuery();
        pageQuery.setPageNum(1);
        pageQuery.setPageSize(100);
        pageQuery.setOrderByColumn("create_time");

        MessagePoolBo messagePoolBo = new MessagePoolBo();
        messagePoolBo.setStatus(0L);
        TableDataInfo<MessagePoolVo> tableDataInfo = messagePoolService.queryPageList(messagePoolBo,pageQuery);
        if(null != tableDataInfo && CollectionUtils.isNotEmpty(tableDataInfo.getRows())){
            List<MessagePoolVo> messagePoolVos = tableDataInfo.getRows();
            List<ApiMessage> apiMessages = BeanUtil.copyToList(messagePoolVos,ApiMessage.class);
            //循环所有的数据
            apiMessages.stream().forEach(apiMessage -> {
                apiMessage.setCreateTime(null);
                apiMessage.setCreateName("system");
                apiMessage.setCreateBy(null);
                apiMessage.setUpdateTime(null);
                apiMessage.setUpdateName("system");
                apiMessage.setUpdateBy(null);
                apiMessage.setStatus(1L);
                apiMessage.setClientStatus(0L);
                apiMessage.setOmsMessageId(apiMessage.getId().toString());
                apiMessage.setId(null);
            });
            //批量插入
            baseMapper.insertBatch(apiMessages);
            //将消息池状态更新成已处理
            List<Long> messageIds = messagePoolVos.stream().map(MessagePoolVo::getId).collect(Collectors.toList());
            messagePoolMapper.update(new LambdaUpdateWrapper<MessagePool>()
                    .set(MessagePool::getStatus,1)
                    .in(MessagePool::getId,messageIds));
        }
        return true;
    }
}
