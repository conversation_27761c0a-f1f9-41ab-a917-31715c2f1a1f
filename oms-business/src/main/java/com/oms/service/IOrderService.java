package com.oms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.oms.common.core.domain.PageQuery;
import com.oms.common.core.page.TableDataInfo;
import com.oms.domain.Order;
import com.oms.domain.bo.OrderBo;
import com.oms.domain.bo.OrderDeliveredBo;
import com.oms.domain.bo.OrderDeliveryBo;
import com.oms.domain.bo.PreOrderBo;
import com.oms.domain.bo.extend.order.*;
import com.oms.domain.vo.OrderVo;
import com.oms.domain.vo.SysOssVo;
import com.oms.domain.vo.extend.order.*;

import java.util.Collection;
import java.util.List;

/**
 * 采购订单Service接口
 *
 * <AUTHOR>
 * @date 2024-09-20
 */
public interface IOrderService extends IService<Order>{

    /**
     * 查询采购订单
     */
    OrderVo queryById(Long id);

    /**
     * 查询采购订单列表
     */
    TableDataInfo<OrderVo> queryPageList(OrderBo bo, PageQuery pageQuery);


    /**
     * 查询供应商采购订单列表
     */
    TableDataInfo<OrderVo> queryVendorPageList(OrderBo bo, PageQuery pageQuery);

    /**
     * 查询采购订单列表
     */
    List<OrderVo> queryList(OrderBo bo);

    /**
     * 新增采购订单
     */
    Boolean insertByBo(OrderBo bo);

    /**
     * 修改采购订单
     */
    Boolean updateByBo(OrderBo bo);

    /**
     * 校验并批量删除采购订单信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);


    /**
     * 修改采购订单供应商信息
     * @param updateType 1 ：修改服务商  2：修改子供应商
     */
    Boolean updateVendor(OrderVendorUpdateBo bo,Integer updateType);



    /**
     * 查询供应商列表(带费率)
     */
    List<OrderShopListQueryVo> queryShipVendorList(OrderVendorFeeRateQueryBo bo);


    /**
     * 修改供应商发货商信息
     */
    Boolean updateShipVendor(OrderVendorUpdateBo bo);



    /**
     * 修改子供应商
     */
    Boolean updateSubVendor(OrderVendorUpdateBo bo);


    /**
     *采购单确认审核
     * @return
     */
    Boolean audit(OrderAuditBo bo);


    /**
     * 验收合格同意发货
     * @return
     */
    Boolean shipAgree(OrderBo bo);

    /**
     * 修改地址
     */
    Boolean updateAddress(OrderAddressUpdateBo bo);


    /**
     * 订单妥投
     * @param  bo 订单妥投
     */
    Boolean deliveredAll(OrderDeliveredAllBo bo);



    /**
     * 订单妥投
     * @param  bo 订单妥投
     */
    Boolean deliveredAllBySellerOrderId(OrderDeliveredAllBo bo);

    /**
     * 订单部分妥投
     * @param  bo 订单妥投
     */
    Boolean deliveredPart(OrderDeliveredBo bo);

    /**
     * 订单虚拟妥投
     * @param  bo 订单妥投
     */
    Boolean virtualDeliveredPart(OrderDeliveredBo bo);


    /**
     * 修改采购订单费率
     */
    Boolean updateVendorFeeRate(OrderFeeRateUpdateBo bo);


    /**
     * 修改卖家备注
     * @param bo
     * @return
     */
    boolean updateShopNotes(OrderShopNotesUpdateBo bo);



    /**
     * 采购单撤回
     * @param bo
     * @return
     */
    boolean recall(OrderRecallBo bo);


    /**
     * 订单拆分
     * @param orderSplitBo
     * @return
     */
    boolean split(OrderSplitBo orderSplitBo);



    /**
     * 订单整单发货
     * @param  bo 订单妥投
     */
    Boolean deliveryAll(OrderDeliveryBo bo);

    /**
     * 订单发货(供应商)
     * @param  bo 订单妥投
     */
    Boolean delivery(OrderDeliveryBo bo);




    /**
     *供应商采购单确认 生成供应商合同
     * @return
     */
    Boolean confrim(OrderVendorConfirmContractBo bo);


    /**
     * 订单发货(供应商)
     * @param  bo 订单妥投
     */
    List<OrderLineCanDeliveryVo> queryCanDeliveryList(OrderQueryBo bo);



    /**
     * 订单发货(供应商)
     * @param  bo 订单妥投
     */
    OrderLineCanDeliveryV2Vo queryCanDeliveryV2List(OrderQueryBo bo);


    /**
     * 自动生成合同(带章)
     * @param orderId
     * @return
     */
    Boolean autoGeneratorContract(Long orderId);


    /**
     * 修改供应商发货商信息
     * @param orderContractBo
     * @param autoConfrim
     * @return
     */
    SysOssVo downloadShipContract(OrderContractBo orderContractBo,boolean autoConfrim);


    /**
     * 查询采购订单导出列表
     */
    List<OrderExportVo> queryExportList(OrderBo bo);


    /**
     * 查询供应商采购订单导出列表
     */
    List<OrderVendorExportVo> queryVendorExportList(OrderBo bo);


    /**
     * 查询首页信息
     * @return
     */
    OrderHomeQueryVo queryHomeData(Long vendorId);

}
