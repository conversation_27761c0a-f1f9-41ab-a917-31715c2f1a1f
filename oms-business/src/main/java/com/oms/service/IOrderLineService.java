package com.oms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.oms.domain.OrderLine;
import com.oms.domain.vo.OrderLineVo;
import com.oms.domain.bo.OrderLineBo;
import com.oms.domain.vo.SellerOrderLineVo;
import com.oms.mapper.OrderLineMapper;

import com.oms.common.core.page.TableDataInfo;
import com.oms.common.core.domain.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 采购订单行Service接口
 *
 * <AUTHOR>
 * @date 2024-09-20
 */
public interface IOrderLineService extends IService<OrderLine>{

    /**
     * 查询采购订单行
     */
    OrderLineVo queryById(Long id);

    /**
     * 查询采购订单行列表
     */
    TableDataInfo<OrderLineVo> queryPageList(OrderLineBo bo, PageQuery pageQuery);

    /**
     * 查询采购订单行列表
     */
    List<OrderLineVo> queryList(OrderLineBo bo);

    /**
     * 新增采购订单行
     */
    Boolean insertByBo(OrderLineBo bo);

    /**
     * 修改采购订单行
     */
    Boolean updateByBo(OrderLineBo bo);

    /**
     * 校验并批量删除采购订单行信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);


    /**
     * 查询采购订单行
     * @param prjId 项目Id
     * @orderId 订单id
     */
    List<OrderLine> queryByOrderId(Long prjId,Long orderId);


    /**
     * 通过项id查询所有的订单行
     * @param sellerOrderIds
     * @return
     */
    List<OrderLineVo> queryOrderLinesByIds(List<Long> sellerOrderIds) ;
}
