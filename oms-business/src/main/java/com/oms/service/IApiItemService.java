package com.oms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.oms.domain.ApiItem;
import com.oms.domain.vo.ApiItemVo;
import com.oms.domain.bo.ApiItemBo;
import com.oms.mapper.ApiItemMapper;

import com.oms.common.core.page.TableDataInfo;
import com.oms.common.core.domain.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * api商品Service接口
 *
 * <AUTHOR>
 * @date 2025-05-07
 */
public interface IApiItemService extends IService<ApiItem>{

    /**
     * 查询api商品
     */
    ApiItemVo queryById(Long id);

    /**
     * 查询api商品列表
     */
    TableDataInfo<ApiItemVo> queryPageList(ApiItemBo bo, PageQuery pageQuery);

    /**
     * 查询api商品列表
     */
    List<ApiItemVo> queryList(ApiItemBo bo);

    /**
     * 新增api商品
     */
    Boolean insertByBo(ApiItemBo bo);

    /**
     * 修改api商品
     */
    Boolean updateByBo(ApiItemBo bo);

    /**
     * 校验并批量删除api商品信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
