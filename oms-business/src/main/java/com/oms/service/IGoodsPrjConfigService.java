package com.oms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.oms.common.core.domain.PageQuery;
import com.oms.common.core.page.TableDataInfo;
import com.oms.domain.GoodsPrjConfig;
import com.oms.domain.bo.GoodsPrjConfigBo;
import com.oms.domain.vo.GoodsPrjConfigVo;

import java.util.Collection;
import java.util.List;

/**
 * 项目配置Service接口
 *
 * <AUTHOR>
 * @date 2024-12-08
 */
public interface IGoodsPrjConfigService extends IService<GoodsPrjConfig>{

    /**
     * 查询项目配置
     */
    GoodsPrjConfigVo queryById(Long id);

    /**
     * 根据项目id查询项目配置
     */
    GoodsPrjConfigVo queryByPrjId(Long prjId);

    /**
     * 查询项目配置列表
     */
    TableDataInfo<GoodsPrjConfigVo> queryPageList(GoodsPrjConfigBo bo);

    /**
     * 查询项目配置列表
     */
    List<GoodsPrjConfigVo> queryList(GoodsPrjConfigBo bo);

    /**
     * 新增项目配置
     */
    Boolean addOrUpdate(GoodsPrjConfigBo bo);

}
