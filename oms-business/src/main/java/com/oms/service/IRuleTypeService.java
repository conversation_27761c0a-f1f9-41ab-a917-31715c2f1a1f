package com.oms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.oms.domain.RuleType;
import com.oms.domain.vo.RuleTypeVo;
import com.oms.domain.bo.RuleTypeBo;
import com.oms.mapper.RuleTypeMapper;

import com.oms.common.core.page.TableDataInfo;
import com.oms.common.core.domain.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 规则类型Service接口
 *
 * <AUTHOR>
 * @date 2024-11-09
 */
public interface IRuleTypeService extends IService<RuleType>{

    /**
     * 查询规则类型
     */
    RuleTypeVo queryById(Long id);

    /**
     * 查询规则类型列表
     */
    TableDataInfo<RuleTypeVo> queryPageList(RuleTypeBo bo, PageQuery pageQuery);

    /**
     * 查询规则类型列表
     */
    List<RuleTypeVo> queryList(RuleTypeBo bo);

    /**
     * 新增规则类型
     */
    Boolean insertByBo(RuleTypeBo bo);

    /**
     * 修改规则类型
     */
    Boolean updateByBo(RuleTypeBo bo);

    /**
     * 校验并批量删除规则类型信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 通过缓存查询规则类型列表
     * @return
     */
    List<RuleTypeVo> queryListCache();

}
