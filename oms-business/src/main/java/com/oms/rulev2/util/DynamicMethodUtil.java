package com.oms.rulev2.util;

import com.oms.rulev2.context.RuleContext;
import lombok.extern.slf4j.Slf4j;


@Slf4j
public class DynamicMethodUtil {

    /**
     * 动态方法执行
     * @param runVal 参数
     * @param context 上下文
     * @return 执行结果
     */
    public static  boolean dynamicMethodExe(String runVal,RuleContext context) {
        // 通过反射执行动态方法
        String[] returnValArg = runVal.split("#");
        if (returnValArg.length != 2) {
            log.error("returnValArg length not equal 2");
            return false;
        }
        String className = returnValArg[0];
        String method = returnValArg[1];
        log.info("-------ruleEngine---------,returnVal:{},className:{},method:{}", runVal, className, method);
        try {
            Class<?> cla = Class.forName(className);
            Object obj = cla.newInstance();
            java.lang.reflect.Method m = cla.getMethod(method, RuleContext.class);
            m.invoke(obj, context);
        } catch (Exception e) {
            log.error("Class.forName,error:", e);
            return false;
        }
        return true;
    }

}
