package com.oms.rulev2.engine;

import com.oms.rulev2.context.RuleContext;
import com.oms.rulev2.listener.NoticeRuleListener;
import com.oms.rulev2.service.RuleService;
import io.netty.util.internal.ThrowableUtil;
import lombok.extern.slf4j.Slf4j;
import org.jeasy.rules.api.Rule;
import org.jeasy.rules.api.Rules;
import org.jeasy.rules.api.RulesEngineParameters;
import org.jeasy.rules.core.DefaultRulesEngine;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 规则引擎服务
 */
@Service
@Slf4j
public class RuleEngine {


    @Autowired
    private RuleService ruleService;

    /**
     * 执行规则
     *
     * @param context 规则上下文
     * @return
     */
    public void executeRule(RuleContext context) {
        List<Rule> list = ruleService.ruleList(context.getRuleTypeCode(), context.getTenantId(), context);
        // 创建规则集合
        Rules rules = new Rules();
        list.forEach(rules::register);
        // 创建规则引擎并配置
        RulesEngineParameters parameters = new RulesEngineParameters();
        parameters.setSkipOnFirstAppliedRule(true); // 设置为 true，匹配到第一个规则后停止执行其他规则
        // 创建规则引擎
        DefaultRulesEngine rulesEngine = new DefaultRulesEngine(parameters);
        rulesEngine.registerRuleListener(new NoticeRuleListener());

        // 触发规则引擎
        try {
            rulesEngine.fire(rules, context.getParams());
        } catch (Exception e) {
            String errorMsg = ThrowableUtil.stackTraceToString(e);
            log.error("规则引擎执行异常 getParams: {}", errorMsg);
            context.setIsMatch(Boolean.FALSE);
            context.setIsError(Boolean.TRUE);
            context.setErrorMsg(errorMsg);
        }
    }

}
