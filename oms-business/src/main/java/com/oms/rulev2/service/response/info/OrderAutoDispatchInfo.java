package com.oms.rulev2.service.response.info;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.List;

/**
 * 自动派单规则返回结果
 */
@EqualsAndHashCode(callSuper = true)
@Slf4j
@Data
public class OrderAutoDispatchInfo extends RuleBaseInfo{


    /**
     * 商品ID
     */
    private Long skuId;

    /**
     * 项目ID
     */
    private Long prjId;

    /**
     * 阶梯扣点
     */
    private String dimStepPoints;

    /**
     * 阶梯扣点 基于dimStepPoints解析后的对象
     */
    private List<StepPointsInfo> stepPointsInfos;

    /**
     * 是否阶梯扣点
     */
    private Boolean dimFlag;

    /**
     * 采购单标识 1.报备 2.落地 3.厂控 4.大单议价
     */
    private Long matchType;


    /**
     * 配送的供应商ID
     */
    private Long vendorId;

    /**
     * 配送供应商名称
     */
    private String vendorName;

    /**
     * 费率
     */
    private BigDecimal feeRate;

    public void setDimStepPoints(String dimStepPoints) {
        this.dimStepPoints = dimStepPoints;
        if (StrUtil.isNotBlank(dimStepPoints) && JSONUtil.isTypeJSONArray(dimStepPoints)) {
            try {
                stepPointsInfos = JSONUtil.parseArray(dimStepPoints).toList(StepPointsInfo.class);
            } catch (Exception e) {
                log.error("dimStepPoints parse {}, error:{}", e, dimStepPoints);
            }
        }
    }
}
