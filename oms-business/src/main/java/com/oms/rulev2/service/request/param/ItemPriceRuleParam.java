package com.oms.rulev2.service.request.param;

import com.oms.rulev2.annotations.DimModel;
import com.oms.rulev2.annotations.DimProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@DimModel("售价校验规则参数")
public class ItemPriceRuleParam {

    /**
     * SKUID
     */
    @DimProperty
    private Long dimSkuId;


    /**
     * 折扣率
     */
    @DimProperty
    private BigDecimal dimDiscountRate;

    /**
     * 客户分类
     */
    @DimProperty
    private String dimOutCategoryId;

    /**
     * 销售价
     */
    @DimProperty
    private Long dimSalePrice;

    /**
     * 市场价
     */
    @DimProperty
    private Long dimOriginalPrice;
}
