package com.oms.rulev2.service.dynamic;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.oms.common.utils.spring.SpringUtils;
import com.oms.domain.OrderReport;
import com.oms.domain.bo.OrderReportLineBo;
import com.oms.domain.vo.OrderReportLineVo;
import com.oms.domain.vo.OrderReportVo;
import com.oms.rulev2.context.RuleContext;
import com.oms.rulev2.service.request.OrderReportMatchRequest;
import com.oms.rulev2.service.request.param.OrderReportMatchLineParam;
import com.oms.rulev2.service.response.info.ReportOrderMatchInfo;
import com.oms.service.IOrderReportLineService;
import com.oms.service.IOrderReportService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 报备订单动态方法执行
 */
@Data
@Slf4j
public class OrderReportDynamic implements IDynamic{

    private IOrderReportService orderReportService;

    private IOrderReportLineService orderReportLineService;

    @Override
    public void init() {
        orderReportService = SpringUtils.getBean(IOrderReportService.class);
        orderReportLineService = SpringUtils.getBean(IOrderReportLineService.class);
    }

    @Override
    public void execute(RuleContext context) {
        this.init();
        OrderReportMatchRequest request = context.getRequest();
        log.info("ReportOrderDynamic request: {}", request);
        List<ReportOrderMatchInfo> result = new ArrayList<>();
        OrderReportVo matchOrder = matchOrderReport(request);
        log.info("----------matchOrderReport----result:{}", matchOrder);
        if (Objects.isNull(matchOrder)) {
            return;
        }
        if (CollUtil.isNotEmpty(matchOrder.getOrderReportLineList())) {
            for (OrderReportLineVo line : matchOrder.getOrderReportLineList()) {
                ReportOrderMatchInfo info = new ReportOrderMatchInfo();
                info.setSkuId(line.getSkuId());
                info.setReportId(matchOrder.getId());
                //info.setReportNo(matchOrder.getOrderNo());
                info.setVendorId(matchOrder.getVendorId());
                info.setVendorName(matchOrder.getVendorName());
                result.add(info);
            }
        } else {
            for (OrderReportMatchLineParam orderLine : request.getOrderLines()) {
                ReportOrderMatchInfo info = new ReportOrderMatchInfo();
                info.setReportId(matchOrder.getId());
                info.setSkuId(orderLine.getSkuId());
                //info.setReportNo(matchOrder.getOrderNo());
                info.setVendorId(matchOrder.getVendorId());
                info.setVendorName(matchOrder.getVendorName());
                result.add(info);
            }

        }

        // 设置返回结果
        context.setResult(result);
    }

    public OrderReportVo matchOrderReport(OrderReportMatchRequest request) {
        log.info("----------matchOrderReport----request:{}", request);

        //外部单号
        String outCode = request.getOutOrderNo();
        List<OrderReportVo> resultList = new ArrayList<>();
        Long prjId = Long.valueOf(request.getPrjId());
        //匹配优先级： 外部单号 》采购订单号 》采购单号+0165 》 oms订单号
        if (StrUtil.isNotBlank(outCode)) {
            List<OrderReportVo> list = getByOutCode(outCode,prjId, null);
            log.info("----------marry----外部单号： {}--,list:{}",outCode, list);
            resultList.addAll(list);
        }

        //采购单号
        String applyCode = request.getApplyCode();
        if (StrUtil.isNotBlank(applyCode)) {
            List<OrderReportVo> list = getByOutCode(applyCode, prjId, null);
            log.info("----------marry----采购单号： {}--,list:{}", applyCode, list);
            resultList.addAll(list);

            List<OrderReportVo> extraList = getByOutCode(null, prjId, applyCode);
            log.info("----------marry----采购单号： {}--,extraList:{}", applyCode, extraList);
            resultList.addAll(extraList);
        }

        // oms订单号
        String sourceId = request.getSourceId();
        if (StrUtil.isNotBlank(sourceId)) {
            List<OrderReportVo> list = getByOutCode(sourceId, prjId, null);
            log.info("----------marry----oms订单号： {}--,list:{}", sourceId, list);
            resultList.addAll(list);
        }

        //外部单号匹配为空,则按SKU维度,判断是否全部匹配,仅没有勾选外部单号的报备单才可进行匹配
        List<OrderReportVo> marryList = new ArrayList<>();
        if (CollUtil.isEmpty(resultList)) {
            List<OrderReportVo> list = marryList(request);
            marryList.addAll(list);
            log.info("----------marry----SKU维度： {}--,list:{}", request, marryList);
        }

        //预售单所有商品行
        Map<Long, List<OrderReportMatchLineParam>> skuIdOrderMap = request.getOrderLines().stream()
                .collect(Collectors.groupingBy(OrderReportMatchLineParam::getSkuId));
        log.info("----------marry--sku---,skuIdOrderMap:{}", JSON.toJSONString(skuIdOrderMap));

        Map<Long, List<OrderReportLineVo>> lineMap = new HashMap<>();

        for (OrderReportVo orderReport : marryList) {
            OrderReportLineBo query = new OrderReportLineBo();
            query.setTenantId(orderReport.getTenantId());
            query.setOrderId(orderReport.getId());
            List<OrderReportLineVo> lineList = orderReportLineService.queryList(query);
            log.info("----------marry--sku---reportId: {},,lines:{}", orderReport.getId(), lineList);
            if (CollUtil.isEmpty(lineList)) {
                continue;
            }

            List<OrderReportLineVo> matchLineList = new ArrayList<>();
            // 判断订单行商品ID是否全部匹配
            for (OrderReportLineVo line : lineList) {
                List<OrderReportMatchLineParam> orderLines = skuIdOrderMap.get(line.getSkuId());
                if (orderLines == null || orderLines.isEmpty()) {
                    continue;
                }
                // 校验数量
                if (line.getResidueQuantity() > 0) {
                    matchLineList.add(line);
                }
            }
            log.info("----------marry--sku---,pipeiLine:{}", matchLineList.size());
            if (CollectionUtils.isNotEmpty(matchLineList)) {
                resultList.add(orderReport);
                lineMap.put(orderReport.getId(), matchLineList);
            }
        }

        // 按照审核时间升序排序
        List<OrderReportVo> orderReportList = resultList.stream()
                .sorted(Comparator.comparing(OrderReportVo::getPushAuditAt)).collect(Collectors.toList());

        if (CollUtil.isNotEmpty(orderReportList)) {
            for (OrderReportVo resInfo : orderReportList) {
                List<OrderReportLineVo> orderReportLineList = lineMap.get(resInfo.getId());
                if (CollUtil.isNotEmpty(orderReportLineList)) {
                    resInfo.setOrderReportLineList(orderReportLineList);
                }
            }
            return orderReportList.get(0);
        }
        return null;
    }

    private List<OrderReportVo> marryList(OrderReportMatchRequest request) {
        LambdaQueryWrapper<OrderReport> wrapper = Wrappers.<OrderReport>lambdaQuery()
                .eq(OrderReport::getPrjId,request.getPrjId())
                .eq(OrderReport::getIsExanteOrder, 0)
                .in(OrderReport::getReportStatus, 0,1)
                .in(OrderReport::getAuditStatus, 2)
                .eq(StrUtil.isNotBlank(request.getProvinceCode()), OrderReport::getProvinceCode, request.getProvinceCode())
                .eq(StrUtil.isNotBlank(request.getCityCode()), OrderReport::getCityCode, request.getCityCode())
                .isNull(OrderReport::getExanteOrderId)
                .isNotNull(OrderReport::getPushAuditAt)
                .orderByAsc(OrderReport::getPushAuditAt);
        return orderReportService.queryList(wrapper);
    }

    private List<OrderReportVo> getByOutCode(String outCode, Long prjId, String exanteOrderIdExtra) {
        LambdaQueryWrapper<OrderReport> wrapper = Wrappers.<OrderReport>lambdaQuery()
                .eq(OrderReport::getPrjId,prjId)
//                .eq(OrderReport::getIsExanteOrder, 1)
                .in(OrderReport::getReportStatus, 0,1)
                .in(OrderReport::getAuditStatus, 2)
                .eq(StrUtil.isNotBlank(outCode), OrderReport::getExanteOrderId, outCode)
                .eq(StrUtil.isNotBlank(exanteOrderIdExtra), OrderReport::getExanteOrderIdExtra, exanteOrderIdExtra)
                .isNotNull(OrderReport::getPushAuditAt)
                .orderByAsc(OrderReport::getPushAuditAt);
        return orderReportService.queryList(wrapper);
    }
}
