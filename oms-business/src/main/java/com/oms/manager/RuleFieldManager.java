package com.oms.manager;

import com.oms.domain.bo.RuleFieldBo;
import com.oms.domain.vo.RuleFieldVo;
import com.oms.rulev2.enums.RuleEngineConstants;
import com.oms.service.IRuleFieldService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 规则字段
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class RuleFieldManager implements KVManager<Long, RuleFieldVo>{

    @Autowired
    private final IRuleFieldService ruleFieldService;

    /**
     * 根据规则类型ID获取规则字段列表Map
     * @return  规则字段列表Map
     */
    public Map<Long, RuleFieldVo> getMapCache() {
        List<RuleFieldVo> list = ruleFieldService.queryListCache();
        return list.stream().collect(Collectors.toMap(RuleFieldVo::getId, Function.identity()));
    }

    /**
     * 根据规则类型ID获取规则字段列表
     * @param ruleTypeId 规则类型ID
     * @return  规则字段列表
     */
    public List<RuleFieldVo> getByRuleTypeId(Long ruleTypeId) {
        return getList().stream().filter(ruleFieldVo -> ruleFieldVo.getRuleTypeId().equals(ruleTypeId)).collect(Collectors.toList());
    }

    /**
     * 根据规则类型ID获取规则字段列表 (无缓存)
     * @param ruleTypeId 规则类型ID
     * @return  规则字段列表
     */
    public List<RuleFieldVo> getByRuleTypeIdNoCache(Long ruleTypeId) {
        RuleFieldBo query = new RuleFieldBo();
        query.setRuleTypeId(ruleTypeId);
        return ruleFieldService.queryList(query);
    }


    /**
     * 根据规则类型ID获取规则字段列表 (无缓存)
     * @param ruleTypeIdList 规则类型ID列表
     * @return  key规则类型ID value规则字段列表
     */
    public Map<Long,List<RuleFieldVo>> getByRuleTypeIdNoCache(Set<Long> ruleTypeIdList) {
        RuleFieldBo query = new RuleFieldBo();
        query.setRuleTypeIdList(ruleTypeIdList);
        query.setStatus(RuleEngineConstants.ENABLE.getValue().intValue());
        return ruleFieldService.queryList(query).stream().collect(Collectors.groupingBy(RuleFieldVo::getRuleTypeId));
    }
}
