package com.oms.excel.convert;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.oms.domain.vo.RuleResultVo;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 规则行返回结果列表转换器
 *
 * <AUTHOR>
 */
public class RuleResultListConverter implements Converter<List<RuleResultVo>> {

    @Override
    public WriteCellData<?> convertToExcelData(List<RuleResultVo> value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) {
        if (value == null || value.isEmpty()) {
            return new WriteCellData<>("");
        }

        String result = value.stream()
                .filter(Objects::nonNull)
                .map(resultVo -> {
                    // Concatenate dimName, '=', and getResultValue()
                    return resultVo.getDimName() + "=" + resultVo.getResultValue();
                })
                .collect(Collectors.joining("; ")); // Join with semicolon and a space for readability

        return new WriteCellData<>(result);
    }

    @Override
    public Class<Object> supportJavaTypeKey() {
        return Object.class;
    }

}
