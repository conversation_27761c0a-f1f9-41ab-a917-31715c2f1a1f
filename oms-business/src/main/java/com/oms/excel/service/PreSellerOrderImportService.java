package com.oms.excel.service;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.oms.common.enums.AsyncTaskExecuteEnum;
import com.oms.common.excel.AsyncTaskContextContext;
import com.oms.common.excel.ExcelResult;
import com.oms.common.exception.ServiceException;
import com.oms.common.utils.poi.ExcelUtil;
import com.oms.domain.bo.PreSellerOrderBo;
import com.oms.domain.bo.extend.order.PreSellerOrderLineImportBo;
import com.oms.domain.vo.extend.order.PreSellerOrderLineImportVo;
import com.oms.excel.strategy.ExcelStrategy;
import com.oms.listener.PreSellerOrderImportListener;
import com.oms.service.IOutSellerOrderService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Service
@Slf4j
@RequiredArgsConstructor
public class PreSellerOrderImportService implements ExcelStrategy {

    private final IOutSellerOrderService sellerOrderService;

    @Override
    public String getType() {
        return AsyncTaskExecuteEnum.PRE_SELLER_ORDER_IMPORT.getCode();
    }



    @Override
    public void execute(AsyncTaskContextContext asyncTaskContextBo) {
        //读取EXCEL  ---表单验证 一次性读取
        //ExcelResult<PreSellerOrderLineImportVo>  result =  ExcelUtil.importExcelV2(asyncTaskContextBo, PreSellerOrderLineImportVo.class,true);
        //读取EXCEL ---自定义读取(每行自定义内容)
        ExcelResult<PreSellerOrderLineImportBo> result = ExcelUtil.importExcelV2(asyncTaskContextBo, PreSellerOrderLineImportBo.class, new PreSellerOrderImportListener());
        //全成功模式
        if(CollectionUtils.isNotEmpty(result.getErrorList())){
            //返回错误信息
            throw new ServiceException(result.getAnalysis());
        }
        if(CollectionUtils.isEmpty(result.getList())){
            throw new ServiceException("导入数据为空");
        }
        //将数据按照外部订单号  进行分组
        List<PreSellerOrderLineImportBo> preSellerOrderLineImportVoList = result.getList();
        // 使用Stream API进行分组，组合prjId和outId作为键
        Map<String, List<PreSellerOrderLineImportBo>> groupedMap = preSellerOrderLineImportVoList.stream()
                .collect(Collectors.groupingBy(
                        vo -> vo.getOutId()
                ));
        //批量创建订单
        sellerOrderService.createOrderByExcel(asyncTaskContextBo.getPrjId(),asyncTaskContextBo.getPrjName(),groupedMap);
    }

//    @Override
//    public AsyncResponseVo execute(AsyncTaskContextBo asyncTaskContextBo) {
//        AsyncResponseVo importResponseVo = new  AsyncResponseVo();
//        //导入Exccel（easy excel）
//        List<MdmBanktypeBo> excelList = ExcelUtil.importExcel(asyncTaskContextBo.getImportInputStream(), MdmBanktypeBo.class);
//        //打印下导入结果
//        log.info(JSONUtil.toJsonStr(excelList));
//        importResponseVo.setStatus(AsyncStatusEnums.SUCC.code());
//        //自定义
//        //List<MdmBanktypeBo> excelList = ExcelUtil.importExcel(new FileInputStream(file), MdmBanktypeBo.class, 自定义listener);
//        //requestJson 是对应的导入任务的对象 //TODO
//        return importResponseVo;
//    }
}
