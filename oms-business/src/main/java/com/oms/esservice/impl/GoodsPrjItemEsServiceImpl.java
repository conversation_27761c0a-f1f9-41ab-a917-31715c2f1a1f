package com.oms.esservice.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.http.HttpStatus;
import com.google.api.client.util.Lists;
import com.google.api.client.util.Maps;
import com.oms.common.core.page.TableDataInfo;
import com.oms.common.utils.StringUtils;
import com.oms.domain.GoodsPrj;
import com.oms.domain.bo.GoodsPrjItemBo;
import com.oms.domain.vo.GoodsBackCategoryVo;
import com.oms.esmapper.GoodsPrjItemEsMapper;
import com.oms.esmodel.GoodsPrjItemEs;
import com.oms.esservice.IGoodsPrjItemEsService;
import com.oms.mapper.GoodsPrjMapper;
import com.oms.service.IGoodsBackCategoryService;
import com.oms.utils.CategoryUtil;
import org.dromara.easyes.core.biz.EsPageInfo;
import org.dromara.easyes.core.conditions.select.LambdaEsQueryWrapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 项目商品ServiceEs业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-24
 */
@Service
public class GoodsPrjItemEsServiceImpl implements IGoodsPrjItemEsService {

    @Resource
    private GoodsPrjItemEsMapper GoodsPrjItemEsMapper;
    @Resource
    private GoodsPrjMapper goodsPrjMapper;
    @Resource
    private IGoodsBackCategoryService goodsBackCategoryService;

    @Override
    public TableDataInfo<GoodsPrjItemEs> pageList(GoodsPrjItemBo bo) {
        LambdaEsQueryWrapper<GoodsPrjItemEs> lqw = packQuery(bo);
        EsPageInfo<GoodsPrjItemEs> esPageInfo = GoodsPrjItemEsMapper.pageQuery(lqw,bo.getPageNum(),bo.getPageSize());
        TableDataInfo<GoodsPrjItemEs> rspData = new TableDataInfo<>();
        rspData.setCode(HttpStatus.HTTP_OK);
        rspData.setMsg("查询成功");
        if(esPageInfo != null && CollectionUtil.isNotEmpty(esPageInfo.getList())){
            List<Long> prjIds = esPageInfo.getList().stream().map(GoodsPrjItemEs::getPrjId).distinct().collect(Collectors.toList());
            Map<Long, GoodsPrj> prjMap = Maps.newHashMap();
            if(CollectionUtil.isNotEmpty(prjIds)){
                List<GoodsPrj> prjList = goodsPrjMapper.selectBatchIds(prjIds);
                if(CollectionUtil.isNotEmpty(prjList)){
                    prjMap = prjList.stream().collect(Collectors.toMap(GoodsPrj::getId, Function.identity()));
                }
            }
            List<Long> cateIds = esPageInfo.getList().stream().map(GoodsPrjItemEs::getCategoryId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            List<GoodsBackCategoryVo> categoryVoList = goodsBackCategoryService.queryFullParentByIds(cateIds);
            Map<Long, GoodsBackCategoryVo> categoryVoMap = Maps.newHashMap();
            if(CollectionUtil.isNotEmpty(categoryVoList)){
                categoryVoMap = categoryVoList.stream().collect(Collectors.toMap(GoodsBackCategoryVo::getId, Function.identity(), (k1, k2) -> k1));
            }
            for (GoodsPrjItemEs item : esPageInfo.getList()) {
                GoodsPrj prj = prjMap.get(item.getPrjId());
                if(prj != null){
                    item.setPrjName(prj.getPrjName());
                }
                item.setCategoryName(CategoryUtil.getItemCategoryNames(categoryVoMap,item.getCategoryId()));
            }

            rspData.setRows(esPageInfo.getList());
            rspData.setTotal(esPageInfo.getTotal());
        }else{
            rspData.setRows(Lists.newArrayList());
            rspData.setTotal(0);
        }
        return rspData;
    }

    private LambdaEsQueryWrapper<GoodsPrjItemEs> packQuery(GoodsPrjItemBo bo) {
        LambdaEsQueryWrapper<GoodsPrjItemEs> lqw = new LambdaEsQueryWrapper<>();
        lqw.eq(bo.getPrjId() != null,GoodsPrjItemEs::getPrjId, bo.getPrjId());
        lqw.match(StringUtils.isNotEmpty(bo.getItemName()), GoodsPrjItemEs::getItemName, bo.getItemName());
        lqw.like(StringUtils.isNotEmpty(bo.getUniversalName()), GoodsPrjItemEs::getUniversalName, bo.getUniversalName());
        lqw.like(StringUtils.isNotEmpty(bo.getTitleName()), GoodsPrjItemEs::getTitleName, bo.getTitleName());
        lqw.eq(bo.getItemId() != null, GoodsPrjItemEs::getItemId, bo.getItemId());
        lqw.eq(bo.getVendorId() != null, GoodsPrjItemEs::getVendorId, bo.getVendorId());
        lqw.eq(bo.getCategoryId() != null, GoodsPrjItemEs::getCategoryId, bo.getCategoryId());
        lqw.eq(bo.getBrandId() != null, GoodsPrjItemEs::getBrandId, bo.getBrandId());
        lqw.like(StringUtils.isNotEmpty(bo.getItemCode()), GoodsPrjItemEs::getItemCode, bo.getItemCode());
        lqw.eq(StringUtils.isNotEmpty(bo.getItemType()), GoodsPrjItemEs::getItemType, bo.getItemType());
        lqw.eq(StringUtils.isNotEmpty(bo.getCanGroup()), GoodsPrjItemEs::getCanGroup, bo.getCanGroup());
        lqw.match(StringUtils.isNotEmpty(bo.getKeyword()), GoodsPrjItemEs::getKeyword, bo.getKeyword());
        lqw.eq(StringUtils.isNotEmpty(bo.getUnit()), GoodsPrjItemEs::getUnit, bo.getUnit());
        lqw.eq(StringUtils.isNotEmpty(bo.getTaxCode()), GoodsPrjItemEs::getTaxCode, bo.getTaxCode());
        lqw.eq(StringUtils.isNotEmpty(bo.getDataType()), GoodsPrjItemEs::getDataType, bo.getDataType());
        lqw.eq(StringUtils.isNotEmpty(bo.getItemColor()), GoodsPrjItemEs::getItemColor, bo.getItemColor());
        lqw.eq(bo.getPushStatus() != null, GoodsPrjItemEs::getPushStatus, bo.getPushStatus());
        lqw.eq(StringUtils.isNotEmpty(bo.getSupportReturn()), GoodsPrjItemEs::getSupportReturn, bo.getSupportReturn());
        lqw.eq(StringUtils.isNotEmpty(bo.getStatus()), GoodsPrjItemEs::getStatus, bo.getStatus());
        lqw.eq(bo.getSkuId() != null, GoodsPrjItemEs::getSkuId, bo.getSkuId());
        lqw.like(StringUtils.isNotEmpty(bo.getSkuCode()), GoodsPrjItemEs::getSkuCode, bo.getSkuCode());
        lqw.like(StringUtils.isNotEmpty(bo.getBarcode()), GoodsPrjItemEs::getBarcode, bo.getBarcode());
        lqw.like(StringUtils.isNotEmpty(bo.getCommunityModel()), GoodsPrjItemEs::getCommunityModel, bo.getCommunityModel());
        lqw.in(CollectionUtil.isNotEmpty(bo.getPushStatusList()),GoodsPrjItemEs::getPushStatus, bo.getPushStatusList());
        lqw.in(GoodsPrjItemEs::getStatus, CollectionUtil.isEmpty(bo.getStatusList()) ? Arrays.asList("1","-1") : bo.getStatusList());
        lqw.gt(bo.getCreateTimeStart() != null, GoodsPrjItemEs::getCreateTime, bo.getCreateTimeStart());
        lqw.lt(bo.getCreateTimeEnd() != null, GoodsPrjItemEs::getCreateTime, bo.getCreateTimeEnd());
        lqw.gt(bo.getUpdateTimeStart() != null, GoodsPrjItemEs::getUpdateTime, bo.getUpdateTimeStart());
        lqw.lt(bo.getUpdateTimeEnd() != null, GoodsPrjItemEs::getUpdateTime, bo.getUpdateTimeEnd());
        if(StringUtils.isNotEmpty(bo.getItemIds())){
            lqw.in(GoodsPrjItemEs::getItemId, Arrays.stream(bo.getItemIds().split(",")).map(String::trim).collect(Collectors.toList()));
        }
        if(StringUtils.isNotEmpty(bo.getSkuIds())){
            lqw.in(GoodsPrjItemEs::getSkuId, Arrays.stream(bo.getSkuIds().split(",")).map(String::trim).collect(Collectors.toList()));
        }
        if(StringUtils.isNotEmpty(bo.getCategoryIds())){
            List<Long> cateIds = goodsBackCategoryService.
                    queryFullChildrenByIds(Arrays.stream(bo.getCategoryIds().split(",")).map(Long::parseLong).collect(Collectors.toList()));
            lqw.in(CollectionUtil.isNotEmpty(cateIds), GoodsPrjItemEs::getCategoryId, cateIds);
        }
        lqw.in(CollectionUtil.isNotEmpty(bo.getPrjIds()),GoodsPrjItemEs::getPrjId,  bo.getPrjIds());
        lqw.orderByDesc(GoodsPrjItemEs::getUpdateTime,GoodsPrjItemEs::getId);
        return lqw;
    }
}
