<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.oms.mapper.OrderExpressCompanyOutMapper">

    <resultMap type="com.oms.domain.OrderExpressCompanyOut" id="OrderExpressCompanyOutResult">
        <result property="id" column="id"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="expressCompanyId" column="express_company_id"/>
        <result property="expressCompanyName" column="express_company_name"/>
        <result property="expressCompanyCode" column="express_company_code"/>
        <result property="outCompanyName" column="out_company_name"/>
        <result property="outCompanyCode" column="out_company_code"/>
        <result property="sts" column="sts"/>
        <result property="prjId" column="prj_id"/>
        <result property="prjName" column="prj_name"/>
        <result property="updateName" column="update_name"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="createName" column="create_name"/>
        <result property="deleted" column="deleted"/>
        <result property="deleteTime" column="delete_time"/>
        <result property="deleteBy" column="delete_by"/>
        <result property="deleteName" column="delete_name"/>
    </resultMap>


</mapper>
