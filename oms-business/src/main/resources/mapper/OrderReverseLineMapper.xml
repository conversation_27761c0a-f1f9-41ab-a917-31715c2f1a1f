<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.oms.mapper.OrderReverseLineMapper">

    <resultMap type="com.oms.domain.OrderReverseLine" id="OrderReverseLineResult">
        <result property="id" column="id"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="prjId" column="prj_id"/>
        <result property="prjName" column="prj_name"/>
        <result property="reverseOrderId" column="reverse_order_id"/>
        <result property="orderLineId" column="order_line_id"/>
        <result property="skuId" column="sku_id"/>
        <result property="skuLineId" column="sku_line_id"/>
        <result property="skuInfoJson" column="sku_info_json"/>
        <result property="quantity" column="quantity"/>
        <result property="refundAmount" column="refund_amount"/>
        <result property="actualRefundAmount" column="actual_refund_amount"/>
        <result property="bundleId" column="bundle_id"/>
        <result property="masterId" column="master_id"/>
        <result property="extraJson" column="extra_json"/>
        <result property="version" column="version"/>
        <result property="skuBrandId" column="sku_brand_id"/>
        <result property="skuBrandName" column="sku_brand_name"/>
        <result property="skuCategoryId" column="sku_category_id"/>
        <result property="skuCategoryName" column="sku_category_name"/>
        <result property="skuCategoryIdOneLevel" column="sku_category_id_one_level"/>
        <result property="skuCategoryNameOneLevel" column="sku_category_name_one_level"/>
        <result property="skuCategoryIdTwoLevel" column="sku_category_id_two_level"/>
        <result property="skuCategoryNameTwoLevel" column="sku_category_name_two_level"/>
        <result property="skuCategoryIdThreeLevel" column="sku_category_id_three_level"/>
        <result property="skuCategoryNameThreeLevel" column="sku_category_name_three_level"/>
        <result property="skuCategoryIdFourLevel" column="sku_category_id_four_level"/>
        <result property="skuCategoryNameFourLevel" column="sku_category_name_four_level"/>
        <result property="shopId" column="shop_id"/>
        <result property="shopName" column="shop_name"/>
        <result property="skuCode" column="sku_code"/>
        <result property="skuImage" column="sku_image"/>
        <result property="skuName" column="sku_name"/>
        <result property="skuAttrKey" column="sku_attr_key"/>
        <result property="skuAttrVal" column="sku_attr_val"/>
        <result property="basePrice" column="base_price"/>
        <result property="feeRate" column="fee_rate"/>
        <result property="updateName" column="update_name"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="createName" column="create_name"/>
        <result property="deleted" column="deleted"/>
        <result property="deleteTime" column="delete_time"/>
        <result property="deleteBy" column="delete_by"/>
        <result property="deleteName" column="delete_name"/>
    </resultMap>


</mapper>
