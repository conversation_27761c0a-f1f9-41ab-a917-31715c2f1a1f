<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.oms.mapper.GoodsPrjOutAttributeMapper">

    <resultMap type="com.oms.domain.GoodsPrjOutAttribute" id="GoodsPrjOutAttributeResult">
        <result property="id" column="id"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="prjId" column="prj_id"/>
        <result property="outCategoryId" column="out_category_id"/>
        <result property="outAttributeId" column="out_attribute_id"/>
        <result property="outAttributeName" column="out_attribute_name"/>
        <result property="outAttributeEnName" column="out_attribute_en_name"/>
        <result property="canRequired" column="can_required"/>
        <result property="canCustomizable" column="can_customizable"/>
        <result property="valueType" column="value_type"/>
        <result property="status" column="status"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateName" column="update_name"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="createName" column="create_name"/>
        <result property="deleted" column="deleted"/>
        <result property="deleteTime" column="delete_time"/>
        <result property="deleteBy" column="delete_by"/>
        <result property="deleteName" column="delete_name"/>
    </resultMap>


</mapper>
