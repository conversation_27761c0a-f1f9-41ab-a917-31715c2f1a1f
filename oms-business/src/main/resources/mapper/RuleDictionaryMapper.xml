<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.oms.mapper.RuleDictionaryMapper">

    <resultMap type="com.oms.domain.RuleDictionary" id="RuleDictionaryResult">
        <result property="id" column="id"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="centerCode" column="center_code"/>
        <result property="dicType" column="dic_type"/>
        <result property="valueType" column="value_type"/>
        <result property="dicName" column="dic_name"/>
        <result property="dicVal" column="dic_val"/>
        <result property="sort" column="sort"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="createName" column="create_name"/>
        <result property="updateName" column="update_name"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="deleted" column="deleted"/>
        <result property="deleteTime" column="delete_time"/>
        <result property="deleteBy" column="delete_by"/>
        <result property="deleteName" column="delete_name"/>
        <result property="status" column="status"/>
    </resultMap>


</mapper>
