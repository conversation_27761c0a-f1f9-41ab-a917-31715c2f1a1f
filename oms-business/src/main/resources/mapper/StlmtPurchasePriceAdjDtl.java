package com.oms.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.oms.common.core.domain.BaseEntity;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 结算-采购调价明细对象 stlmt_purchase_price_adj_dtl
 *
 * <AUTHOR>
 * @date 2025-01-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("stlmt_purchase_price_adj_dtl")
public class StlmtPurchasePriceAdjDtl extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 主建
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 租户
     */
    private Long tenantId;
    /**
     * 项目ID
     */
    private Long projectId;
    /**
     * 项目名称
     */
    private String projectName;
    /**
     * 采购调价ID
     */
    private Long purchasePriceAdjId;
    /**
     * OMS采购订单ID
     */
    private Long purchaseOrderId;
    /**
     * OMS采购订单明细ID
     */
    private Long purchaseOrderLineId;
    /**
     * 外部订单
     */
    private String outId;
    /**
     * 销售订单
     */
    private Long sellerOrderId;
    /**
     * 结算采购单据ID
     */
    private Long stlmtPurchaseOrderId;
    /**
     * 结算采购单据明细ID
     */
    private Long stlmtPurchaseOrderLineId;
    /**
     * 采购时间
     */
    private Date purchaseTime;
    /**
     * 收货时间
     */
    private Date arrivalDate;
    /**
     * 供应商ID
     */
    private Long vendorId;
    /**
     * 供应商名称
     */
    private String vendorName;
    /**
     * 订单类型;0:服务商 1:自营
     */
    private Long orderType;
    /**
     * 采购类型;1:采购 2:采退
     */
    private Long purchaseType;
    /**
     * 单据类型;1:采购订单 2：采退订单 3：成本调价
     */
    private Long documentType;
    /**
     * 状态
     */
    private String status;
    /**
     * 商品id
     */
    private Long itemId;
    /**
     * 商品名称
     */
    private String itemName;
    /**
     * skuId
     */
    private Long skuId;
    /**
     * 商品名称
     */
    private String skuName;
    /**
     * 计价单位
     */
    private String pricingUnit;
    /**
     * 税率
     */
    private BigDecimal taxRate;
    /**
     * 采购数量
     */
    private BigDecimal purchaseQuantity;
    /**
     * 原含税箱价
     */
    private BigDecimal cpriceFrom;
    /**
     * 新含税箱价
     */
    private BigDecimal cpriceTo;
    /**
     * 原采购金额
     */
    private BigDecimal purchaseAmountFrom;
    /**
     * 新采购金额
     */
    private BigDecimal purchaseAmountTo;
    /**
     * 三方系统状态;0.不可同步 1.待外部新建 2.待外部修改 51.待提交 52.待一审 53.待二审 54.待确认 55.已确认 56.已驳回 99.已删除 -1外部同步失败
     */
    private Long remoteStatus;
    /**
     * 反馈原因
     */
    private String busiRemarks;
    /**
     * 包裹单号
     */
    private Long packageOrderId;
    /**
     * 创建人名
     */
    private String createName;
    /**
     * 更新人名
     */
    private String updateName;
    /**
     * 逻辑删除 (1-已删除，0-未删除)
     */
    private Integer deleted;
    /**
     * 删除时间
     */
    private Date deleteTime;
    /**
     * 删除人
     */
    private Long deleteBy;
    /**
     * 删除人名
     */
    private String deleteName;

}
