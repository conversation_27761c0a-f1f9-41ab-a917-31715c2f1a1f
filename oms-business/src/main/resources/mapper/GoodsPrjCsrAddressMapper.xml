<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.oms.mapper.GoodsPrjCsrAddressMapper">

    <resultMap type="com.oms.domain.GoodsPrjCsrAddress" id="GoodsPrjCsrAddressResult">
        <result property="id" column="id"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="prjId" column="prj_id"/>
        <result property="csrId" column="csr_id"/>
        <result property="provinceId" column="province_id"/>
        <result property="cityId" column="city_id"/>
        <result property="regionId" column="region_id"/>
        <result property="streetId" column="street_id"/>
        <result property="address" column="address"/>
        <result property="postalCode" column="postal_code"/>
        <result property="addressType" column="address_type"/>
        <result property="fixedPhone" column="fixed_phone"/>
        <result property="phone" column="phone"/>
        <result property="fax" column="fax"/>
        <result property="email" column="email"/>
        <result property="userName" column="user_name"/>
        <result property="remark" column="remark"/>
        <result property="status" column="status"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateName" column="update_name"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="createName" column="create_name"/>
        <result property="deleted" column="deleted"/>
        <result property="deleteTime" column="delete_time"/>
        <result property="deleteBy" column="delete_by"/>
        <result property="deleteName" column="delete_name"/>
    </resultMap>


</mapper>
