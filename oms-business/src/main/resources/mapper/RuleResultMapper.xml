<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.oms.mapper.RuleResultMapper">

    <resultMap type="com.oms.domain.RuleResult" id="RuleResultResult">
        <result property="id" column="id"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="ruleLineId" column="rule_line_id"/>
        <result property="ruleFieldId" column="rule_field_id"/>
        <result property="returnType" column="return_type"/>
        <result property="returnVal" column="return_val"/>
        <result property="isFixed" column="is_fixed"/>
        <result property="ruleTypeId" column="rule_type_id"/>
        <result property="dimCode" column="dim_code"/>
        <result property="returnKey" column="return_key"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="createName" column="create_name"/>
        <result property="updateName" column="update_name"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="deleted" column="deleted"/>
        <result property="deleteTime" column="delete_time"/>
        <result property="deleteBy" column="delete_by"/>
        <result property="deleteName" column="delete_name"/>
    </resultMap>


</mapper>
