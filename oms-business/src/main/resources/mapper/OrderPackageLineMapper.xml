<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.oms.mapper.OrderPackageLineMapper">

    <resultMap type="com.oms.domain.OrderPackageLine" id="OrderPackageLineResult">
        <result property="id" column="id"/>
        <result property="prjId" column="prj_id"/>
        <result property="prjName" column="prj_name"/>
        <result property="packageOrderId" column="package_order_id"/>
        <result property="orderId" column="order_id"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="outId" column="out_id"/>
        <result property="orderLineId" column="order_line_id"/>
        <result property="skuId" column="sku_id"/>
        <result property="quantity" column="quantity"/>
        <result property="status" column="status"/>
        <result property="confirmAt" column="confirm_at"/>
        <result property="tag" column="tag"/>
        <result property="extraJson" column="extra_json"/>
        <result property="version" column="version"/>
        <result property="shopId" column="shop_id"/>
        <result property="buyerId" column="buyer_id"/>
        <result property="isync" column="isync"/>
        <result property="sellerOrderId" column="seller_order_id"/>
        <result property="updateName" column="update_name"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="createName" column="create_name"/>
        <result property="deleted" column="deleted"/>
        <result property="deleteTime" column="delete_time"/>
        <result property="deleteBy" column="delete_by"/>
        <result property="deleteName" column="delete_name"/>
    </resultMap>


</mapper>
