<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.oms.mapper.RuleLineMapper">

    <resultMap type="com.oms.domain.RuleLine" id="RuleLineResult">
        <result property="id" column="id"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="ruleTypeId" column="rule_type_id"/>
        <result property="ruleTypeCode" column="rule_type_code"/>
        <result property="ruleId" column="rule_id"/>
        <result property="ruleHeadCode" column="rule_head_code"/>
        <result property="ruleCode" column="rule_code"/>
        <result property="ruleName" column="rule_name"/>
        <result property="ruleStatus" column="rule_status"/>
        <result property="priority" column="priority"/>
        <result property="validityStartAt" column="validity_start_at"/>
        <result property="validityEndAt" column="validity_end_at"/>
        <result property="isIndefinite" column="is_indefinite"/>
        <result property="overall" column="overall"/>
        <result property="remark" column="remark"/>
        <result property="tagType" column="tag_type"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="createName" column="create_name"/>
        <result property="updateName" column="update_name"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="deleted" column="deleted"/>
        <result property="deleteTime" column="delete_time"/>
        <result property="deleteBy" column="delete_by"/>
        <result property="deleteName" column="delete_name"/>
    </resultMap>


</mapper>
