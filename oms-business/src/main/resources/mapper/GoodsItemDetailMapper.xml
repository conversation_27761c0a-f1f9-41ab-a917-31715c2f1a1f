<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.oms.mapper.GoodsItemDetailMapper">

    <resultMap type="com.oms.domain.GoodsItemDetail" id="GoodsItemDetailResult">
        <result property="id" column="id"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="itemId" column="item_id"/>
        <result property="imageJson" column="image_json"/>
        <result property="pcDetail" column="pc_detail"/>
        <result property="wapDetail" column="wap_detail"/>
        <result property="status" column="status"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateName" column="update_name"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="createName" column="create_name"/>
        <result property="deleted" column="deleted"/>
        <result property="deleteTime" column="delete_time"/>
        <result property="deleteBy" column="delete_by"/>
        <result property="deleteName" column="delete_name"/>
    </resultMap>


</mapper>
