<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.oms.mapper.TendersInfoMapper">

    <resultMap type="com.oms.domain.TendersInfo" id="TendersInfoResult">
        <result property="id" column="id"/>
        <result property="cursorMark" column="cursor_mark"/>
        <result property="infoId" column="info_id"/>
        <result property="infoTitle" column="info_title"/>
        <result property="infoContent" column="info_content"/>
        <result property="status" column="status"/>
        <result property="infoType" column="info_type"/>
        <result property="infoTypeSegment" column="info_type_segment"/>
        <result property="infoPublishTime" column="info_publish_time"/>
        <result property="areaProvince" column="area_province"/>
        <result property="areaCity" column="area_city"/>
        <result property="areaCountry" column="area_country"/>
        <result property="tendersNumber" column="tenders_number"/>
        <result property="bidingAcquireTime" column="biding_acquire_time"/>
        <result property="bidingEndTime" column="biding_end_time"/>
        <result property="tenderBeginTime" column="tender_begin_time"/>
        <result property="tenderEndTime" column="tender_end_time"/>
        <result property="openBidingTime" column="open_biding_time"/>
        <result property="biddingType" column="bidding_type"/>
        <result property="isElectronic" column="is_electronic"/>
        <result property="tenderCompany" column="tender_company"/>
        <result property="tenderRelationName" column="tender_relation_name"/>
        <result property="tenderRelationWay" column="tender_relation_way"/>
        <result property="winBiddingUnit" column="win_bidding_unit"/>
        <result property="winBiddingRelationName" column="win_bidding_relation_name"/>
        <result property="winBiddingRelationWay" column="win_bidding_relation_way"/>
        <result property="bidWinnerAmount" column="bid_winner_amount"/>
        <result property="bidBudget" column="bid_budget"/>
        <result property="fundsSource" column="funds_source"/>
        <result property="bidEvaluationExpert" column="bid_evaluation_expert"/>
        <result property="fileList" column="file_list"/>
        <result property="belongingArea" column="belonging_area"/>
        <result property="materialCategory" column="material_category"/>
        <result property="supportDepartment" column="support_department"/>
        <result property="projectLevel" column="project_level"/>
        <result property="createBy" column="create_by"/>
        <result property="createName" column="create_name"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateName" column="update_name"/>
        <result property="updateTime" column="update_time"/>
        <result property="deleted" column="deleted"/>
        <result property="deleteTime" column="delete_time"/>
        <result property="deleteBy" column="delete_by"/>
        <result property="deleteName" column="delete_name"/>
    </resultMap>


</mapper>
