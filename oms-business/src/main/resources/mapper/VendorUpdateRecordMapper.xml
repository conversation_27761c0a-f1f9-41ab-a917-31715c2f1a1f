<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.oms.mapper.VendorUpdateRecordMapper">

    <resultMap type="com.oms.domain.VendorUpdateRecord" id="VendorUpdateRecordResult">
        <result property="id" column="id"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="vendorId" column="vendor_id"/>
        <result property="name" column="name"/>
        <result property="vendorType" column="vendor_type"/>
        <result property="type" column="type"/>
        <result property="phone" column="phone"/>
        <result property="extraJson" column="extra_json"/>
        <result property="auditStatus" column="audit_status"/>
        <result property="auditDate" column="audit_date"/>
        <result property="auditMsg" column="audit_msg"/>
        <result property="auditById" column="audit_by_id"/>
        <result property="auditByName" column="audit_by_name"/>
        <result property="createBy" column="create_by"/>
        <result property="createName" column="create_name"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateName" column="update_name"/>
        <result property="updateTime" column="update_time"/>
        <result property="deleted" column="deleted"/>
        <result property="deleteTime" column="delete_time"/>
        <result property="deleteBy" column="delete_by"/>
        <result property="deleteName" column="delete_name"/>
    </resultMap>


</mapper>
