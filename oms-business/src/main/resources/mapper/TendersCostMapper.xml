<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.oms.mapper.TendersCostMapper">

    <resultMap type="com.oms.domain.TendersCost" id="TendersCostResult">
        <result property="id" column="id"/>
        <result property="infoId" column="info_id"/>
        <result property="infoTitle" column="info_title"/>
        <result property="costType" column="cost_type"/>
        <result property="payAmount" column="pay_amount"/>
        <result property="payAccountName" column="pay_account_name"/>
        <result property="payBank" column="pay_bank"/>
        <result property="payAccount" column="pay_account"/>
        <result property="payRemark" column="pay_remark"/>
        <result property="payDeadline" column="pay_deadline"/>
        <result property="payCert" column="pay_cert"/>
        <result property="costReturnDate" column="cost_return_date"/>
        <result property="invoiceDate" column="invoice_date"/>
        <result property="status" column="status"/>
        <result property="createBy" column="create_by"/>
        <result property="createName" column="create_name"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateName" column="update_name"/>
        <result property="updateTime" column="update_time"/>
        <result property="deleted" column="deleted"/>
        <result property="deleteTime" column="delete_time"/>
        <result property="deleteBy" column="delete_by"/>
        <result property="deleteName" column="delete_name"/>
    </resultMap>


</mapper>
