package com.oms.common.interceptor;

import cn.dev33.satoken.annotation.SaIgnore;
import com.oms.common.core.domain.BaseEntity;
import com.oms.common.core.domain.BaseEntityBo;
import com.oms.common.helper.LoginHelper;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;


/**
 * 供应商参数拦截器
 */
@Aspect
@Component
public class VendorParamsHandelInterceptor {

	@Resource
	protected HttpServletRequest request;
	@Resource
	protected HttpServletResponse response;

	//新版本的Controller
	@Pointcut("execution(* com.oms.controller.vendor..*.*(..))")
	public void roleC() {
	}

	@Around("roleC()")
	public Object aroundC(ProceedingJoinPoint pjp) throws Throwable {
		return checkInner(pjp);
	}

	//#################################################################################

	private Object checkInner(ProceedingJoinPoint pjp) throws Throwable {
		boolean bret1 = ((MethodSignature) pjp.getSignature()).getMethod().isAnnotationPresent(SaIgnore.class);
		Object[] args = pjp.getArgs();
		if (!bret1) {
			if (args.length > 0) {
				for (Object obj : args) {
					if (obj instanceof BaseEntity) {
						BaseEntity baseEntityBo = (BaseEntity) obj;
						baseEntityBo.setTenantId(LoginHelper.getTenantId());
//						baseEntityBo.setVendorId(LoginHelper.getVendorId());
//						baseEntityBo.setVendorName(LoginHelper.getVendorName());
					}
					if (obj instanceof BaseEntityBo) {
						BaseEntityBo baseEntityBo = (BaseEntityBo) obj;
						baseEntityBo.setTenantId(LoginHelper.getTenantId());
					}
				}
			}
		}
		Object	proceed = pjp.proceed(args);
		return proceed;
	}

}
