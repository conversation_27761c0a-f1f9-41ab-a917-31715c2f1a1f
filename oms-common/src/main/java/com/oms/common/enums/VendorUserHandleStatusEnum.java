package com.oms.common.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 供应商账号状态表-处理状态
 */
public enum VendorUserHandleStatusEnum {
	WAIT(0, "待处理"),
	COMPLETE(1, "已处理"),

	;
	@Getter
	private Integer code;

	@Getter
	private String desc;

	VendorUserHandleStatusEnum(Integer code, String desc) {
		this.code = code;
		this.desc = desc;
	}

	public static Integer getCode(String desc) {
		VendorUserHandleStatusEnum[] codes = values();
		for (VendorUserHandleStatusEnum vendorUserStatusApplyTypeEnum : codes) {
			if(vendorUserStatusApplyTypeEnum.getDesc().equals(desc)){
				return vendorUserStatusApplyTypeEnum.getCode();
			}
		}
		return null;
	}

	public static String getDesc(Integer code){
		VendorUserHandleStatusEnum[] codes = values();
		for (VendorUserHandleStatusEnum vendorUserStatusApplyTypeEnum : codes) {
			if(vendorUserStatusApplyTypeEnum.getCode().equals(code)){
				return vendorUserStatusApplyTypeEnum.getDesc();
			}
		}
		return null;
	}

	public static List<Integer> getCodeList() {
		return Arrays.asList(values()).stream().map(VendorUserHandleStatusEnum::getCode).collect(Collectors.toList()) ;
	}

}
