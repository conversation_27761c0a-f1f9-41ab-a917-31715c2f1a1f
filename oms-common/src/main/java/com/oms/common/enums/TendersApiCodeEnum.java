package com.oms.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;


/**
 * 投标token 接口静态参数
 */
@Getter
@AllArgsConstructor
public enum TendersApiCodeEnum {

    SERVICE_ERROR("-1", "系统繁忙，服务暂不可用"),
    SUCCESS("0", "成功"),
    PARAM_ERROR("10001", "参数错误"),
    AUTH_ERROR("20001", "认证失败，接收到的鉴权信息不正确"),
    AUTH_ACCOUNT_ERROR("20002", "认证失败，账户不可用"),
    AUTH_DATA_ERROR("20003", "认证失败，没有此数据的访问权限"),
    AUTH_INTERFACE_ERROR("20005", "认证失败，没有此接口的访问权限"),
    AUTH_FREQUENCY_ERROR("30001", "接口限制，获取数据接口频率超出限制"),
    AUTH_FREQUENCY_ACCOUNT_ERROR("30002", "接口限制，调用次数超出账号总量限制"),
    AUTH_FREQUENCY_YEAR_ERROR("30003", "接口限制，接口调用次数超出当年限制"),
    AUTH_FREQUENCY_DAY_ERROR("30004", "接口限制，接口调用次数超出当天限制"),
    NO_DATA("40001", "获取附件失败，请稍后重试"),
    NO_ATTACHMENT("40002", "附件链接已失效，无法提供下载服务"),
    NO_PROJECT("110001", "前置商机接口:项目已失效，无法查询相关信息"),

    ;

    private final String value;
    private final String description;



    public static TendersApiCodeEnum fromValue(String value) {
        for (TendersApiCodeEnum tendersApiCodeEnum: TendersApiCodeEnum.values()) {
            if (tendersApiCodeEnum.value.equals(value)) {
                return tendersApiCodeEnum;
            }
        }
        return null;
    }

}
