package com.oms.common.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 合同来源类型枚举类
 * <AUTHOR> @date 
 */
public enum VendorContractSourceTypeEnum {

    VENDOR_REGISTER(1, "供应商入驻签署"),
    OMS(2, "OMS"),
    ;
    @Getter
    private Integer code;

    @Getter
    private String desc;

    VendorContractSourceTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static Integer getCode(String desc) {
        VendorContractSourceTypeEnum[] codes = values();
        for (VendorContractSourceTypeEnum yesNoEnum : codes) {
            if(yesNoEnum.getDesc().equals(desc)){
                return yesNoEnum.getCode();
            }
        }
        return null;
    }

    public static String getDesc(Integer code){
        VendorContractSourceTypeEnum[] codes = values();
        for (VendorContractSourceTypeEnum yesNoEnum : codes) {
            if(yesNoEnum.getCode().equals(code)){
                return yesNoEnum.getDesc();
            }
        }
        return null;
    }

    public static List<Integer> getCodeList() {
        return Arrays.asList(values()).stream().map(VendorContractSourceTypeEnum::getCode).collect(Collectors.toList()) ;
    }
}
