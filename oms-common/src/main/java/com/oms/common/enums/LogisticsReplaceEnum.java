package com.oms.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.Optional;

@AllArgsConstructor
@Getter
public enum LogisticsReplaceEnum {
    PLACE_ORDER_NAME("variable_placeOrderName","签收人"),
    PURPOSE_CITY("variable_purposeCity","目的城市"),
    DRIVER("variable_driver","司机"),
    MOBILE("variable_mobile","电话"),
    PLATE_NUMBER("variable_plateNumber","车牌号"),
    STOREHOUSE_NAME("variable_storehouseName","仓库"),
    STOREHOUSE_ADDRESS("variable_storehouseAddress","仓库地址"),
    CORP_CODE("variable_corpCode","物流编码"),
    CORP_NAME("variable_corpName","物流公司"),
    TRACKING_NUMBER("variable_trackingNumber","快递单号"),
    IPM_CITY("variable_ipmCity","发货城市"),
    ;

    private final String replaceStr;
    private final String desc;

    public static String replaceContent(String content, Map<String, String> replaceMap){
        if(StringUtils.isEmpty(content)){
            return "";
        }
        for (LogisticsReplaceEnum logisticsReplaceEnum:LogisticsReplaceEnum.values()){
            if(content.contains(logisticsReplaceEnum.getReplaceStr())){
                content = content.replaceAll(logisticsReplaceEnum.getReplaceStr(), Optional.ofNullable(replaceMap.get(logisticsReplaceEnum.getReplaceStr())).orElse("--"));
            }
        }
        return content;
    }
}
