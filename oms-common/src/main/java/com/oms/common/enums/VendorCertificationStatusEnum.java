package com.oms.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 供应商资质状态枚举
 */
@Getter
@AllArgsConstructor
public enum VendorCertificationStatusEnum {
   // 状态 0-生效中 1-过期 2-过期待供应商修改 3-待生效
    EFFECT(0, "生效中"),
    EXPIRED(1, "过期"),
    EXPIRED_WAIT_FOR_VENDOR_MODIFY(2, "过期待供应商修改"),
    WAIT_FOR_EFFECT(3, "待生效"),
    ;

    private final int value;
    private final String description;

    public static VendorCertificationStatusEnum fromValue(int value) {
        for (VendorCertificationStatusEnum auditStatus: VendorCertificationStatusEnum.values()) {
            if (auditStatus.value == value) {
                return auditStatus;
            }
        }

        return null;
    }
}
