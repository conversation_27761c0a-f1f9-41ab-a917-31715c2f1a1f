package com.oms.common.enums;

/**
 * 合同发货状态枚举
 */
public enum ShipRequestStatusEnum {
    INTERFACE_ORDER(0, "合同未提交"),
    HANDMADE_ORDERS(1, "合同待审核"),
    MALL_ORDERS(2, "合同已审核");

    private final Integer code;
    private final String info;

    ShipRequestStatusEnum(Integer code, String info) {
        this.code = code;
        this.info = info;
    }

    public Integer getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }
}
