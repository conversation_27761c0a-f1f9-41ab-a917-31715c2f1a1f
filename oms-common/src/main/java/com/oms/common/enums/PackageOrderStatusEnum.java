package com.oms.common.enums;

import lombok.Getter;


public enum PackageOrderStatusEnum {

    /**
     * 初始状态
     */
    PACKAGE_ORDER_INIT(0,"init", "初始状态"),

    /**
     * 已发货
     */
    PACKAGE_ORDER_DELIVERED(1, "delivered", "已发货"),

    /**
     * 已收货
     */
    PACKAGE_ORDER_RECEIVED(2, "received", "已收货"),

    /**
     * 拒收
     */
    PACKAGE_ORDER_CANCELLED(3, "cancelled", "拒收"),

    /**
     * 异常
     */
    PACKAGE_ORDER_ERROR(4, "error", "异常");

    /**
     * code
     */
    @Getter
    private int code;

    /**
     * 描述
     */
    private String desc;

    @Getter
    private String zhDesc;

    PackageOrderStatusEnum(int code, String desc, String zhDesc) {
        this.code = code;
        this.desc = desc;
        this.zhDesc = zhDesc;
    }

}
