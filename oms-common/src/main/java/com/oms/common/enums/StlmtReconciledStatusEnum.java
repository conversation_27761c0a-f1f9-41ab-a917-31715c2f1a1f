package com.oms.common.enums;

/**
 * 状态枚举-结算对账枚举类
 */
public enum StlmtReconciledStatusEnum {
    UNRECONCILED(1,"未对账"),
    PARTIALLY_RECONCILED(2,"部分对账"),
    RECONCILED(3,"对账完成")

    ;
    final int key;

    final String value;

    StlmtReconciledStatusEnum(int key, String value) {
        this.key = key;
        this.value = value;
    }
    public int getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }
    public static StlmtReconciledStatusEnum getByKey(int key){
        for(StlmtReconciledStatusEnum x:values()){
            if(x.key==key){
                return x;
            }
        }
        return null;
    }
    public static String getValueByKey(int key){
        for(StlmtReconciledStatusEnum x:values()){
            if(x.key==key){
                return x.value;
            }
        }
        return null;
    }
}
