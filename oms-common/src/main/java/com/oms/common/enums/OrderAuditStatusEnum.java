package com.oms.common.enums;

import lombok.Getter;


public enum OrderAuditStatusEnum {

    /**
     * 待确认
     */
    TO_CONFIRM(0, "TO_CONFIRM", "待确认"),

    /**
     * 待审核
     */
    TO_AUDIT(1, "TO_AUDIT", "待审核"),

    /**
     * 已审核
     */
    AUDITED(2, "AUDITED", "已审核"),


    /**
     * 已取消
     */
    CANCEL(3, "CANCEL", "已取消");

    /**
     * code
     */
    @Getter
    private int code;

    /**
     * 描述
     */
    private String desc;

    @Getter
    private String info;

    OrderAuditStatusEnum(int code, String desc, String info) {
        this.code = code;
        this.desc = desc;
        this.info = info;
    }

}
