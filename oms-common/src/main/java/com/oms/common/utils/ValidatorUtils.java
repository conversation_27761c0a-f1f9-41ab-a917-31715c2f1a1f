package com.oms.common.utils;

import com.oms.common.core.validate.AddGroup;
import com.oms.common.exception.ServiceException;
import com.oms.common.utils.spring.SpringUtils;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.util.CollectionUtils;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import javax.validation.Validator;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Validator 校验框架工具
 *
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ValidatorUtils {

    private static final Validator VALID = SpringUtils.getBean(Validator.class);

    public static <T> void validate(T object, Class<?>... groups) {
        Set<ConstraintViolation<T>> validate = VALID.validate(object, groups);
        if (!validate.isEmpty()) {
            throw new ConstraintViolationException("参数校验异常", validate);
        }
    }


    public static <T> void validateV2(T object, Class<?>... groups) {
        Set<ConstraintViolation<T>> validate = VALID.validate(object, groups);
        if (!validate.isEmpty()) {
            String errMsg = validate.stream().map((cv) -> {
                return cv == null ? "null" : cv.getPropertyPath() + ": " + cv.getMessage();
            }).collect(Collectors.joining(", "));
            throw new ServiceException(errMsg);
        }
    }


    /**
     * 校验对象
     *
     * @param object 待校验对象
     * @param groups 待校验的组
     */
    public static void validateEntity(Object object, Class<?>... groups) throws ServiceException {
        //基本类型不作操作
        if (object instanceof List) {
            List<Object> list = (List<Object>) object;
            int i = 0;
            List<String> errorList = new ArrayList<>();
            for (Object obj : list) {
                i++;
                try {
                    validateObject(obj, groups);
                } catch (ServiceException e) {
                    errorList.add("第" + i + "条数据：" + e.getMessage().replace("\n", "; "));
                }
            }
            if (!CollectionUtils.isEmpty(errorList)) {
                throw new ServiceException(StringUtils.join(errorList, "\n"));
            }
        } else {
            validateObject(object, groups);
        }
    }

    /**
     * 校验对象
     *
     * @param object 待校验对象
     * @param groups 待校验的组
     */
    public static void validateObject(Object object, Class<?>... groups) throws ServiceException {
        Set<ConstraintViolation<Object>> constraintViolations = VALID.validate(object, groups);
        if (!constraintViolations.isEmpty()) {
            List<String> errorList = new ArrayList<>();
            for (ConstraintViolation<Object> constraint : constraintViolations) {
                errorList.add(constraint.getPropertyPath()+constraint.getMessage());
            }
            throw new ServiceException(StringUtils.join(errorList, "\n"));
        }
    }
}
