package com.oms.common.core.page;

import cn.hutool.http.HttpStatus;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 表格分页数据对象
 *
 */

@Data
@NoArgsConstructor
public class AsyncTableDataInfo<T> extends TableDataInfo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 待处理数量
     */
    private long toDoNum;


    /**
     * 进行中数量
     */
    private long doingNum;


}
