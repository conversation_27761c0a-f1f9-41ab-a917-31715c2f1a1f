<template>
  <el-dialog
    v-model="dialogVisible"
    width="500px"
    class="pb4"
    title="详情"
    :close-on-click-modal="false"
    :before-close="onCancel"
  >
    <el-form v-loading="loading" label-width="100px" label-suffix="：">
#foreach($column in $columns)
  #set($field=$column.javaField)
  #if($column.insert && !$column.pk)
    #set($parentheseIndex=$column.columnComment.indexOf("（"))
    #if($parentheseIndex != -1)
      #set($comment=$column.columnComment.substring(0, $parentheseIndex))
    #else
      #set($comment=$column.columnComment)
    #end
    #set($dictType=$column.dictType)
    #if($column.htmlType == "input")
      <el-form-item label="${comment}" prop="${field}">{{ detailState.${field} }}</el-form-item>
    #end
  #end
#end
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="onCancel">关 闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
// import
import { get${BusinessName} } from '@/api/${moduleName}/${businessName}'

const props = defineProps({
  modelValue: {
    type: Object,
    require: true,
    default() {
      return {
        id: '',
        visible: false
      }
    }
  }
})
const emit = defineEmits(['update:modelValue'])

// data
// 页面加载状态
const loading = ref(false)
const detailState = reactive({})

// computed
const dialogVisible = computed({
  get() {
    return props.modelValue?.visible
  },
  set(val) {
    emit('update:modelValue', { visible: val })
  }
})

onMounted(() => {
  // 加载数据
  if (props.modelValue.id) {
    loadData(props.modelValue.id)
  }
})

// methods
// 取消
const onCancel = () => {
  dialogVisible.value = false
}
// 回显加载数据
const loadData = (id) => {
  loading.value = true
  get${BusinessName}(id).then((res) => {
    Object.assign(detailState, res.data || {})
    loading.value = false
  })
}
</script>