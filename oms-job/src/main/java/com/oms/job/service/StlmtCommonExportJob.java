package com.oms.job.service;

import com.oms.common.enums.AsyncTaskExecuteEnum;
import com.oms.excel.strategy.ExcelStrategyService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


/**
 * 结算任务导出统一JOB
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class StlmtCommonExportJob {


    private final ExcelStrategyService excelStrategyService;

    /**
     * 结算任务导出统一JOB
     */
    @XxlJob("stlmtCommonExportJob")
    public void stlmtCommonExportJobHandler(){
        excelStrategyService.exportExecute(AsyncTaskExecuteEnum.INVOICE_PROCESS_EXPORT.getCode());
    }
}
