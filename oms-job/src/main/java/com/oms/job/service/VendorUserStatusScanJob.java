package com.oms.job.service;

import com.oms.common.enums.AsyncTaskExecuteEnum;
import com.oms.excel.strategy.ExcelStrategyService;
import com.oms.service.IPriceGoodsService;
import com.oms.service.IVendorUserStatusService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


/**
 * 供应商账号状态扫描任务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class VendorUserStatusScanJob {

    private final IVendorUserStatusService vendorUserStatusService;

    /**
     * 处理供应商账号到期关停数据
     */
    @XxlJob("vendorUserStatusScanJob")
    public void vendorUserStatusScanJobHandle(){
        vendorUserStatusService.handleExpireShutdown();
    }


}
