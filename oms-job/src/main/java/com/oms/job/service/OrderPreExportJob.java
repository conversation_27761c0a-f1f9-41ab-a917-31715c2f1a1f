package com.oms.job.service;

import com.oms.common.enums.AsyncTaskExecuteEnum;
import com.oms.excel.strategy.ExcelStrategyService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


/**
 * 销售预占订单导出
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrderPreExportJob {


    private final ExcelStrategyService excelStrategyService;

    /**
     * 销售订单导出
     */
    @XxlJob("PreOrderExportJob")
    public void PreOrderExportJobHandler(){
        excelStrategyService.exportExecute(AsyncTaskExecuteEnum.ORDER_PRE_EXPORT.getCode());
    }
}
