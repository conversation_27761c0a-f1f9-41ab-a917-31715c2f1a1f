package com.oms.job.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.oms.common.enums.AsyncTaskExecuteEnum;
import com.oms.domain.OrderReport;
import com.oms.excel.strategy.ExcelStrategyService;
import com.oms.mapper.OrderReportMapper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Date;


/**
 * 报备单失效状态处理
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrderReportLoseEfficacyHandleJob {


    private final OrderReportMapper orderReportMapper;

    /**
     * 销售订单导出
     */
    @XxlJob("OrderReportLoseEfficacyHandleJob")
    public void OrderReportLoseEfficacyJobHandler(){
        // 计算7天前的日期 ---防止数据过多
        //Date sevenDaysAgo = new Date(System.currentTimeMillis() - 7 * 24 * 60 * 60 * 1000L);

        //查询出所有失效时间大于当前时间的报备单，将数据修改成已失效...只查最近7天 防止数据过多
        orderReportMapper.update(new LambdaUpdateWrapper<OrderReport>()
                //大于当前时间
                .lt(OrderReport::getExpireAt,new Date())
                .in(OrderReport::getReportStatus, Arrays.asList(0,1))
                .set(OrderReport::getUpdateTime,new Date())
                .set(OrderReport::getUpdateName,"JOB")
                .set(OrderReport::getReportStatus,2));
    }
}
