# 项目技术栈

本项目是一个基于 Spring Boot 的订单管理系统 (OMS)，主要技术栈如下：

## 核心框架

*   **Spring Boot**: 作为项目的核心框架，简化了 Spring 应用的开发和部署。
*   **Maven**: 项目管理和构建工具。
*   **Java 8**: 项目使用的 Java 版本。

## 数据访问

*   **Mybatis-Plus**: 简化 MyBatis 操作的增强工具，提供了许多便利功能（如分页、乐观锁、代码生成等）。
*   **Dynamic DataSource**: 多数据源支持。
*   **P6Spy**: SQL 性能分析工具。

## 数据库

*   **MySQL** (根据项目结构和常见实践推测，pom.xml中未直接列出MySQL驱动，但有SQL Server驱动，可能支持多种数据库)

## 缓存与消息队列

*   **Redis**: 使用 Redisson 作为客户端，用于缓存和分布式锁。
*   **Lock4j**: 分布式锁框架。
*   **Transmittable ThreadLocal**: 解决线程池中 ThreadLocal 传递问题。

## 安全认证

*   **Sa-Token**: 轻量级权限认证框架，支持 JWT。

## 定时任务

*   **XXL-JOB**: 分布式任务调度平台。

## 搜索

*   **Elasticsearch**: 使用 Easy-ES 客户端，用于商品等数据的搜索。

## 规则引擎

*   **QLExpress**: 强大的表达式引擎。
*   **Easy Rules**: 简单的规则引擎。

## 工具库

*   **Hutool**: Java 工具类库。
*   **OkHttp**: HTTP 客户端。
*   **Guava**: Google 的 Java 核心库。

## 文档

*   **SpringDoc OpenAPI**: 用于生成 Swagger API 文档。

## 文件处理

*   **Apache POI**: 处理 Excel 文件。
*   **EasyExcel**: 阿里巴巴的 Excel 处理工具。
*   **Velocity**: 模板引擎，可能用于代码生成或文档生成。
*   **iTextPDF / openhtmltopdf**: PDF 生成相关库。

## 其他

*   **Lombok**: 简化 Java Bean 开发。
*   **Bouncy Castle**: 加密相关库。
*   **ip2region**: 离线 IP 地址定位库。
*   **sms4j**: 短信发送框架。
*   **Spring Boot Admin**: 应用监控。

